import * as React from "react";
import type { SVGProps } from "react";
const SvgInsurance = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="currentColor"
    viewBox="0 0 32 33"
    {...props}
  >
    <path d="M16.406 15.902c-.592 0-1.077.091-1.46.211v10.48a1.427 1.427 0 0 1-2.852 0 1.426 1.426 0 0 0-2.852 0 4.28 4.28 0 0 0 4.276 4.276 4.28 4.28 0 0 0 4.277-4.276V16.087a5 5 0 0 0-1.39-.185zM14.1 2.521c-5.277.754-9.681 4.103-11.329 8.807-.475 1.359-.688 3.48-.653 4.872.01.369.298.67.667.694h.046c.35 0 .65-.253.703-.602.137-.878 1.699-1.817 3.808-1.817 1.69 0 3.002.601 3.541 1.277-.397-3.044-.644-9.36 3.217-13.23" />
    <path d="M20.316 16.495c.397-2.378 1.302-10.486-3.937-14.002-5.296 3.555-4.312 11.803-3.923 14.078a.7.7 0 0 0 .092-.176l.09-.265c.28-.814 1.765-1.655 3.768-1.655s3.489.841 3.768 1.658l.09.262c.012.036.035.067.052.1" />
    <path d="M18.7 2.565c3.847 3.905 3.566 10.253 3.166 13.258.5-.704 1.855-1.348 3.604-1.348 2.004 0 3.49.842 3.768 1.655a.713.713 0 0 0 1.387-.23c0-6.588-5.263-12.241-11.926-13.336z" />
  </svg>
);
export default SvgInsurance;
