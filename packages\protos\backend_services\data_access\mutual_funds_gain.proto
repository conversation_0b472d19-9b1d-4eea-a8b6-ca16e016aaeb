syntax = "proto3";
package backend_services.data_access;
import "database/fi_mutual_funds.proto";

service MutualFundsGains {
    rpc UpdateGainsMutualFundsAccount(UpdateGainsMutualFundsAccountRequest) returns (UpdateGainsMutualFundsAccountResponse) {};
	rpc GetGainsMutualFundsAccount(GetGainsMutualFundsAccountRequest) returns (GetGainsMutualFundsAccountResponse) {};
	rpc GetMutualFundsUnrealizedGains(GetMutualFundsUnrealizedGainsRequest) returns (GetMutualFundsUnrealizedGainsResponse) {};
	rpc SaveMutualFundsGains(SaveMutualFundsGainsRequest) returns (SaveMutualFundsGainsResponse) {};
}


message UpdateGainsMutualFundsAccountRequest{
	repeated database.MutualFundsGain records = 1;
}
message UpdateGainsMutualFundsAccountResponse{}

message GetGainsMutualFundsAccountRequest{
	string account_id = 1;
	bool unrealized = 2;
    int64 timestamp_after = 3;
    int64 timestamp_before = 4;
}
message GetGainsMutualFundsAccountResponse{
	repeated database.MutualFundsGain records = 1;
}

message GetMutualFundsUnrealizedGainsRequest {
	repeated string account_ids = 1;
}

message GetMutualFundsUnrealizedGainsResponse {
	repeated database.MutualFundsGain records = 1;
}

message SaveMutualFundsGainsRequest {
	string account_id = 1;
	int64 from = 2;
	repeated database.MutualFundsGain gains = 3;
}

message SaveMutualFundsGainsResponse {
}