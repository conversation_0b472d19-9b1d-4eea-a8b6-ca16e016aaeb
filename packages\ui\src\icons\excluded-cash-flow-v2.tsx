import * as React from "react";
import type { SVGProps } from "react";
const SvgExcludedCashFlowV2 = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="currentColor"
    viewBox="0 0 20 16"
    {...props}
  >
    <path d="M9.43 3.471a4.524 4.524 0 0 0-3.772 5.477 4.52 4.52 0 0 0 2.16 2.92h-2.58a6.235 6.235 0 0 1 .293-8.16 6.23 6.23 0 0 1 3.9-1.945V3.48zm.717 9H2.808v-.803a.359.359 0 0 0-.597-.269L.307 13.065a.36.36 0 0 0 0 .543l1.904 1.665a.36.36 0 0 0 .597-.27v-.824h7.339a6.23 6.23 0 1 0-.055-12.46h-.064v1.709h.064a4.52 4.52 0 1 1 .046 9.042zM12.92 1.714h4.455v-.8a.36.36 0 0 1 .597-.27l1.904 1.665a.36.36 0 0 1 0 .542l-1.904 1.665a.358.358 0 0 1-.597-.268v-.82h-2.142a6.8 6.8 0 0 0-2.313-1.71z" />
  </svg>
);
export default SvgExcludedCashFlowV2;
