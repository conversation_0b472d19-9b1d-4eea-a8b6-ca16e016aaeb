// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.2
//   protoc               v6.31.1
// source: backend_services/visualization/shared.proto

/* eslint-disable */
import { Binary<PERSON>eader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "backend_services.visualization";

export enum FlowType {
  Expense = 0,
  Income = 1,
  Investment = 2,
  UNRECOGNIZED = -1,
}

export function flowTypeFromJSON(object: any): FlowType {
  switch (object) {
    case 0:
    case "Expense":
      return FlowType.Expense;
    case 1:
    case "Income":
      return FlowType.Income;
    case 2:
    case "Investment":
      return FlowType.Investment;
    case -1:
    case "UNRECOGNIZED":
    default:
      return FlowType.UNRECOGNIZED;
  }
}

export function flowTypeToJSON(object: FlowType): string {
  switch (object) {
    case FlowType.Expense:
      return "Expense";
    case FlowType.Income:
      return "Income";
    case FlowType.Investment:
      return "Investment";
    case FlowType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum TimeStep {
  Daily = 0,
  Weekly = 1,
  Monthly = 2,
  Yearly = 3,
  UNRECOGNIZED = -1,
}

export function timeStepFromJSON(object: any): TimeStep {
  switch (object) {
    case 0:
    case "Daily":
      return TimeStep.Daily;
    case 1:
    case "Weekly":
      return TimeStep.Weekly;
    case 2:
    case "Monthly":
      return TimeStep.Monthly;
    case 3:
    case "Yearly":
      return TimeStep.Yearly;
    case -1:
    case "UNRECOGNIZED":
    default:
      return TimeStep.UNRECOGNIZED;
  }
}

export function timeStepToJSON(object: TimeStep): string {
  switch (object) {
    case TimeStep.Daily:
      return "Daily";
    case TimeStep.Weekly:
      return "Weekly";
    case TimeStep.Monthly:
      return "Monthly";
    case TimeStep.Yearly:
      return "Yearly";
    case TimeStep.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface OverallCashflow {
  amount: number;
  flowType: FlowType;
}

export interface CategoryCashflow {
  amount: number;
  percentage: number;
  categoryCollection: string;
  categoryId: string;
  flowType: FlowType;
}

function createBaseOverallCashflow(): OverallCashflow {
  return { amount: 0, flowType: 0 };
}

export const OverallCashflow: MessageFns<OverallCashflow> = {
  encode(message: OverallCashflow, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.amount !== 0) {
      writer.uint32(9).double(message.amount);
    }
    if (message.flowType !== 0) {
      writer.uint32(16).int32(message.flowType);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): OverallCashflow {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseOverallCashflow();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.amount = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.flowType = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): OverallCashflow {
    return {
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      flowType: isSet(object.flowType) ? flowTypeFromJSON(object.flowType) : 0,
    };
  },

  toJSON(message: OverallCashflow): unknown {
    const obj: any = {};
    if (message.amount !== 0) {
      obj.amount = message.amount;
    }
    if (message.flowType !== 0) {
      obj.flowType = flowTypeToJSON(message.flowType);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<OverallCashflow>, I>>(base?: I): OverallCashflow {
    return OverallCashflow.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OverallCashflow>, I>>(object: I): OverallCashflow {
    const message = createBaseOverallCashflow();
    message.amount = object.amount ?? 0;
    message.flowType = object.flowType ?? 0;
    return message;
  },
};

function createBaseCategoryCashflow(): CategoryCashflow {
  return { amount: 0, percentage: 0, categoryCollection: "", categoryId: "", flowType: 0 };
}

export const CategoryCashflow: MessageFns<CategoryCashflow> = {
  encode(message: CategoryCashflow, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.amount !== 0) {
      writer.uint32(9).double(message.amount);
    }
    if (message.percentage !== 0) {
      writer.uint32(17).double(message.percentage);
    }
    if (message.categoryCollection !== "") {
      writer.uint32(26).string(message.categoryCollection);
    }
    if (message.categoryId !== "") {
      writer.uint32(34).string(message.categoryId);
    }
    if (message.flowType !== 0) {
      writer.uint32(40).int32(message.flowType);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CategoryCashflow {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCategoryCashflow();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.amount = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.percentage = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.categoryCollection = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.categoryId = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.flowType = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CategoryCashflow {
    return {
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      percentage: isSet(object.percentage) ? globalThis.Number(object.percentage) : 0,
      categoryCollection: isSet(object.categoryCollection) ? globalThis.String(object.categoryCollection) : "",
      categoryId: isSet(object.categoryId) ? globalThis.String(object.categoryId) : "",
      flowType: isSet(object.flowType) ? flowTypeFromJSON(object.flowType) : 0,
    };
  },

  toJSON(message: CategoryCashflow): unknown {
    const obj: any = {};
    if (message.amount !== 0) {
      obj.amount = message.amount;
    }
    if (message.percentage !== 0) {
      obj.percentage = message.percentage;
    }
    if (message.categoryCollection !== "") {
      obj.categoryCollection = message.categoryCollection;
    }
    if (message.categoryId !== "") {
      obj.categoryId = message.categoryId;
    }
    if (message.flowType !== 0) {
      obj.flowType = flowTypeToJSON(message.flowType);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CategoryCashflow>, I>>(base?: I): CategoryCashflow {
    return CategoryCashflow.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CategoryCashflow>, I>>(object: I): CategoryCashflow {
    const message = createBaseCategoryCashflow();
    message.amount = object.amount ?? 0;
    message.percentage = object.percentage ?? 0;
    message.categoryCollection = object.categoryCollection ?? "";
    message.categoryId = object.categoryId ?? "";
    message.flowType = object.flowType ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
