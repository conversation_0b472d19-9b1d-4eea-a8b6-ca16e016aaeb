import * as React from "react";
import type { SVGProps } from "react";
const SvgService = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="currentColor"
    viewBox="0 0 35 48"
    {...props}
  >
    <g fillRule="evenodd" clipRule="evenodd">
      <path d="M19.805 29.733H14.3l.007 5.722 2.055-2.055a.877.877 0 0 1 1.23-.01l2.213 2.141zm-6.757 0h-.85a2.505 2.505 0 0 0-2.506 2.506v9.402a2.505 2.505 0 0 0 2.505 2.505H21.6a2.505 2.505 0 0 0 2.505-2.505V32.24a2.505 2.505 0 0 0-2.505-2.506h-.543v6.685c0 .774-.93 1.168-1.486.63l-2.585-2.501-2.433 2.434c-.552.551-1.496.161-1.497-.62z" />
      <path d="M17.09 18.381c2.491 0 4.012-1.626 4.853-2.99 1.06-1.722 1.695-4.105 1.695-6.375 0-3.733-2.936-6.769-6.544-6.769-3.61 0-6.545 3.036-6.545 6.769 0 2.213.616 4.563 1.644 6.289 1.215 2.04 2.864 3.073 4.9 3.073zm6.599 2.026H10.48c-4.424 0-8.023 3.423-8.023 7.63v9.748h.025l-.002.003 5.255.001v-7.927a2.505 2.505 0 0 1 2.506-2.505h13.313a2.505 2.505 0 0 1 2.506 2.505v7.932l5.638.001-.005-.014h.02v-9.748c0-4.206-3.602-7.629-8.026-7.629z" />
    </g>
  </svg>
);
export default SvgService;
