syntax = "proto3";
package backend_services.data_access;
import "database/fi_deposit.proto";

service DepositTxnCategoryMetadata {
    rpc UpdateTaggedTransactions(UpdateTaggedTransactionsRequest) returns (UpdateTaggedTransactionsResponse) {};
	rpc GetDepositUnparsedOrUncategorisedTxns(GetDepositUnparsedOrUncategorisedTxnsRequest) returns (GetDepositUnparsedOrUncategorisedTxnsResponse) {};
	rpc MarkDepositTxnsNotRequiredByAnnotationTool(MarkDepositTxnsNotRequiredByAnnotationToolRequest) returns (MarkDepositTxnsNotRequiredByAnnotationToolResponse) {};
	rpc DeleteDepositTxnCategoryMetadataAccountsData(DeleteDepositTxnCategoryMetadataAccountsDataRequest) returns (DeleteDepositTxnCategoryMetadataAccountsDataResponse) {};
	rpc CopyDepositTxnCategoryMetadataAccountsToDeleteCol(CopyDepositTxnCategoryMetadataAccountsToDeleteColRequest) returns (CopyDepositTxnCategoryMetadataAccountsToDeleteColResponse) {};
	rpc GetTxnsCategory(GetTxnsCategoryRequest) returns (GetTxnsCategoryResponse) {};
	rpc UpsertDepositTxnCategoryMetadata(UpsertDepositTxnCategoryMetadataRequest) returns (UpsertDepositTxnCategoryMetadataResponse) {};
}

message UpdateTaggedTransactionsRequest{
	repeated database.DepositTxnCategoryMetadata records = 1;
}
message UpdateTaggedTransactionsResponse{}


message GetDepositUnparsedOrUncategorisedTxnsRequest{
	int64 start_date = 1;
	int64 end_date = 2;
}
message GetDepositUnparsedOrUncategorisedTxnsResponse{
	repeated database.DepositTxnCategoryMetadata DepositTxnCategoryMetadata = 1;
}


message MarkDepositTxnsNotRequiredByAnnotationToolRequest{
	repeated string ids = 1; //object ids of docs DepositTxnCategoryMetadata to update
}
message MarkDepositTxnsNotRequiredByAnnotationToolResponse{}

message CopyDepositTxnCategoryMetadataAccountsToDeleteColRequest{
	repeated string account_ids = 1;
}
message CopyDepositTxnCategoryMetadataAccountsToDeleteColResponse {}

message DeleteDepositTxnCategoryMetadataAccountsDataRequest{
	repeated string account_ids = 1;
}
message DeleteDepositTxnCategoryMetadataAccountsDataResponse{}

message GetTxnsCategoryRequest {
	repeated string txn_ids = 1;
}
message GetTxnsCategoryResponse {
	repeated database.DepositTxnCategoryMetadata records = 1;
}

message UpsertDepositTxnCategoryMetadataRequest {
	repeated database.DepositTxnCategoryMetadata records = 1; 
}

message UpsertDepositTxnCategoryMetadataResponse {
}