syntax = "proto3";
package backend_services.data_access;

import "database/core.proto";
import "database/fi_deposit.proto";

service TransactionalQueries {
    rpc InsertRecurrentTransactionGroupAndDepositTransactions(InsertRecurrentTransactionGroupAndDepositTransactionsRequest) returns (InsertRecurrentTransactionGroupAndDepositTransactionsResponse) {};
}

message InsertRecurrentTransactionGroupAndDepositTransactionsRequest {
    repeated Record records = 1;
    message Record {
        database.RecurrentTransactionGroup group = 1;
        repeated database.DepositTransaction txns = 2;
    }
}

message InsertRecurrentTransactionGroupAndDepositTransactionsResponse {
}