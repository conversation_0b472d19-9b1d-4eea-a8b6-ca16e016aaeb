import * as React from "react";
import type { SVGProps } from "react";
const SvgFun = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="currentColor"
    viewBox="0 0 38 38"
    {...props}
  >
    <path
      fillRule="evenodd"
      d="M8.73.803h21.34c4.268 0 7.76 3.492 7.76 7.76v21.34c0 4.267-3.492 7.759-7.76 7.759H8.73c-4.267 0-7.76-3.492-7.76-7.76V8.562c0-4.267 3.493-7.76 7.76-7.76m15.094 22.193c.465-1.009 1.668-1.397 2.677-.931.97.504 1.397 1.707.892 2.677-1.474 3.065-4.617 5.005-7.992 5.005s-6.518-1.979-8.032-5.005c-.465-1.009-.077-2.173.931-2.677 1.01-.466 2.173-.078 2.678.93.853 1.708 2.522 2.755 4.423 2.755 1.9 0 3.608-1.047 4.423-2.754M12.882 18.65l3.298-2.754c.35-.31.582-.738.582-1.242a1.58 1.58 0 0 0-.582-1.242l-3.298-2.754c-.66-.582-1.707-.466-2.25.194-.582.66-.466 1.707.194 2.25l1.823 1.552-1.823 1.552c-.66.582-.776 1.59-.194 2.25.31.388.776.582 1.241.582.35 0 .699-.116 1.009-.388m13.037-7.992c.66-.582 1.668-.466 2.25.194.582.698.505 1.707-.194 2.25l-1.823 1.552 1.823 1.552c.66.543.776 1.59.194 2.25a1.58 1.58 0 0 1-1.241.582c-.35 0-.699-.155-1.009-.388l-3.298-2.754a1.58 1.58 0 0 1-.582-1.242c0-.504.233-.931.582-1.242z"
      clipRule="evenodd"
    />
  </svg>
);
export default SvgFun;
