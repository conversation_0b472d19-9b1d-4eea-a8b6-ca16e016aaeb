import { HomePage } from "@repo/ui";
import type { <PERSON>a, StoryObj } from "@storybook/react";

const meta: Meta<typeof HomePage> = {
  title: "Pages/Home",
  parameters: {
    layout: "fullscreen",
  },
  component: HomePage,
  argTypes: {
    username: { control: "text" },
    email: { control: "text" },
  },
};

export default meta;

type Story = StoryObj<typeof HomePage>;

export const Default: Story = {};
