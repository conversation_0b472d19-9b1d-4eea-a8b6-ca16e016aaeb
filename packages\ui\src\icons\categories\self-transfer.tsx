import * as React from "react";
import type { SVGProps } from "react";
const SvgSelfTransfer = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="currentColor"
    viewBox="0 0 30 29"
    {...props}
  >
    <path d="m13.3 14.677.017-.017c.584-.54.905-1.265.95-2.004a2.96 2.96 0 0 0-.744-2.153l-.028-.031.001-.002-.017-.017a2.97 2.97 0 0 0-4.158-.208l-7.618 6.753a2.97 2.97 0 0 0-.257 4.203q.072.08.133.142l.003.002-.003.001q.048.048.146.133l7.563 6.704-.001.002a2.965 2.965 0 0 0 2.183.77 2.97 2.97 0 0 0 2.022-.969v.001a.6.6 0 0 0 .072-.082 2.976 2.976 0 0 0-.3-4.15l-1.733-1.537h7.679c1.449 0 2.834-.292 4.102-.818a10.808 10.808 0 0 0 5.83-5.83 10.7 10.7 0 0 0 .818-4.102c0-1.45-.292-2.835-.818-4.103a10.808 10.808 0 0 0-5.83-5.83A10.7 10.7 0 0 0 19.21.719H3.687c-.825 0-1.572.333-2.113.875a2.98 2.98 0 0 0 0 4.224c.542.54 1.289.876 2.113.876H19.21a4.74 4.74 0 0 1 3.37 1.405c.443.443.8.97 1.043 1.552a4.737 4.737 0 0 1-1.043 5.188 4.8 4.8 0 0 1-1.552 1.043 4.7 4.7 0 0 1-1.818.362H11.53c.588-.52 1.18-1.04 1.764-1.565l.002.001z" />
  </svg>
);
export default SvgSelfTransfer;
