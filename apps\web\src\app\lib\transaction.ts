import { FetchDocuments } from "@repo/ui";
import { requestMetadata, createGrpcClient } from "./utils/create-grpc-client";
import { GRPC_CONFIG } from "./constants";
import {
  DepositTransactionClient,
  GetTransactionResponse,
  SearchBy,
  Transaction,
  TransactionFilters,
} from "./types/transaction.types";
import { applyTransactionFilters } from "./utils/transaction-filters";

const transactionClient: DepositTransactionClient = createGrpcClient<DepositTransactionClient>({
  protoPath: GRPC_CONFIG.PROTO_FILES.TRANSACTION,
  servicePath: 'backend_services.visualization',
  serviceConstructor: 'DepositTransaction'
});

export function getTransactions(
  authToken: string,
  pageSize: number,
  pageNumber: number,
  filters?: TransactionFilters
): Promise<GetTransactionResponse> {
  requestMetadata.set("authorization", authToken);
  const request = applyTransactionFilters(filters || {});
  request.paginationParams = {
    pageSize: pageSize,
    pageNumber: pageNumber,
  };

  return new Promise((resolve, reject) => {
    transactionClient.FetchDepositTxns(
      request,
      requestMetadata,
      (error, response) => {
        if (error) {
          console.error("FetchDepositTxns error:", error);
          reject(error);
        } else {
          resolve(response);
        }
      },
    );
  });
}

export function saveTransaction(
  authToken: string,
  transactionId: string,
  favorite: boolean,
): Promise<object> {
  requestMetadata.set("authorization", authToken);
  return new Promise((resolve, reject) => {
    transactionClient.MarkDepositTxnFavorite(
      {
        transactionId,
        favorite,
      },
      requestMetadata,
      (error, response) => {
        if (error) {
          reject(error);
        } else {
          resolve(response);
        }
      },
    );
  });
}

export function assignCategoryToTransaction(
  authToken: string,
  transactionId: string,
  categoryId: string,
  subcategoryId: string,
  collection: string = "",
): Promise<object> {
  requestMetadata.set("authorization", authToken);

  return new Promise((resolve, reject) => {
    transactionClient.AssignCategoryToDepositTxns(
      {
        transactionIds: [transactionId],
        categoryId,
        subcategoryId,
        collection,
      },
      requestMetadata,
      (error, response) => {
        if (error) {
          reject(error);
        } else {
          resolve(response);
        }
      },
    );
  });
}

export function excludeTransactionFromCashFlow(
  authToken: string,
  transactionId: string,
  flag: boolean,
): Promise<object> {
  requestMetadata.set("authorization", authToken);

  return new Promise((resolve, reject) => {
    transactionClient.ExcludeTxnFromCashFlow(
      {
        txnId: transactionId,
        excludeCashFlow: flag,
      },
      requestMetadata,
      (error, response) => {
        if (error) {
          reject(error);
        } else {
          resolve(response);
        }
      },
    );
  });
}

export function updateTransactionNotes(
  authToken: string,
  transactionId: string,
  notes: string,
): Promise<object> {
  requestMetadata.set("authorization", authToken);

  return new Promise((resolve, reject) => {
    transactionClient.UpdateTransactionNotes(
      {
        transactionId,
        notes,
      },
      requestMetadata,
      (error, response) => {
        if (error) {
          reject(error);
        } else {
          resolve(response);
        }
      },
    );
  });
}

export function fetchTransactionDocumentsRPC(
  authToken: string,
  transactionId: string,
): Promise<FetchDocuments> {
  requestMetadata.set("authorization", authToken);

  return new Promise((resolve, reject) => {
    transactionClient.FetchTransactionDocuments(
      {
        transactionId,
      },
      requestMetadata,
      (error, response) => {
        if (error) {
          reject(error);
        } else {
          resolve(response);
        }
      },
    );
  });
}

export function deleteTransactionDocumentsRPC(
  authToken: string,
  transactionId: string,
  fileNames: string[],
): Promise<{ ok: boolean }> {
  requestMetadata.set("authorization", authToken);

  return new Promise((resolve, reject) => {
    transactionClient.DeleteTransactionDocuments(
      {
        transactionId,
        objectNames: fileNames,
      },
      requestMetadata,
      (error) => {
        if (error) {
          reject(error);
        } else {
          resolve({
            ok: true,
          });
        }
      },
    );
  });
}

export function searchTransactionRPC(
  authToken: string,
  input: string,
  searchBy: SearchBy,
  pageNumber: number,
  pageSize: number,
  filters?: {
    timeRange?: { fromTime: number; toTime: number };
    amountRange?: { minAmount: number; maxAmount: number };
    transactionType?: string;
    tagStatus?: string;
    bookmarkOptions?: {
      showFavorites?: boolean;
      excludedFromCashflow?: boolean;
      withNotes?: boolean;
    };
  },
): Promise<{ cards: Transaction[] }> {
  requestMetadata.set("authorization", authToken);

  const request: any = {};

  if (filters) {
    request.filter = {
      txnFilters: {},
    };

    // Time range filter
    if (
      filters.timeRange &&
      (filters.timeRange.fromTime > 0 || filters.timeRange.toTime > 0)
    ) {
      request.filter.txnFilters.timeRange = {
        fromTime:
          filters.timeRange.fromTime > 0
            ? String(filters.timeRange.fromTime)
            : undefined,
        toTime:
          filters.timeRange.toTime > 0
            ? String(filters.timeRange.toTime)
            : undefined,
      };
      if (!request.filter.txnFilters.timeRange.fromTime) {
        delete request.filter.txnFilters.timeRange.fromTime;
      }
      if (!request.filter.txnFilters.timeRange.toTime) {
        delete request.filter.txnFilters.timeRange.toTime;
      }
      if (
        !request.filter.txnFilters.timeRange.fromTime &&
        !request.filter.txnFilters.timeRange.toTime
      ) {
        delete request.filter.txnFilters.timeRange;
      }
    }

    // Amount range filter
    if (
      filters.amountRange &&
      ((typeof filters.amountRange.minAmount === "number" &&
        filters.amountRange.minAmount > 0) ||
        (typeof filters.amountRange.maxAmount === "number" &&
          filters.amountRange.maxAmount > 0))
    ) {
      request.filter.txnFilters.amountRange = {};

      if (
        typeof filters.amountRange.minAmount === "number" &&
        filters.amountRange.minAmount > 0
      ) {
        request.filter.txnFilters.amountRange.minAmount = String(
          filters.amountRange.minAmount,
        );
      }

      if (
        typeof filters.amountRange.maxAmount === "number" &&
        filters.amountRange.maxAmount > 0
      ) {
        request.filter.txnFilters.amountRange.maxAmount = String(
          filters.amountRange.maxAmount,
        );
      }
    }

    // Bookmark options filter
    if (filters.bookmarkOptions) {
      const { showFavorites, excludedFromCashflow, withNotes } =
        filters.bookmarkOptions;

      if (showFavorites) {
        request.filter.txnFilters.favorited = { favorited: true };
      }

      if (excludedFromCashflow) {
        request.filter.txnFilters.excludeCashFlow = {
          excludeCashFlow: true,
        };
      }

      if (withNotes) {
        request.filter.txnFilters.hasUserNotes = { hasUserNotes: true };
      }
    }
    // Transaction type
    if (filters.transactionType && filters.transactionType !== "all") {
      const txnType =
        filters.transactionType.toLowerCase() === "incoming"
          ? "CREDIT"
          : "DEBIT";
      request.filter.txnFilters.txnType = { txnType: txnType };
    }

    // Tag status
    if (filters.tagStatus && filters.tagStatus.toLowerCase() !== "all") {
      const isUntagged = filters.tagStatus.toLowerCase() === "untagged";
      request.filter.txnFilters.untagged = { untagged: isUntagged };
    }
  }
  request.input = input;
  request.searchBy = searchBy;
  request.paginationParams = {
    pageSize: pageSize,
    pageNumber: pageNumber,
  };

  return new Promise((resolve, reject) => {
    transactionClient.SearchTxns(
      request,
      requestMetadata,
      (error, response: any) => {
        if (error) {
          reject(error);
        } else {
          resolve({
            cards: response.cards,
          });
        }
      },
    );
  });
}
