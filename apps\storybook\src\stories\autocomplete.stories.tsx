import { Autocomplete, AutocompleteContainer } from "@repo/ui";
import { Meta, StoryObj } from "@storybook/react";

const meta: Meta<typeof Autocomplete> = {
  title: "Components/Autocomplete",
  component: Autocomplete,
  tags: ["autodocs"],
  args: {
    className: "max-w-[335px]",
  },
};

export default meta;
type Story = StoryObj<typeof Autocomplete>;

const optionsList = [
  "Swiggy instamart",
  "Swiggy grocery",
  "Swiggy delivery",
  "ICICI Bank",
];

// Closed State – Initially only input is visible
export const Closed: Story = {
  args: {
    query: "",
    options: optionsList,
    open: false,
    onQueryChange: () => {},
    onOptionSelect: () => {},
    onClear: () => {},
    onCreateTag: () => {},
    onOpenChange: () => {},
  },
};

// Open State – Expanded view showing all tags
export const Open: Story = {
  args: {
    query: "",
    options: optionsList,
    open: true,
    onQueryChange: () => {},
    onOptionSelect: () => {},
    onClear: () => {},
    onCreateTag: () => {},
    onOpenChange: () => {},
  },
};

//Interactive State – Full functionality with filtering and adding tags
export const Interactive: Story = {
  render: () => <AutocompleteContainer />,
};
