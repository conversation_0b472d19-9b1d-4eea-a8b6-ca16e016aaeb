import React, { useState } from "react";
import type { <PERSON>a, StoryObj } from "@storybook/react";
import { BookmarkFilterOptions } from "@repo/ui";

const meta: Meta<typeof BookmarkFilterOptions> = {
  title: "Filters/BookmarkFilterOptions",
  component: BookmarkFilterOptions,
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof BookmarkFilterOptions>;

export const Interactive: Story = {
  render: () => {
    const [bookmarkOptions, setBookmarkOptions] = useState({
      showFavorites: false,
      showUntagged: false,
      showTagged: false,
      showIncoming: false,
      showOutgoing: false,
      excludedFromCashflow: false,
      showUnusual: false,
      showRecurring: false,
    });

    const onBookmarkOptionChange = (option: string, checked: boolean) => {
      switch (option) {
        case "Favourite Transactions":
          setBookmarkOptions((prev) => ({
            ...prev,
            showFavorites: checked,
          }));
          break;
        case "Untagged Transactions":
          setBookmarkOptions((prev) => ({
            ...prev,
            showUntagged: checked,
          }));
          break;
        case "Tagged Transactions":
          setBookmarkOptions((prev) => ({
            ...prev,
            showTagged: checked,
          }));
          break;
        case "Incoming Transactions":
          setBookmarkOptions((prev) => ({
            ...prev,
            showIncoming: checked,
          }));
          break;
        case "Outgoing Transactions":
          setBookmarkOptions((prev) => ({
            ...prev,
            showOutgoing: checked,
          }));
          break;
        case "Excluded from cashflow":
          setBookmarkOptions((prev) => ({
            ...prev,
            excludedFromCashflow: checked,
          }));
          break;
        case "Unusual Transactions":
          setBookmarkOptions((prev) => ({
            ...prev,
            showUnusual: checked,
          }));
          break;
        case "Recurring Transactions":
          setBookmarkOptions((prev) => ({
            ...prev,
            showRecurring: checked,
          }));
          break;
        default:
          break;
      }
    };

    return (
      <div className="w-[18rem]">
        <BookmarkFilterOptions
          filters={bookmarkOptions}
          onFilterChange={onBookmarkOptionChange}
        />
      </div>
    );
  },
};
