import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import {
  Button,
  Toast,
  ToastProvider,
  ToastViewport,
  Toaster,
  useToast,
} from "@repo/ui";

const meta: Meta<typeof Toast> = {
  title: "Components/Toast",
  component: Toast,
  tags: ["autodocs"],
  args: {
    description: 'You\'ve successfully added "Swiggy dineout"',
    open: true,
  },
};

export default meta;
type Story = StoryObj<typeof Toast>;

// Static Toast Story
export const Default: Story = {
  decorators: [
    (Story, { args }) => (
      <ToastProvider>
        <ToastViewport className="">
          <Story
            args={{
              ...args,
              // Prevent swipe to dismiss
              onSwipeEnd: (event) => event.preventDefault(),
              onSwipeMove: (event) => event.preventDefault(),
            }}
          />
        </ToastViewport>
      </ToastProvider>
    ),
  ],
};

// Interactive Toast Story
export const Interactive: Story = {
  render: function Render(args) {
    const { toast } = useToast();

    return (
      <ToastProvider>
        <Toaster />
        <Button
          onClick={() => {
            toast({
              title: args.title,
              description: args.description,
            });
          }}
        >
          Show Toast
        </Button>
      </ToastProvider>
    );
  },
};
