import { Button } from "@repo/ui";
import type { <PERSON>a, StoryObj } from "@storybook/react";

const meta: Meta<typeof Button> = {
  title: "Components/Button", // This title determines where the story will appear in the Storybook UI
  component: Button, // Component we are creating the story for
  tags: ["autodocs"],
  parameters: {
    layout: "centered", // Centers the component in the Storybook preview panel
  },
  args: {
    children: "Default Button", // Default button label
    variant: "default", // Default variant
    size: "default", // Default size
    disabled: false,
  },
};

export default meta; // This must be the default export

type Story = StoryObj<typeof meta>;

// Default story without overriding any args
export const Default: Story = {};

export const Secondary: Story = {
  args: {
    variant: "secondary",
    children: "Secondary Button",
  },
};

export const Conditional: Story = {
  args: {
    variant: "conditional",
    children: "Conditional Button",
  },
};

export const Outline: Story = {
  args: {
    variant: "outline",
    children: "Outline Button",
  },
};

export const Disabled: Story = {
  args: {
    children: "Disabled Button",
    disabled: true,
  },
};

// Custom story with a small button size
export const Small: Story = {
  args: {
    size: "sm",
    children: "Small Button",
  },
};

// Custom story with a link variant
export const Link: Story = {
  args: {
    variant: "link",
    children: "Link Button",
  },
};
