syntax = "proto3";
package backend_services.data_access;

import "database/core.proto";

service User {
	rpc InsertUsers(InsertUsersRequest) returns (InsertUsersResponse) {};
	rpc CreatePrimaryUserIfNotExists(CreatePrimaryUserIfNotExistsRequest) returns (CreatePrimaryUserIfNotExistsResponse) {};
	rpc GetPansOfFamily(GetPansOfFamilyRequest) returns (GetPansOfFamilyResponse) {}
	rpc GetFamilyIdOfPhoneNumber(GetFamilyIdOfPhoneNumberRequest) returns (GetFamilyIdOfPhoneNumberResponse) {}
	rpc UpdateUserPanAndGetId(UpdateUserPanAndGetIdRequest) returns (UpdateUserPanAndGetIdResponse) {}
	rpc UpsertUsersByPhoneAndPan(UpsertUsersByPhoneAndPanRequest) returns (UpsertUsersByPhoneAndPanResponse) {}
	rpc GetIdOfUserInFamilyWithPan(GetIdOfUserInFamilyWithPanRequest) returns (GetIdOfUserInFamilyWithPanResponse) {}
	rpc AddUserAlternatePhoneNumber(AddUserAlternatePhoneNumberRequest) returns (AddUserAlternatePhoneNumberResponse) {}
	rpc GetUsersOfFamily(GetUsersOfFamilyRequest) returns (GetUsersOfFamilyResponse) {};
	rpc GetUsersWithPhoneNumber(GetUsersWithPhoneNumberRequest) returns (GetUsersWithPhoneNumberRespone) {};
	rpc CountFamiliesOfPhoneExcludingOne(CountFamiliesOfPhoneExcludingOneRequest) returns (CountFamiliesOfPhoneExcludingOneResponse) {};
	rpc UpdateNonEmptyUserMetadata(UpdateNonEmptyUserMetadataRequest) returns (UpdateNonEmptyUserMetadataResponse) {};
	rpc AssignGroupToUsers(AssignGroupToUsersRequest) returns (AssignGroupToUsersResponse) {};
	rpc GetGroupIdsOfUsers(GetGroupIdsOfUsersRequest) returns (GetGroupIdsOfUsersResponse) {};
	rpc RemoveUsersFromGroup(RemoveUsersFromGroupRequest) returns (RemoveUsersFromGroupResponse) {};
	rpc GetGroupsUsers(GetGroupsUsersRequest) returns (GetGroupsUsersResponse) {};
	rpc RemoveAllUsersFromGroup(RemoveAllUsersFromGroupRequest) returns (RemoveAllUsersFromGroupResponse) {};
	rpc UpdateUserById(UpdateUserByIdRequest) returns (UpdateUserByIdResponse) {}
	rpc DeleteUserWithoutPanByPhone(DeleteUserWithoutPanByPhoneRequest) returns (DeleteUserWithoutPanByPhoneResponse) {};
	rpc CountUsersMatchingPhone(CountUsersMatchingPhoneRequest) returns (CountUsersMatchingPhoneResponse) {};
}

message InsertUsersRequest{
	repeated database.User users = 1;
}
message InsertUsersResponse{}

message CreatePrimaryUserIfNotExistsRequest {
	string phone_number = 1;
}
message CreatePrimaryUserIfNotExistsResponse {
}

message CountDocsMatchingFamilyIdRequest{
	string family_id = 1;
	string phone_number = 2;
}
message CountDocsMatchingFamilyIdResponse{
	int64 count = 1;
}




message GetPansOfFamilyRequest{
	string family_id = 1;
}
message GetPansOfFamilyResponse{
	repeated string pans = 1;
}


message GetFamilyIdOfPhoneNumberRequest{
	string phone_number = 1;
}
message GetFamilyIdOfPhoneNumberResponse{
	string family_id = 1;
}


message UpdateUserPanAndGetIdRequest{
	string phone_number = 1;
	string pan = 2;
	string family_id = 3;
}
message UpdateUserPanAndGetIdResponse{
	string userId = 1;
}


message UpsertUsersByPhoneAndPanRequest{
	repeated database.User users = 1;
}
message UpsertUsersByPhoneAndPanResponse{
}


message GetIdOfUserInFamilyWithPanRequest{
	string family_id = 1;
	string pan = 2;
}
message GetIdOfUserInFamilyWithPanResponse{
	string user_id = 1;
}


message AddUserAlternatePhoneNumberRequest{
	string user_id = 1;
	string alternate_phone_number = 2;
}
message AddUserAlternatePhoneNumberResponse{}


message GetUsersOfFamilyRequest{
	string family_id = 1;
}
message GetUsersOfFamilyResponse{
	repeated database.User users = 1;
}


message GetUsersWithPhoneNumberRequest{
	string phone_number = 1;
}
message GetUsersWithPhoneNumberRespone{
	repeated database.User users = 1;
}

message CountFamiliesOfPhoneExcludingOneRequest{
	string phone_number = 1;
	string family_id_to_exclude = 2;
}
message CountFamiliesOfPhoneExcludingOneResponse{
	int64 count = 1;
}

message UpdateNonEmptyUserMetadataRequest{
	string id = 1;
	string name = 2;
	string address = 3;
}
message UpdateNonEmptyUserMetadataResponse{}

message AssignGroupToUsersRequest{
	string group_id = 1;
	repeated string user_ids = 2;
	string family_id = 3;
}
message AssignGroupToUsersResponse{}

message GetGroupIdsOfUsersRequest {
	repeated string user_ids = 1;
	string family_id = 2;
}

message GetGroupIdsOfUsersResponse {
	repeated database.User users = 1;
}

message RemoveUsersFromGroupRequest {
	string group_id = 1;
	repeated string user_ids = 2;
}
message RemoveUsersFromGroupResponse {}

message GetGroupsUsersRequest {
	repeated string group_ids = 1;
	string family_id = 2;
}

message GetGroupsUsersResponse {
	repeated database.User users = 1;
}

message RemoveAllUsersFromGroupRequest{
	string group_id = 1;
	string family_id = 2;
}

message RemoveAllUsersFromGroupResponse{}

message UpdateUserByIdRequest {
	database.User user = 1;
}

message UpdateUserByIdResponse {}

message DeleteUserWithoutPanByPhoneRequest {
	string phone_number = 1;
}

message DeleteUserWithoutPanByPhoneResponse {}

message CountUsersMatchingPhoneRequest {
	string phone_number = 1;
	string family_id = 2;
}

message CountUsersMatchingPhoneResponse {
	int64 count = 1;
}