syntax = "proto3";
package backend_services.visualization;

message OverallCashflow {
    double amount = 1;
    FlowType flow_type = 2;
}

message CategoryCashflow {
    double amount = 1;
    double percentage = 2;
    string category_collection = 3;
    string category_id = 4;
    FlowType flow_type = 5;
}

enum FlowType {
    Expense = 0; 
    Income = 1;
    Investment = 2;
}

enum TimeStep {
    Daily = 0;
    Weekly = 1;
    Monthly = 2;
    Yearly = 3;
}