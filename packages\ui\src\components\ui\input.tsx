import * as React from "react";
import { cn } from "../../lib/utils";

interface InputProps extends React.ComponentProps<"input"> {
  prefixIcon?: React.ReactNode;
  prefixText?: string;
  outerClassName?: string;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    { className, outerClassName, prefixIcon, prefixText, type, ...props },
    ref,
  ) => {
    return (
      <div
        className={cn(
          "flex items-center w-full px-3 py-2 gap-2 border border-input rounded-md bg-transparent text-sm font-medium text-[#1E1E1E] shadow-sm transition-colors focus-within:ring-1 focus-within:ring-ring disabled:cursor-not-allowed disabled:opacity-50",
          outerClassName,
        )}
      >
        {prefixIcon}
        {prefixText && (
          <span className="text-[#797878] font-normal">{prefixText}</span>
        )}
        <input
          type={type}
          className={cn(
            "w-full bg-transparent outline-none placeholder:text-[#797878]",
            className,
          )}
          ref={ref}
          {...props}
        />
      </div>
    );
  },
);

Input.displayName = "Input";

export { Input };
