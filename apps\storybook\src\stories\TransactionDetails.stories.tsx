import type { <PERSON>a, StoryObj } from "@storybook/react";
import {
  Button,
  creditCategories,
  debitCategories,
  dummyTransactions,
  Sheet,
  SheetContent,
  SheetTitle,
  SheetTrigger,
  TransactionCard,
  TransactionCardWithDate,
  TransactionDetails,
} from "@repo/ui";

const dummyCategories = [...creditCategories, ...debitCategories];

const meta: Meta<typeof TransactionDetails> = {
  title: "Components/Transaction Details",
  component: TransactionDetails,
  tags: ["autodocs"],
  args: {
    transaction: dummyTransactions[0]?.transactions[0],
    date: dummyTransactions[0]?.date,
  },
};

export default meta;
type Story = StoryObj<typeof TransactionDetails>;

export const Default: Story = {};

export const Interactive: Story = {
  render: () => <TransactionDetailsWithSheet />,
};

const TransactionDetailsWithSheet = () => {
  const handleSavedChanged = (txnId: string, flag: boolean) => {
    console.log(txnId, flag);
  };
  const handleExcludedCashflowChanged = (txnId: string, flag: boolean) => {
    console.log(txnId, flag);
  };
  const handleNotesChanged = (txnId: string, notes: string) => {
    console.log(txnId, notes);
  };
  const handleUploadFile = (txnId: string, file: File) => {
    console.log(txnId, file);
  };
  const handleDeleteFile = (txnId: string, fileName: string) => {
    console.log(txnId, fileName);
  };
  const onAddEditTagClick = (transaction: TransactionCardWithDate) => {
    console.log(transaction);
  };
  return (
    <Sheet>
      <SheetTrigger>
        <Button size="sm">Open Transaction Details</Button>
      </SheetTrigger>
      <SheetContent className="max-w-[700px] sm:max-w-[600px] overflow-y-auto">
        <SheetTitle className="mb-4">Transaction Details</SheetTitle>
        <TransactionDetails
          transaction={dummyTransactions[0]?.transactions[0] as TransactionCard}
          date={dummyTransactions[0]?.date as Date}
          onSavedChanged={handleSavedChanged}
          onExcludedCashflowChanged={handleExcludedCashflowChanged}
          onNotesChanged={handleNotesChanged}
          onUploadFile={handleUploadFile}
          onDeleteFile={handleDeleteFile}
          currentTransactionDocs={[]}
          categories={dummyCategories}
          onAddEditTagClick={onAddEditTagClick}
        />
      </SheetContent>
    </Sheet>
  );
};
