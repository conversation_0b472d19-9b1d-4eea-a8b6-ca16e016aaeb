ui-tests:
  image: node:iron
  stage: test
  interruptible: true
  variables:
    GIT_DEPTH: 0
  before_script:
    - corepack enable
    - corepack prepare --activate
    - pnpm config set store-dir .pnpm-store
  script:
    - pnpm install
    - pnpm run chromatic --only-changed --exit-zero-on-changes
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
      when: manual
  cache:
    key: $CI_COMMIT_REF_SLUG-$CI_PROJECT_DIR
    paths:
      - .pnpm-store/
