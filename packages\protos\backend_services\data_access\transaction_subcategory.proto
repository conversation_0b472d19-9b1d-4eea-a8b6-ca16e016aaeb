syntax = "proto3";
package backend_services.data_access;
import "database/custom_type.proto";
import "database/transaction_category.proto";

service TransactionSubcategory {
	rpc InsertUserTxnSubcategory(InsertUserTxnSubcategoryRequest) returns (InsertUserTxnSubcategoryResponse) {};
	rpc UpdateUserTxnSubcategory(UpdateUserTxnSubcategoryRequest) returns (UpdateUserTxnSubcategoryResponse) {};
	rpc AddCategoryMapping(AddCategoryMappingRequest) returns (AddCategoryMappingResponse) {};
	rpc RemoveCategoryMapping(RemoveCategoryMappingRequest) returns (RemoveCategoryMappingResponse) {};
	rpc DeleteUserTxnSubcategory(DeleteUserTxnSubcategoryRequest) returns (DeleteUserTxnSubcategoryResponse) {};
	rpc GetMappingsGroupedByCategory(GetMappingsGroupedByCategoryRequest) returns (GetMappingsGroupedByCategoryResponse) {};
	rpc GetTxnSubcategoriesById(GetTxnSubcategoriesByIdRequest) returns (GetTxnSubcategoriesByIdResponse) {};
}

message InsertUserTxnSubcategoryRequest{
	database.TransactionSubcategory record = 1;
}
message InsertUserTxnSubcategoryResponse{
	database.ObjectId subcategory_id = 1;
}


message AddCategoryMappingRequest{
	database.ObjectId subcategory_id = 1;
	database.ObjectId category_id = 2;
	repeated string txn_types = 3; 
}
message AddCategoryMappingResponse{}

message DeleteUserTxnSubcategoryRequest {
	database.ObjectId subcategory_id = 1;
}
message DeleteUserTxnSubcategoryResponse{}

message GetMappingsGroupedByCategoryRequest{
	string txn_type = 1;
}
message GetMappingsGroupedByCategoryResponse{
	message Category {
		string id = 1;
		string name = 2;
	}
	message Subcategory{
		string id = 1;
		string name = 2;
	}
	message Mapping {
		Category category = 1;
		repeated Subcategory subcategories = 2;
	}
	repeated Mapping mappings = 1;
}

message RemoveCategoryMappingRequest {
	database.ObjectId category_id = 2;
	database.ObjectId subcategory_id = 3;
}
message RemoveCategoryMappingResponse {}


message UpdateUserTxnSubcategoryRequest{
	database.ObjectId subcategory_id = 1;
	string subcategory_name = 2;
}
message UpdateUserTxnSubcategoryResponse{}

message GetTxnSubcategoriesByIdRequest {
	repeated database.ObjectId ids = 1;
}

message GetTxnSubcategoriesByIdResponse {
	repeated database.TransactionSubcategory records = 1;
}