// in_org_rebit_api_fischema_mutual_funds.proto at 0:0
syntax = "proto3";
package rebit.fi_schemas.mutual_funds;


/*
  REBIT FI Type - Equities Schema as per https://specifications.rebit.org.in/api_schema/account_aggregator/documentation/mutual_funds.html
*/

message Account {
  string linked_acc_ref = 1; // @gotags: json:"linkedAccRef"
  float version = 2; // @gotags: json:"version,string"
  string type = 3; // @gotags: json:"type"
  // Demat ID assigned or issued to investor
  string masked_demat_id = 4; // @gotags: json:"maskedDematID"
  // Folio Number of Investor
  string masked_folio_no = 5;  // @gotags: json:"maskedFolioNo"
  Profile profile = 6;  // @gotags: json:"Profile"
  Summary summary = 7;  // @gotags: json:"Summary"
  Transactions transactions = 8;  // @gotags: json:"Transactions"
}
message Holder {
  // Name of primary holder operating the account.
  string name = 1;  // @gotags: json:"name"
  // Date of birth of primary account holder
  DateOfBirth dob = 2; // @gotags: json:"dob"
  // Primary mobile number of account holder
  string mobile = 3; // @gotags: json:"mobile"
  // This field will indicate whether a nominee is registered.
  string nominee = 4; // @gotags: json:"nominee"
  // Demat ID assigned or issued to investor
  string demat_id = 5; // @gotags: json:"dematId"
  // Folio Number of Investor
  string folio_no = 6; // @gotags: json:"folioNo"
  // Landline number of primary account holder.
  string landline = 7; // @gotags: json:"landline"
  // Address of primary account holder.
  string address = 8; // @gotags: json:"address"
  // Email ID of primary account holder.
  string email = 9; // @gotags: json:"email"
  // PAN number of primary account holder
  string pan = 10; // @gotags: json:"pan"
  // KYC status whether its completed or pending.
  // Note: This differs between ReBIT and Finarkein.
  string kyc_compliance = 11; // @gotags: json:"ckycCompliance"

  // Note: These differs between ReBIT and Finarkein.
  string masked_demat_id = 12; // @gotags: json:"maskedDematID"
  string masked_folio_no = 13; // @gotags: json:"maskedFolioNo"
  string masked_acc_number = 14; // @gotags: json:"maskedAccNumber"
  string linked_acc_ref = 15; // @gotags: json:"linkedAccRef"

  message DateOfBirth {
    int64 value = 1;
  }
}
message Holders {
  repeated Holder holder = 1; // @gotags: json:"Holder"
}
enum HoldingMode {
  // Default
  HOLDING_MODE_UNSPECIFIED = 0;
  DEMAT = 1;
  PHYSICAL = 2;
}
enum HoldingNominee {
  // Default
  HOLDING_NOMINEE_UNSPECIFIED = 0;
  REGISTERED = 1;
  NOT_REGISTERED = 2;
}
message Holding {
  // Name of asset management company associated with investment
  string amc = 1; // @gotags: json:"amc"
  // Trust or institution registering and maintaining the detailed records of the transactions of investors for the convenience of mutual fund houses. E.g. CAMS/KARVY
  string registrar = 2; // @gotags: json:"registrar"
  // Code of scheme chosen by person for the investment
  string scheme_code = 3; // @gotags: json:"schemeCode"
  // It defines the Income Distribution cum Capital Withdrawal option in MF ( Growth, Reinvest, Payout)
  string scheme_option = 4; // @gotags: json:"schemeOption"
  // All MF schemes are classified into one of the five categories ( Equity, Debt, Hybrid, Solution Oriented and Other Schemes) under SEBI's guidelines on MF schemes categorization and rationalization
  string scheme_types = 5; // @gotags: json:"schemeTypes"
  // It defines MF scheme categorization based on type of underlying assets and style of investments
  string scheme_category = 6; // @gotags: json:"schemeCategory"
  // International Securities Identification Number (ISIN) uniquely identifies a security.
  string isin = 7; // @gotags: json:"isin"
  // Long Name of ISIN
  string isin_description = 8; // @gotags: json:"isinDescription"
  // Unique Client Code generated by Stock Exchanges.
  string ucc = 9; // @gotags: json:"ucc"
  // AMFI code associated with investment made.
  string amfi_code = 10; // @gotags: json:"amfiCode"
  // Unique folio number associated with investment.
  string folio_no = 11; // @gotags: json:"folioNo"
  // Whether the Mutual Fund is FATCA (Foreign Account Tax Compliance Act) compliant - Yes/No.
  string fatca_status = 12; // @gotags: json:"FatcaStatus"
  // Units allotted in folio till date
  string closing_units = 13; // @gotags: json:"closingUnits"
  // The number of units which have been marked as Lien (Mutual fund units pledged against some borrowed amount).
  string lien_units = 14; // @gotags: json:"lienUnits"
  // NAV is current market rate of security held as on date
  double nav = 15; // @gotags: json:"nav"
  // Last Available NAV date for the scheme of investment
  NavDate nav_date = 16; // @gotags: json:"navDate"
  // Units which are locked and not available for sale
  string lockin_units = 17; // @gotags: json:"lockinUnits"

  message NavDate {
    int64 value = 1;
  }
}
message Holdings {
  repeated Holding holding = 1; // @gotags: json:"Holding"
}
message Investment {
  Holdings holdings = 1; // @gotags: json:"Holdings"
}
message Profile {
  Holders holders = 1; // @gotags: json:"Holders"
}
enum SchemeCategory {
  // Default
  SCHEME_CATEGORY_UNSPECIFIED = 0;
  MULTI_CAP_FUND = 1;
  LARGE_CAP_FUND = 2;
  LARGE_AND_MID_CAP_FUND = 3;
  MIDCAP_FUND = 4;
  SMALL_CAP_FUND = 5;
  DIVIDEND_YIELD_FUND = 6;
  VALUE_FUND = 7;
  CONTRA_FUND = 8;
  FOCUSED_FUND = 9;
  SECTORAL_OR_THEMATIC = 10;
  ELSS = 11;
  OVERNIGHT_FUND = 12;
  LIQUID_FUND = 13;
  ULTRA_SHORT_DURATION_FUND = 14;
  LOW_DURATION_FUND = 15;
  MONEY_MARKET_FUND = 16;
  SHORT_DURATION_FUND = 17;
  MEDIUM_DURATION_FUND = 18;
  MEDIUM_TO_LONG_DURATION_FUND = 19;
  LONG_DURATION_FUND = 20;
  DYNAMIC_BOND = 21;
  CORPORATE_BOND_FUND = 22;
  CREDIT_RISK_FUND = 23;
  BANKING_AND_PSU_FUND = 24;
  GILT_FUND = 25;
  GILT_FUND_WITH_10_YEAR_CONSTANT_DURATION = 26;
  FLOATER_FUND = 27;
  CONSERVATIVE_HYBRID_FUND = 28;
  BALANCED_HYBRID_FUND = 29;
  AGGRESSIVE_HYBRID_FUND = 30;
  DYNAMIC_ASSET_ALLOCATION_OR_BALANCED_ADVANTAGE = 31;
  MULTI_ASSET_ALLOCATION = 32;
  ARBITRAGE_FUND = 33;
  EQUITY_SAVINGS = 34;
  RETIREMENT_FUND = 35;
  CHILDREN_S_FUND = 36;
  INDEX_FUNDS_OR_ETFS = 37;
  FOFS_OVERSEAS_OR_DOMESTIC = 38;
}
enum SchemeOption {
  // Default
  SCHEME_OPTION_UNSPECIFIED = 0;
  REINVEST = 1;
  PAYOUT = 2;
  GROWTH_TYPE = 3;
}
enum SchemePlan {
  // Default
  SCHEME_PLAN_UNSPECIFIED = 0;
  DIRECT = 1;
  REGULAR = 2;
}
enum SchemeTypes {
  // Default
  SCHEME_TYPES_UNSPECIFIED = 0;
  EQUITY_SCHEMES = 1;
  DEBT_SCHEMES = 2;
  HYBRID_SCHEMES = 3;
  SOLUTION_ORIENTED_SCHEMES = 4;
  OTHER_SCHEMES = 5;
}
message Summary {
  // Principal or Actual amount of investment.
  // Note: Finarkein does not return this and instead returns holding-wise cost_value,
  // but we anyway don't use it as we rely on public data to get NAV at purchase (cost value).
  double cost_value = 1; // @gotags: json:"costValue"
  // Current value of investment as on date.
  // Note: Finarkein does not return this and instead returns holding-wise current_value,
  // but we anyway don't use it as we rely on public data to get live value based on NAV.
  double current_value = 2; // @gotags: json:"currentValue"
  Investment investment = 3; // @gotags: json:"Investment"
}
message Transaction {
  // Transaction ID recorded or captured for investment made.
  string txn_id = 1; // @gotags: json:"txnId"
  // Asset Management company which is operating the fund.
  string amc = 2; // @gotags: json:"amc"
  // Trust or institution registering and maintaining the detailed record of the given transaction. E.g. CAMS/KARVY.
  string registrar = 3; // @gotags: json:"registrar"
  // Code of scheme chosen by person for the investment.
  string scheme_code = 4; // @gotags: json:"schemeCode"
  // It defines the options to buy the same mutual fund scheme, run by the same fund managers who invest in the same stocks and bonds.
  string scheme_plan = 5; // @gotags: json:"schemePlan"
  // International Securities Identification Number (ISIN) uniquely identifies a security.
  string isin = 6; // @gotags: json:"isin"
  // AMFI code associated with investment made.
  string amfi_code = 7; // @gotags: json:"amfiCode"
  // Unique Client Code generated by Stock Exchanges for the given transaction.
  string ucc = 8; // @gotags: json:"ucc"
  // The transaction amount.
  // Note: This differs between ReBIT and Finarkein.
  double amount = 9; // @gotags: json:"amount"
  // NAV is current market rate of security held as on date.
  string nav = 10; // @gotags: json:"nav"
  // Last Available NAV date for the scheme of investment
  NavDate nav_date = 11; // @gotags: json:"navDate"
  // Transaction type may be BUY or SELL.
  string type = 12; // @gotags: json:"type"
  // Flag which identify units which are locked and not available for sale.
  string lockin_flag = 13; // @gotags: json:"lock-inFlag"
  // Units which are locked and not available for sale.
  string lockin_days = 14; // @gotags: json:"lock-inDays"
  // Mode of transaction. May be DEMAT,NEFT,Net banking, etc.
  string mode = 15; // @gotags: json:"mode"
  // Narration is additional details in form of description of remark associated with investment.
  string narration = 16; // @gotags: json:"narration"
  // Long Name of ISIN
  string isin_description = 17; // @gotags: json:"isinDescription"
  // Transacted units / quantity of ISIN
  string units = 18; // @gotags: json:"units"
  // Transaction Date
  TransactionDate transaction_date = 19; // @gotags: json:"transactionDate"

  message TransactionDate {
    int64 value = 1;
  }
  message NavDate {
    int64 value = 1;
  }
}
enum TransactionType {
  // Default
  TRANSACTION_TYPE_UNSPECIFIED = 0;
  TRANSACTION_TYPE_BUY = 1;
  TRANSACTION_TYPE_SELL = 2;
  TRANSACTION_TYPE_OTHERS = 3;
}
message Transactions {
  // The date from which the Financial Information was requested
  string start_date = 1; // @gotags: json:"startDate"
  // The date till which the Financial Information was requested
  string end_date = 2; // @gotags: json:"endDate"
  repeated Transaction transaction = 3; // @gotags: json:"Transaction"
}
