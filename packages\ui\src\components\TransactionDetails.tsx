"use client";

import React, { useEffect, useMemo, useState } from "react";
import {
  BankIcon,
  Category,
  TransactionCard,
  TransactionCardWithDate,
  TransactionDocument,
  TransactionTypeEnum,
} from "./TransactionsTable";
import { Badge } from "./ui/badge";
import { Separator } from "./ui/separator";
import { addMonths, format } from "date-fns";
import { categoryIconMap } from "./AddEditTag";
import { Button } from "./ui/button";
import { Switch } from "./ui/switch";
import { Textarea } from "./ui/textarea";
import { cn } from "../lib/utils";
import { currencyFormatter } from "../lib/helper";
import SvgAddV2 from "../icons/add-v2";
import SvgDelete from "../icons/delete";
import SvgTextfile from "../icons/textfile";
import SvgExcludedCashFlowV2 from "../icons/excluded-cash-flow-v2";
import SvgSavedTransactionV2 from "../icons/saved-transaction-v2";
import { Misc } from "../icons/categories";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select";

interface TransactionDetailsProps {
  transaction: TransactionCard;
  date: Date;
  onSavedChanged: (transactionId: string, flag: boolean) => void;
  onExcludedCashflowChanged: (transactionId: string, flag: boolean) => void;
  onNotesChanged: (transactionId: string, notes: string) => void;
  onUploadFile: (transactionId: string, file: File) => void;
  onDeleteFile: (transactionId: string, fileName: string) => void;
  currentTransactionDocs: TransactionDocument[];
  categories: Category[];
  onAddEditTagClick: (transaction: TransactionCardWithDate) => void;
}

interface FileWithImgUrl extends File {
  imgUrl?: string;
}

function TransactionDetails({
  transaction,
  date,
  onSavedChanged,
  onExcludedCashflowChanged,
  onNotesChanged,
  onUploadFile,
  currentTransactionDocs = [],
  onDeleteFile,
  categories,
  onAddEditTagClick,
}: TransactionDetailsProps) {
  const [selectedFiles, setSelectedFiles] = useState<{
    [id: string]: FileWithImgUrl;
  }>({});
  const [isExcludedCashflow, setIsExcludedCashflow] = useState(
    transaction.isExcludedCashflow,
  );
  const [isSaved, setIsSaved] = useState(transaction.isSaved);
  const [notes, setNotes] = useState(transaction.notes);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files ? event.target.files[0] : null;

    if (file) {
      setSelectedFiles((prev) => ({
        ...prev,
        [file.name]: file,
      }));
      if (file && file.type.startsWith("image/")) {
        const reader = new FileReader();
        reader.onload = () => {
          if (reader.result) {
            setSelectedFiles((prev) => ({
              ...prev,
              [file.name]: {
                ...file,
                imgUrl: reader.result as string,
              },
            }));
          }
        };

        reader.readAsDataURL(file);
      }
      onUploadFile(transaction.txnId, file);
    }
  };

  const handleDeleteFile = (fileName: string) => {
    setSelectedFiles((prev) => {
      const files = { ...prev };
      delete files[fileName];
      return files;
    });
    onDeleteFile(transaction.txnId, fileName);
  };

  const handleOnChangedSaved = () => {
    onSavedChanged(transaction.txnId, !isSaved);
    setIsSaved((prev) => !prev);
  };

  const handleOnChanegdExcludedCashflow = () => {
    onExcludedCashflowChanged(transaction.txnId, !isExcludedCashflow);
    setIsExcludedCashflow((prev) => !prev);
  };

  function handleAddEditTagClick(transaction: TransactionCard, date: Date) {
    onAddEditTagClick({ ...transaction, date: date });
  }

  const accountedDate = useMemo(() => {
    return new Date(
      transaction.cashFlowPeriod?.year,
      transaction.cashFlowPeriod?.month - 1,
    );
  }, [transaction.cashFlowPeriod]);

  const [selectedAccountedIn, setSelectedAccountedIn] = useState(() =>
    format(accountedDate, "MMMM yyyy"),
  );

  useEffect(() => {
    setSelectedAccountedIn(format(accountedDate, "MMMM yyyy"));
  }, [accountedDate]);

  const accountedOptions = [-1, 0, 1].map((offset) => {
    const date = addMonths(accountedDate, offset);
    return format(date, "MMMM yyyy");
  });

  const formatteddate = format(date, "MMM d ''yy, h:mm a");
  const isCreditedTransaction =
    transaction.txnType === TransactionTypeEnum.CREDIT;

  // Format the amount to a proper currency
  const formattedAmount = currencyFormatter
    .format(transaction.amount)
    .split(".");
  const amountUnit = formattedAmount[0] + ".";
  const amountSubUnit = formattedAmount[1];
  const tag = transaction.tag as any;
  const categoryId = tag.categoryId || tag.category_id || '';
  const selectedCategory = categories?.find(
    (item) => item.id === categoryId
  );

  useEffect(() => {
    setNotes(transaction.notes);
  }, [transaction]);

  return (
    <div className="flex gap-4 flex-col bg-background pb-3">
      {/* Transaction main details */}
      <div className="flex flex-col items-center gap-10 border border-[#F4EEF9] rounded-2xl p-9 text-sm bg-gradient-to-t from-white to-[#FAF8FC]">
        <div className="flex flex-col items-center w-4/5 gap-5">
          <div className="flex flex-col items-center">
            <span>{isCreditedTransaction ? "Received from" : "Paid to"}</span>
            <span className="text-primary font-semibold">
              {transaction.merchantName}
            </span>
          </div>
          <div className="flex flex-col gap-2 items-center min-w-52">
            <div
              className={cn(
                "text-5xl font-semibold",
                isCreditedTransaction && "text-[#4FA33F]",
              )}
            >
              {isCreditedTransaction && (
                <span className="mr-1" aria-hidden={true}>
                  +
                </span>
              )}
              <span>{amountUnit}</span>
              <span
                className={cn(
                  "text-2xl",
                  isCreditedTransaction ? "text-[#6FBE5D]" : "text-[#797878]",
                )}
              >
                {amountSubUnit}
              </span>
            </div>
            <Separator className="bg-[#EBE0F4]" />
            <span className="text-xs">{formatteddate}</span>
          </div>
          <div>
            <div className="flex">
              <span>{isCreditedTransaction ? "Received by" : "Paid by"}</span>
              <span className="font-medium px-1">
                {transaction.account?.holderName}
              </span>
              <span className="self-center px-1">
                <BankIcon bankName={transaction.bankName} className="w-5" />
              </span>
              <span>**{transaction.account?.maskedAccNumber.slice(-2)}</span>
            </div>
          </div>
          <div>
            {selectedCategory ? (
              <Badge
                selected={false}
                variant="default"
                startIcon={categoryIconMap[selectedCategory.name] || <Misc />}
                className="text-[#905BB5]"
                onClick={() => handleAddEditTagClick(transaction, date)}
              >
                {selectedCategory.name}
              </Badge>
            ) : (
              <Button
                size="sm"
                variant="outline"
                className="px-4 py-1.5 font-semibold rounded-[10px]"
                onClick={() => handleAddEditTagClick(transaction, date)}
              >
                Add Tag
              </Button>
            )}
          </div>
        </div>
        <div className="flex flex-col gap-5 w-full text-xs">
          <div className="grid grid-cols-2 gap-5">
            <div className="flex flex-col gap-1">
              <span className="text-[#797878]">Transaction ID</span>
              <span className="font-medium break-all">{transaction.txnId}</span>
            </div>
            <div className="flex flex-col gap-1">
              <span className="text-[#797878]">Transaction Mode</span>
              <span className="font-medium">{transaction.txnMode}</span>
            </div>
            <div className="flex flex-col gap-1">
              <span className="text-[#797878]">Accounted in</span>
              <Select
                value={selectedAccountedIn}
                onValueChange={setSelectedAccountedIn}
              >
                <SelectTrigger className="w-fit text-primary bg-transparent focus:outline-none border-none h-4 p-0">
                  <SelectValue placeholder="Select Month" />
                </SelectTrigger>
                <SelectContent>
                  {accountedOptions.map((month) => (
                    <SelectItem key={month} value={month}>
                      {month}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex flex-col gap-1">
              <span className="text-[#797878]">History</span>
              <span className="text-primary font-medium underline underline-offset-2">
                2 Transactions
              </span>
            </div>
          </div>
          <div className="flex flex-col gap-1">
            <span className="text-[#797878]">Narration</span>
            <span className="font-medium text-[10px] break-all">
              {transaction.narration}
            </span>
          </div>
        </div>
      </div>

      {/* Transaction toggle buttons */}
      <div className="flex flex-col border border-[#F4EEF9] rounded-2xl p-5 gap-4 text-xs font-medium">
        <div className="flex justify-between items-center">
          <div className="flex gap-2 items-center">
            <SvgExcludedCashFlowV2
              width="20px"
              height="16px"
              className="w-6 text-primary"
            />
            <div>Exclude from cash flow</div>
          </div>
          <Switch
            defaultChecked={isExcludedCashflow}
            onCheckedChange={handleOnChanegdExcludedCashflow}
          />
        </div>
        <div className="flex justify-between items-center">
          <div className="flex gap-2 items-center">
            <SvgSavedTransactionV2
              width="22px"
              height="22px"
              className="w-6 text-primary"
            />
            <div>Save Transaction</div>
          </div>
          <Switch
            defaultChecked={isSaved}
            onCheckedChange={handleOnChangedSaved}
          />
        </div>
      </div>

      {/* Transaction receipts */}
      <div className="flex flex-col border border-[#EBE0F4] border-dashed rounded-2xl p-5 gap-4 text-xs">
        <input type="file" id="fileInput" hidden onChange={handleFileChange} />
        <label
          className="flex justify-between items-center cursor-pointer"
          htmlFor="fileInput"
        >
          <div className="flex flex-col gap-1">
            <span className="font-medium">Your receipts</span>
            <span className="text-[#797878]">
              Click here to upload additional files
            </span>
          </div>
          <SvgAddV2 className="w-8" width="32px" height="32px" />
        </label>
        {currentTransactionDocs.length > 0 && (
          <div
            className="flex flex-col gap-3 p-3 rounded-lg bg-[#EEF5FF]"
            key="currentTransactionDocs"
          >
            {currentTransactionDocs.map((file) => (
              <div
                className="flex items-center justify-between"
                key={file.objectName}
              >
                <div className="flex items-center gap-2">
                  {file.thumbnail ? (
                    <img
                      className="w-12 h-12 rounded-lg"
                      src={
                        file.thumbnail.startsWith("data:image")
                          ? file.thumbnail
                          : `data:image/png;base64,${file.thumbnail}`
                      }
                      alt={file.name}
                    />
                  ) : (
                    <div className="w-12 h-12 rounded-lg bg-white flex justify-center items-center">
                      <SvgTextfile width="25px" height="25px" />
                    </div>
                  )}
                  <div className="flex">{file.name}</div>
                </div>
                <div
                  onClick={() => handleDeleteFile(file.objectName)}
                  className="cursor-pointer"
                >
                  <SvgDelete width="17px" height="17px" />
                </div>
              </div>
            ))}
          </div>
        )}

        {Object.keys(selectedFiles).length > 0 && (
          <>
            <div
              className="flex flex-col gap-3 p-3 rounded-lg bg-[#EEF5FF]"
              key="files"
            >
              {Object.entries(selectedFiles).map(([fileName, file]) => (
                <div
                  className="flex items-center justify-between"
                  key={file.name}
                >
                  <div className="flex items-center gap-2">
                    {file?.imgUrl ? (
                      <img
                        className="w-12 h-12 rounded-lg"
                        src={file.imgUrl}
                        alt={file.name}
                      />
                    ) : (
                      <div className="w-12 h-12 rounded-lg bg-white flex justify-center items-center">
                        <SvgTextfile />
                      </div>
                    )}
                    <div className="flex">{fileName}</div>
                  </div>
                </div>
              ))}
            </div>
          </>
        )}
      </div>

      {/* Transaction Notes*/}
      <div className="flex flex-col border border-[#F4EEF9] rounded-2xl p-5 gap-4 text-xs font-medium">
        <label htmlFor="notes">Your Notes</label>
        <Textarea
          className="resize-none text-xs font-normal border-none p-0 focus:outline-none focus-visible:ring-offset-0 focus-visible:ring-0"
          id="notes"
          rows={5}
          placeholder="Please enter your notes here..."
          value={notes}
          onChange={(event) => {
            setNotes(event.target.value);
          }}
          onBlur={() => onNotesChanged(transaction.txnId, notes)}
        />
      </div>
    </div>
  );
}

export { TransactionDetails };
