syntax = "proto3";
package backend_services.user;

import "database/core.proto";

service User {
   // Initiate consent creation request flow
  rpc InitiateConsentRequestFlow(InitiateConsentFlowRequest) returns (InitiateConsentFlowResponse) {};

   // Create consent request
  rpc CreateConsent(CreateConsentRequest) returns (CreateConsentResponse) {};

  // Delete account
  rpc DeleteAccount(DeleteAccountRequest) returns (DeleteAccountResponse) {};
  
  // Handle request from AA Redirection
  rpc HandleAARedirectUrl(HandleAARedirectUrlRequest) returns (HandleAARedirectUrlResponse) {};

  // Get FIP details 
  rpc GetFIPDetails(GetFIPDetailsRequest) returns (GetFIPDetailsResponse){};

  // Get phone users
  rpc GetAccessibleUsers(GetAccessibleUsersRequest) returns (GetAccessibleUsersResponse) {};

  rpc HandleConsentStatusUpdate(HandleConsentStatusUpdateRequest) returns (HandleConsentStatusUpdateResponse) {};

  // Group related rpcs
  rpc CreateGroup(CreateGroupRequest) returns (CreateGroupResponse){};
  rpc GetAccessibleGroups(GetAccessibleGroupsRequest) returns (GetAccessibleGroupsResponse){};
  rpc AddUsersToGroup(AddUsersToGroupRequest) returns (AddUsersToGroupResponse){};
  rpc RemoveUsersFromGroup(RemoveUsersFromGroupRequest) returns (RemoveUsersFromGroupResponse){};
  rpc UpdateGroupName(UpdateGroupNameRequest) returns (UpdateGroupNameResponse){};
  rpc DeleteGroup(DeleteGroupRequest) returns (DeleteGroupResponse){};

  // Family related rpcs
  rpc AddFamilyMember(AddFamilyMemberRequest) returns (AddFamilyMemberResponse) {};
}


message InitiateConsentFlowRequest {
  database.FiType fi_type = 1; // Valid Values: DEPOSIT, EQUITIES, MUTUAL_FUNDS
  string consent_phone_number = 2; // Use this number for Consent Request Initiation
  string consent_pan = 3; // Required only when fi_type is EQUITIES, MUTUAL FUNDS
}
message InitiateConsentFlowResponse {
  string consent_request_url = 1;
  string consent_handle_id = 2;
  string aa_user_id = 3;
}

message CreateConsentRequest {
  database.FiType fi_type = 1; // Valid Values: DEPOSIT, EQUITIES, MUTUAL_FUNDS
  string consent_phone_number = 2; // Use this number for Consent Request Initiation
  string consent_pan = 3; // Required only when fi_type is EQUITIES, MUTUAL FUNDS
}
message CreateConsentResponse {
  string request_id = 1;
  string redirect_url = 2;
}


message DeleteAccountRequest{
  string account_id = 2; 
}
message DeleteAccountResponse{}

message HandleAARedirectUrlRequest{
  string ecres = 1;
  string fi = 2;
  string resdate = 3;
}
message HandleAARedirectUrlResponse{}


message GetFIPDetailsRequest{
  repeated string fip_id = 1;
}
message GetFIPDetailsResponse{
  repeated database.FipDetails fip_details = 1;
}

message GetAccessibleUsersRequest{}
message GetAccessibleUsersResponse{
  repeated database.User users = 1;
}

message HandleConsentStatusUpdateRequest {
  string consent_handle_id = 1;
}
message HandleConsentStatusUpdateResponse {}

message CreateGroupRequest {
  string name = 1;
  repeated string users = 2;
  string family_id = 3;
}
message CreateGroupResponse {
  string group_id = 1;
}

message GetAccessibleGroupsRequest {}
message GetAccessibleGroupsResponse {
  message Group {
    database.UserGroup group = 1;
    repeated database.User users = 2;
  }
  repeated Group groups = 1;
}

message AddUsersToGroupRequest {
  string group_id = 1;
  repeated string user_ids = 2; 
}
message AddUsersToGroupResponse {}

message RemoveUsersFromGroupRequest {
  string group_id = 1;
  repeated string user_ids = 2; 
}
message RemoveUsersFromGroupResponse {}

message UpdateGroupNameRequest {
  string group_id = 1; 
  string name = 2;
}
message UpdateGroupNameResponse {}

message DeleteGroupRequest {
  string group_id = 1;
}
message DeleteGroupResponse {}

message AddFamilyMemberRequest {
  string phone_number = 1;
  string nickname = 2;
  string email = 3;
  database.UserRelation relation = 4;
}

message AddFamilyMemberResponse {}