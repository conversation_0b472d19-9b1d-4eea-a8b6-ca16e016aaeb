syntax = "proto3";
package backend_services.visualization;

import "database/cards_filters.proto";
import "database/core.proto";
import "backend_services/visualization/shared.proto";

// Service for Wealth Widgets
service Wealth{
    rpc FetchWidgets(FetchWidgetsRequest) returns (stream FetchWidgetsResponse) {};
}

message FetchWidgetsRequest {
    database.UserGroupFilters user_groups = 1;
    database.AccountsFilter accounts_filter = 2;
}

message FetchWidgetsResponse {
    repeated Widget widgets = 1;
}

message Widget {
    oneof widget {
        TotalNetWorthWidget total_net_worth_widget = 1;
        UserNetWorthWidget user_net_worth_widget = 2;
        GroupNetWorthWidget group_net_worth_widget = 3;
        GroupSavingsWidget group_savings_widget = 4;
        UserSavingsWidget user_savings_widget = 5;
        InvestmentsWorthWidget investments_worth_widget = 6;
        CashflowWidget cashflow_widget = 7;
    }
}

enum WorthCategory {
    VALUE_CATEGORY_UNSPECIFIED = 0;
    VALUE_CATEGORY_SAVINGS = 1;
    VALUE_CATEGORY_INVESTMENTS = 2;
}
message TotalNetWorthWidget {
    double total_net_worth = 1;

    message CategoryWorth {
        double worth = 1;
        WorthCategory worth_category = 2;    
    }
    repeated CategoryWorth category_worths = 2;
}


message GroupSavingsWidget {
    double total_balance = 1;
    message GroupSavings {
        double balance = 1;
        double percentage = 2;
        string group_id = 3;
    }
    repeated GroupSavings savings = 2;
}

message UserSavingsWidget {
    double total_balance = 1;
    message UserSavings {
        double balance = 1;
        double percentage = 2;
        string user_id = 3;
    }
    repeated UserSavings savings = 2;
}

message InvestmentsWorthWidget {
    double total_value = 1;
    message InstrumentValue {
        double value = 1;
        database.FiType instrument = 2;
    }
    repeated InstrumentValue instrument_values = 2;
}

message UserNetWorthWidget {
    message UserNetWorth {
        double net_worth = 1;
        string user_id = 2;
    }
    repeated UserNetWorth net_worths = 1;
}

message GroupNetWorthWidget {
    message GroupNetWorth {
        double net_worth = 1;
        string group_id = 2;
    }
    repeated GroupNetWorth net_worths = 1;
}

message CashflowWidget {
    repeated OverallCashflow overall_cashflows = 1;
    repeated CategoryCashflow category_cashflows = 2;
}
