import * as grpc from "@grpc/grpc-js";
import {
  Account,
  Category,
  FetchDocuments,
  Subcategory,
  TransactionTypeEnum,
} from "@repo/ui";

export interface TransactionCategory {
  categoryCollection: string;
  categoryId: string;
  subcategoryId: string;
}

export interface TransactionMerchant {
  userId: string;
  merchantId: string;
  merchantName: string;
}

export interface Transaction {
  txnId: string;
  accountId: string;
  amount: number;
  type: TransactionTypeEnum;
  txnTimestamp: string;
  mode: string;
  narration: string;
  rawTxnId: string;
  favorite: boolean;
  tag: TransactionCategory;
  excludeCashFlow: boolean;
  merchant: TransactionMerchant;
  fipId: string;
  userNotes: string;
  cashFlowPeriod: {
    month: number;
    year: number;
  };
  documentsCount: number;
  account?: Account;
  category?: Category;
  subcategory?: Subcategory;
}

export interface GetTransactionResponse {
  cards: Transaction[];
}

export interface TransactionRequest {
  filter: {
    userGroups?: {
      userIds?: { id: string }[];
      userGroupIds?: { id: string }[];
    };
    accountFilters?: {
      accountIds: string[];
    };
    txnFilters?: {
      timeRange?: {
        fromTime?: string;
        toTime?: string;
      };
      amountRange?: {
        minAmount?: string;
        maxAmount?: string;
      };
      favorited?: { favorited: boolean };
      excludeCashFlow?: { excludeCashFlow: boolean };
      hasUserNotes?: { hasUserNotes: boolean };
      txnType?: { txnType: string };
      untagged?: { untagged: boolean };
      category?: Array<{
        categoryCollection: string;
        categoryId: string;
        subcategoryId: string;
      }>;
    };
  };
  paginationParams: {
    pageSize: number;
    pageNumber: number;
  };
}

export interface TransactionFilters {
  userGroups?: {
    userIds: { id: string }[];
    userGroupIds: { id: string }[];
  };
  accountFilters?: { accountIds?: string[] };
  timeRange?: { fromTime: number; toTime: number };
  amountRange?: { minAmount: number; maxAmount: number };
  transactionType?: string;
  tagStatus?: string;
  bookmarkOptions?: {
    showFavorites?: boolean;
    excludedFromCashflow?: boolean;
    withNotes?: boolean;
  };
  categories?: Array<TransactionCategory>;
}

export type DepositTransactionClient = grpc.Client & {
  FetchDepositTxns: (
    request: object,
    metadata: grpc.Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: GetTransactionResponse,
    ) => void,
  ) => void;
  MarkDepositTxnFavorite: (
    request: {
      transactionId: string;
      favorite: boolean;
    },
    metadata: grpc.Metadata,
    callback: (error: grpc.ServiceError | null, response: object) => void,
  ) => void;
  ExcludeTxnFromCashFlow: (
    request: {
      txnId: string;
      excludeCashFlow: boolean;
    },
    metadata: grpc.Metadata,
    callback: (error: grpc.ServiceError | null, response: object) => void,
  ) => void;
  UpdateTransactionNotes: (
    request: {
      transactionId: string;
      notes: string;
    },
    metadata: grpc.Metadata,
    callback: (error: grpc.ServiceError | null, response: object) => void,
  ) => void;
  FetchTransactionDocuments: (
    request: {
      transactionId: string;
    },
    metadata: grpc.Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: FetchDocuments,
    ) => void,
  ) => void;
  DeleteTransactionDocuments: (
    request: {
      transactionId: string;
      objectNames: string[];
    },
    metadata: grpc.Metadata,
    callback: (error: grpc.ServiceError | null, response: object) => void,
  ) => void;
  AssignCategoryToDepositTxns: (
    request: {
      transactionIds: string[];
      categoryId: string;
      collection: string;
      subcategoryId: string;
    },
    metadata: grpc.Metadata,
    callback: (error: grpc.ServiceError | null, response: object) => void,
  ) => void;
  SearchTxns: (
    request: {
      input: string;
      searchBy: SearchBy;
    },
    metadata: grpc.Metadata,
    callback: (error: grpc.ServiceError | null, response: object) => void,
  ) => void;
};
export enum SearchBy {
  SEARCH_BY_ALL = 0,
  SEARCH_BY_TAG = 1,
  SEARCH_BY_MERCHANT = 2,
  SEARCH_BY_USER = 3,
  SEARCH_BY_NOTES = 4,
  SEARCH_BY_AMOUNT = 5,
  SEARCH_BY_REMARKS = 6,
}