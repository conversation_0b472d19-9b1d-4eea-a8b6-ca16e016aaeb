"use client";
import { useState } from "react";
import { Button, Checkbox } from "@repo/ui";

function HeroSection(): JSX.Element {
  const [isAccepted, setIsAccepted] = useState(false);

  return (
    <div className="container center">
      <h1 className="text-4xl font-semibold">
        Hello World!{" "}
        {isAccepted && (
          <span className="text-blue-400 font-extrabold ">
            Welcome to CuspWeb
          </span>
        )}
      </h1>
      <div>
        <Checkbox
          id="terms"
          checked={isAccepted}
          onCheckedChange={() => setIsAccepted(true)}
          disabled={isAccepted}
        />
        <label htmlFor="terms" className="mx-4">
          Accept terms and conditions
        </label>
        {isAccepted && (
          <>
            <Button onClick={() => setIsAccepted(false)}>reset</Button>
          </>
        )}
      </div>
    </div>
  );
}

export default HeroSection;
