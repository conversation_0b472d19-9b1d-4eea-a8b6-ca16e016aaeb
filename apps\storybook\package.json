{"name": "storybook", "private": true, "version": "0.0.0", "scripts": {"dev": "storybook dev -p 6006", "build": "storybook build", "lint": "eslint . --report-unused-disable-directives --max-warnings 0", "build-storybook": "storybook build", "chromatic": "chromatic"}, "dependencies": {"@repo/ui": "workspace:*", "react": "^18.3.1", "react-day-picker": "8.10.1", "react-dom": "^18.3.1"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/tailwind-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@storybook/addon-essentials": "^8.6.9", "@storybook/addon-interactions": "^8.6.9", "@storybook/addon-links": "^8.6.9", "@storybook/addon-styling-webpack": "^1.0.1", "@storybook/blocks": "^8.6.9", "@storybook/cli": "^8.6.9", "@storybook/react": "^8.6.9", "@storybook/react-vite": "^8.6.9", "@storybook/test": "^8.6.9", "@types/node": "^22.10.2", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "chromatic": "^11.25.1", "postcss": "^8.5.1", "storybook": "^8.6.9", "lucide-react": "0.468.0", "tailwindcss": "^3.4.17", "typescript": "^5.7.3", "vite": "^6.0.1"}}