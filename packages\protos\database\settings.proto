syntax = "proto3";
package database;

import "database/custom_type.proto";

message Feedback {
  string feature_id = 1; // Feature or Question identifier
  string feedback_id = 2; // Client-generated random identifier
  string notes = 3; // Typed-in content of user
  int32 experience_rating = 4; // Experience rating
  bool helpful = 5; // Whether user found it helpful or not
}

// flag:is-collection=true;
message UserFeedback {
  ObjectId _id = 1;
  string phone_number = 2;
  Feedback Feedback = 3;
  int64 created_at = 4;
}
