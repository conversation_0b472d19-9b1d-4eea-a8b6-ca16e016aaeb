syntax = "proto3";
package backend_services.data_access;
import "database/fi_mutual_funds.proto";

service FiMutualFundsTransaction {
    rpc UpsertMutualFundsAccountsTransactions(UpsertMutualFundsAccountsTransactionsRequest) returns (UpsertMutualFundsAccountsTransactionsResponse) {};
    rpc GetMutualFundsTransactions(GetMutualFundsTransactionsRequest) returns (GetMutualFundsTransactionsResponse) {};
    rpc DeleteFiMutualFundsAccountsTransactions(DeleteFiMutualFundsAccountsTransactionsRequest) returns (DeleteFiMutualFundsAccountsTransactionsResponse) {};
    rpc CopyFiMutualFundsAccountsTxnsToDeleteCol(CopyFiMutualFundsAccountsTxnsToDeleteColRequest) returns (CopyFiMutualFundsAccountsTxnsToDeleteColResponse) {};
    rpc FindAnyMutualFundsTxnMatchingIds(FindAnyMutualFundsTxnMatchingIdsRequest) returns (FindAnyMutualFundsTxnMatchingIdsResponse) {};
}

message UpsertMutualFundsAccountsTransactionsRequest {
   repeated database.FiMutualFundsAccountTransaction records = 1;
}

message UpsertMutualFundsAccountsTransactionsResponse {
	map<int64, string> inserted_docs_index_to_id_map = 1;
}


message GetMutualFundsTransactionsRequest {
    string account_id = 1;
    int64 from = 2;
}

message GetMutualFundsTransactionsResponse {
    repeated database.FiMutualFundsAccountTransaction records = 1;
}

message CopyFiMutualFundsAccountsTxnsToDeleteColRequest{
	repeated string account_ids = 1;
}

message CopyFiMutualFundsAccountsTxnsToDeleteColResponse {}

message DeleteFiMutualFundsAccountsTransactionsRequest{
	repeated string account_ids = 1;
}

message DeleteFiMutualFundsAccountsTransactionsResponse{}

message FindAnyMutualFundsTxnMatchingIdsRequest{
    string account_id = 1;
    repeated string transaction_ids = 2;
}

message FindAnyMutualFundsTxnMatchingIdsResponse{
    database.FiMutualFundsAccountTransaction record = 1;
}
