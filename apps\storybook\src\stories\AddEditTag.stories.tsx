import { AddEditTag, debitCategories } from "@repo/ui";
import type { Meta, StoryObj } from "@storybook/react";

const meta: Meta<typeof AddEditTag> = {
  title: "Components/AddEditTag",
  component: AddEditTag,
  tags: ["autodocs"],
  args: {
    categories: debitCategories,
    onSelectCategory: (category) => {
      console.log(category);
    },
    transaction: {
      entity: "Cash",
      amount: 6883.0,
      date: new Date("2025/05/07"),
      narration: "UPI IN / 032613645945 / 9975089217 @ upi / NO REMARKS",
    },
  },
  decorators: [
    (Story) => (
      <div style={{ width: "500px" }}>
        <Story />
      </div>
    ),
  ],
};

export default meta;

type Story = StoryObj<typeof AddEditTag>;

export const Default: Story = {};

export const WithSelectedCategory: Story = {
  args: {
    selectedCategory: debitCategories[0],
    transaction: {
      entity: "Cash",
      amount: 6883.0,
      date: new Date("2025/05/07"),
      narration: "UPI IN / 032613645945 / 9975089217 @ upi / NO REMARKS",
      tag: {
        category_collection: "",
        category_id: "67c8025059c3c908d42c61a7",
        subcategory_id: "67c8025059c3c908d42c61cf",
      },
    },
  },
};
