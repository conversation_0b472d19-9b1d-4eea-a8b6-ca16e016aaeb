# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
.pnp
.pnp.js
dist
dist-ssr
*.local

# testing
coverage

# next.js
.next/
out/
build
.swc/

# debug
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Ignore lock files for npm and Yarn
package-lock.json
yarn.lock

# turbo
.turbo

# build
storybook-static

# Editor directories and files
.vscode
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
*.pem
