import { DateFilter } from "@repo/ui";
import type { <PERSON>a, StoryObj } from "@storybook/react";

const meta: Meta<typeof DateFilter> = {
  title: "Filters/DateFilter",
  component: DateFilter,
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <div className="bg-white border rounded-md md:w-[28rem] p-3">
        <Story />
      </div>
    ),
  ],
};

export default meta;

type Story = StoryObj<typeof DateFilter>;

export const Default: Story = {
  args: {
    onFilterChange: (filters) => {
      console.log("Filters:", filters);
    },
  },
};
