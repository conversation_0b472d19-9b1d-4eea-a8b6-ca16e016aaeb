// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.2
//   protoc               v6.31.1
// source: database/core.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { ObjectId } from "./custom_type";

export const protobufPackage = "database";

export enum ConsentStatus {
  CONSENT_STATUS_UNSPECIFIED = 0,
  CONSENT_STATUS_PENDING = 1,
  CONSENT_STATUS_ACTIVE = 2,
  CONSENT_STATUS_REVOKED = 3,
  CONSENT_STATUS_EXPIRED = 4,
  CONSENT_STATUS_REJECTED = 5,
  UNRECOGNIZED = -1,
}

export function consentStatusFromJSON(object: any): ConsentStatus {
  switch (object) {
    case 0:
    case "CONSENT_STATUS_UNSPECIFIED":
      return ConsentStatus.CONSENT_STATUS_UNSPECIFIED;
    case 1:
    case "CONSENT_STATUS_PENDING":
      return ConsentStatus.CONSENT_STATUS_PENDING;
    case 2:
    case "CONSENT_STATUS_ACTIVE":
      return ConsentStatus.CONSENT_STATUS_ACTIVE;
    case 3:
    case "CONSENT_STATUS_REVOKED":
      return ConsentStatus.CONSENT_STATUS_REVOKED;
    case 4:
    case "CONSENT_STATUS_EXPIRED":
      return ConsentStatus.CONSENT_STATUS_EXPIRED;
    case 5:
    case "CONSENT_STATUS_REJECTED":
      return ConsentStatus.CONSENT_STATUS_REJECTED;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ConsentStatus.UNRECOGNIZED;
  }
}

export function consentStatusToJSON(object: ConsentStatus): string {
  switch (object) {
    case ConsentStatus.CONSENT_STATUS_UNSPECIFIED:
      return "CONSENT_STATUS_UNSPECIFIED";
    case ConsentStatus.CONSENT_STATUS_PENDING:
      return "CONSENT_STATUS_PENDING";
    case ConsentStatus.CONSENT_STATUS_ACTIVE:
      return "CONSENT_STATUS_ACTIVE";
    case ConsentStatus.CONSENT_STATUS_REVOKED:
      return "CONSENT_STATUS_REVOKED";
    case ConsentStatus.CONSENT_STATUS_EXPIRED:
      return "CONSENT_STATUS_EXPIRED";
    case ConsentStatus.CONSENT_STATUS_REJECTED:
      return "CONSENT_STATUS_REJECTED";
    case ConsentStatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum FiType {
  UNSPECIFIED = 0,
  DEPOSIT = 1,
  EQUITY = 2,
  MUTUAL_FUND = 3,
  UNRECOGNIZED = -1,
}

export function fiTypeFromJSON(object: any): FiType {
  switch (object) {
    case 0:
    case "UNSPECIFIED":
      return FiType.UNSPECIFIED;
    case 1:
    case "DEPOSIT":
      return FiType.DEPOSIT;
    case 2:
    case "EQUITY":
      return FiType.EQUITY;
    case 3:
    case "MUTUAL_FUND":
      return FiType.MUTUAL_FUND;
    case -1:
    case "UNRECOGNIZED":
    default:
      return FiType.UNRECOGNIZED;
  }
}

export function fiTypeToJSON(object: FiType): string {
  switch (object) {
    case FiType.UNSPECIFIED:
      return "UNSPECIFIED";
    case FiType.DEPOSIT:
      return "DEPOSIT";
    case FiType.EQUITY:
      return "EQUITY";
    case FiType.MUTUAL_FUND:
      return "MUTUAL_FUND";
    case FiType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum UserRelation {
  USER_RELATION_UNSPECIFIED = 0,
  USER_RELATION_SELF = 1,
  USER_RELATION_WIFE = 2,
  USER_RELATION_SPOUSE = 3,
  USER_RELATION_SON = 4,
  USER_RELATION_DAUGHTER = 5,
  USER_RELATION_SIBLING = 6,
  USER_RELATION_PARENT = 7,
  USER_RELATION_GRAND_PARENTS = 8,
  UNRECOGNIZED = -1,
}

export function userRelationFromJSON(object: any): UserRelation {
  switch (object) {
    case 0:
    case "USER_RELATION_UNSPECIFIED":
      return UserRelation.USER_RELATION_UNSPECIFIED;
    case 1:
    case "USER_RELATION_SELF":
      return UserRelation.USER_RELATION_SELF;
    case 2:
    case "USER_RELATION_WIFE":
      return UserRelation.USER_RELATION_WIFE;
    case 3:
    case "USER_RELATION_SPOUSE":
      return UserRelation.USER_RELATION_SPOUSE;
    case 4:
    case "USER_RELATION_SON":
      return UserRelation.USER_RELATION_SON;
    case 5:
    case "USER_RELATION_DAUGHTER":
      return UserRelation.USER_RELATION_DAUGHTER;
    case 6:
    case "USER_RELATION_SIBLING":
      return UserRelation.USER_RELATION_SIBLING;
    case 7:
    case "USER_RELATION_PARENT":
      return UserRelation.USER_RELATION_PARENT;
    case 8:
    case "USER_RELATION_GRAND_PARENTS":
      return UserRelation.USER_RELATION_GRAND_PARENTS;
    case -1:
    case "UNRECOGNIZED":
    default:
      return UserRelation.UNRECOGNIZED;
  }
}

export function userRelationToJSON(object: UserRelation): string {
  switch (object) {
    case UserRelation.USER_RELATION_UNSPECIFIED:
      return "USER_RELATION_UNSPECIFIED";
    case UserRelation.USER_RELATION_SELF:
      return "USER_RELATION_SELF";
    case UserRelation.USER_RELATION_WIFE:
      return "USER_RELATION_WIFE";
    case UserRelation.USER_RELATION_SPOUSE:
      return "USER_RELATION_SPOUSE";
    case UserRelation.USER_RELATION_SON:
      return "USER_RELATION_SON";
    case UserRelation.USER_RELATION_DAUGHTER:
      return "USER_RELATION_DAUGHTER";
    case UserRelation.USER_RELATION_SIBLING:
      return "USER_RELATION_SIBLING";
    case UserRelation.USER_RELATION_PARENT:
      return "USER_RELATION_PARENT";
    case UserRelation.USER_RELATION_GRAND_PARENTS:
      return "USER_RELATION_GRAND_PARENTS";
    case UserRelation.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** flag:is-collection=true; */
export interface User {
  Id: ObjectId | undefined;
  phoneNumber: string;
  pan: string;
  name: string;
  address: string;
  dateOfBirth: string;
  alternatePhoneNumbers: string[];
  familyId: string;
  groupId: ObjectId | undefined;
  isPrimary: boolean;
  lastLoginTime: number;
  createdAt: number;
  updatedAt: number;
  /** Nickname provided by primary user. This is different from holder name which is received during FI fetch */
  nickname: string;
  email: string;
  /** Relation to the primary user. */
  relation: UserRelation;
}

/** flag:is-collection=true; */
export interface UserGroup {
  Id: ObjectId | undefined;
  name: string;
  familyId: string;
  createdAt: number;
  updatedAt: number;
}

/** flag:is-collection=true; */
export interface Consent {
  Id: ObjectId | undefined;
  parentPhoneNumber: string;
  consentPhoneNumber: string;
  consentHandle: string;
  consentId: string;
  accounts: ConsentedAccount[];
  customerId: string;
  fiType: FiType;
  consentStart: number;
  consentExpiry: number;
  dataRangeFrom: number;
  dataRangeTo: number;
  isDataProcessedOnce: boolean;
  createdAt: number;
  updatedAt: number;
  sessionId: string;
  dataProcessedCheckpointTimestamp: number;
  consentRequestId: string;
}

/** flag:is-collection=true; */
export interface ConsentedAccount {
  fipId: string;
  linkedAccRef: string;
  maskedAccNumber: string;
  /** Account deleted by user before FI fetch */
  isDeleted: boolean;
}

/** flag:is-collection=true; */
export interface FipDetails {
  Id: ObjectId | undefined;
  fipId: string;
  fipName: string;
  fiTypes: FiType[];
  entityIconUri: string;
  entityLogoUri: string;
  entityLogoWithNameUri: string;
  /** TODO: Confirm type as value null in Sandbox */
  code: string;
  enable: string;
  otpLength: number;
  createdAt: number;
  updatedAt: number;
}

/** flag:is-collection=true; */
export interface UserAccount {
  Id: ObjectId | undefined;
  userId: ObjectId | undefined;
  fiType: FiType;
  familyId: string;
  consentId: string;
  linkedAccRef: string;
  accountId: ObjectId | undefined;
  createdAt: number;
  updatedAt: number;
}

/** flag:is-collection=true; */
export interface DeletedAccountLog {
  Id: ObjectId | undefined;
  fiType: FiType;
  accountId: ObjectId | undefined;
  createdAt: number;
}

function createBaseUser(): User {
  return {
    Id: undefined,
    phoneNumber: "",
    pan: "",
    name: "",
    address: "",
    dateOfBirth: "",
    alternatePhoneNumbers: [],
    familyId: "",
    groupId: undefined,
    isPrimary: false,
    lastLoginTime: 0,
    createdAt: 0,
    updatedAt: 0,
    nickname: "",
    email: "",
    relation: 0,
  };
}

export const User: MessageFns<User> = {
  encode(message: User, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.Id !== undefined) {
      ObjectId.encode(message.Id, writer.uint32(10).fork()).join();
    }
    if (message.phoneNumber !== "") {
      writer.uint32(18).string(message.phoneNumber);
    }
    if (message.pan !== "") {
      writer.uint32(26).string(message.pan);
    }
    if (message.name !== "") {
      writer.uint32(34).string(message.name);
    }
    if (message.address !== "") {
      writer.uint32(42).string(message.address);
    }
    if (message.dateOfBirth !== "") {
      writer.uint32(50).string(message.dateOfBirth);
    }
    for (const v of message.alternatePhoneNumbers) {
      writer.uint32(58).string(v!);
    }
    if (message.familyId !== "") {
      writer.uint32(66).string(message.familyId);
    }
    if (message.groupId !== undefined) {
      ObjectId.encode(message.groupId, writer.uint32(74).fork()).join();
    }
    if (message.isPrimary !== false) {
      writer.uint32(80).bool(message.isPrimary);
    }
    if (message.lastLoginTime !== 0) {
      writer.uint32(88).int64(message.lastLoginTime);
    }
    if (message.createdAt !== 0) {
      writer.uint32(96).int64(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      writer.uint32(104).int64(message.updatedAt);
    }
    if (message.nickname !== "") {
      writer.uint32(114).string(message.nickname);
    }
    if (message.email !== "") {
      writer.uint32(122).string(message.email);
    }
    if (message.relation !== 0) {
      writer.uint32(128).int32(message.relation);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): User {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUser();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.Id = ObjectId.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.phoneNumber = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.pan = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.address = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.dateOfBirth = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.alternatePhoneNumbers.push(reader.string());
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.familyId = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.groupId = ObjectId.decode(reader, reader.uint32());
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.isPrimary = reader.bool();
          continue;
        }
        case 11: {
          if (tag !== 88) {
            break;
          }

          message.lastLoginTime = longToNumber(reader.int64());
          continue;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.createdAt = longToNumber(reader.int64());
          continue;
        }
        case 13: {
          if (tag !== 104) {
            break;
          }

          message.updatedAt = longToNumber(reader.int64());
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          message.nickname = reader.string();
          continue;
        }
        case 15: {
          if (tag !== 122) {
            break;
          }

          message.email = reader.string();
          continue;
        }
        case 16: {
          if (tag !== 128) {
            break;
          }

          message.relation = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): User {
    return {
      Id: isSet(object.Id) ? ObjectId.fromJSON(object.Id) : undefined,
      phoneNumber: isSet(object.phoneNumber) ? globalThis.String(object.phoneNumber) : "",
      pan: isSet(object.pan) ? globalThis.String(object.pan) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      address: isSet(object.address) ? globalThis.String(object.address) : "",
      dateOfBirth: isSet(object.dateOfBirth) ? globalThis.String(object.dateOfBirth) : "",
      alternatePhoneNumbers: globalThis.Array.isArray(object?.alternatePhoneNumbers)
        ? object.alternatePhoneNumbers.map((e: any) => globalThis.String(e))
        : [],
      familyId: isSet(object.familyId) ? globalThis.String(object.familyId) : "",
      groupId: isSet(object.groupId) ? ObjectId.fromJSON(object.groupId) : undefined,
      isPrimary: isSet(object.isPrimary) ? globalThis.Boolean(object.isPrimary) : false,
      lastLoginTime: isSet(object.lastLoginTime) ? globalThis.Number(object.lastLoginTime) : 0,
      createdAt: isSet(object.createdAt) ? globalThis.Number(object.createdAt) : 0,
      updatedAt: isSet(object.updatedAt) ? globalThis.Number(object.updatedAt) : 0,
      nickname: isSet(object.nickname) ? globalThis.String(object.nickname) : "",
      email: isSet(object.email) ? globalThis.String(object.email) : "",
      relation: isSet(object.relation) ? userRelationFromJSON(object.relation) : 0,
    };
  },

  toJSON(message: User): unknown {
    const obj: any = {};
    if (message.Id !== undefined) {
      obj.Id = ObjectId.toJSON(message.Id);
    }
    if (message.phoneNumber !== "") {
      obj.phoneNumber = message.phoneNumber;
    }
    if (message.pan !== "") {
      obj.pan = message.pan;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.address !== "") {
      obj.address = message.address;
    }
    if (message.dateOfBirth !== "") {
      obj.dateOfBirth = message.dateOfBirth;
    }
    if (message.alternatePhoneNumbers?.length) {
      obj.alternatePhoneNumbers = message.alternatePhoneNumbers;
    }
    if (message.familyId !== "") {
      obj.familyId = message.familyId;
    }
    if (message.groupId !== undefined) {
      obj.groupId = ObjectId.toJSON(message.groupId);
    }
    if (message.isPrimary !== false) {
      obj.isPrimary = message.isPrimary;
    }
    if (message.lastLoginTime !== 0) {
      obj.lastLoginTime = Math.round(message.lastLoginTime);
    }
    if (message.createdAt !== 0) {
      obj.createdAt = Math.round(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      obj.updatedAt = Math.round(message.updatedAt);
    }
    if (message.nickname !== "") {
      obj.nickname = message.nickname;
    }
    if (message.email !== "") {
      obj.email = message.email;
    }
    if (message.relation !== 0) {
      obj.relation = userRelationToJSON(message.relation);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<User>, I>>(base?: I): User {
    return User.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<User>, I>>(object: I): User {
    const message = createBaseUser();
    message.Id = (object.Id !== undefined && object.Id !== null) ? ObjectId.fromPartial(object.Id) : undefined;
    message.phoneNumber = object.phoneNumber ?? "";
    message.pan = object.pan ?? "";
    message.name = object.name ?? "";
    message.address = object.address ?? "";
    message.dateOfBirth = object.dateOfBirth ?? "";
    message.alternatePhoneNumbers = object.alternatePhoneNumbers?.map((e) => e) || [];
    message.familyId = object.familyId ?? "";
    message.groupId = (object.groupId !== undefined && object.groupId !== null)
      ? ObjectId.fromPartial(object.groupId)
      : undefined;
    message.isPrimary = object.isPrimary ?? false;
    message.lastLoginTime = object.lastLoginTime ?? 0;
    message.createdAt = object.createdAt ?? 0;
    message.updatedAt = object.updatedAt ?? 0;
    message.nickname = object.nickname ?? "";
    message.email = object.email ?? "";
    message.relation = object.relation ?? 0;
    return message;
  },
};

function createBaseUserGroup(): UserGroup {
  return { Id: undefined, name: "", familyId: "", createdAt: 0, updatedAt: 0 };
}

export const UserGroup: MessageFns<UserGroup> = {
  encode(message: UserGroup, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.Id !== undefined) {
      ObjectId.encode(message.Id, writer.uint32(10).fork()).join();
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.familyId !== "") {
      writer.uint32(26).string(message.familyId);
    }
    if (message.createdAt !== 0) {
      writer.uint32(808).int64(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      writer.uint32(816).int64(message.updatedAt);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserGroup {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserGroup();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.Id = ObjectId.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.familyId = reader.string();
          continue;
        }
        case 101: {
          if (tag !== 808) {
            break;
          }

          message.createdAt = longToNumber(reader.int64());
          continue;
        }
        case 102: {
          if (tag !== 816) {
            break;
          }

          message.updatedAt = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserGroup {
    return {
      Id: isSet(object.Id) ? ObjectId.fromJSON(object.Id) : undefined,
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      familyId: isSet(object.familyId) ? globalThis.String(object.familyId) : "",
      createdAt: isSet(object.createdAt) ? globalThis.Number(object.createdAt) : 0,
      updatedAt: isSet(object.updatedAt) ? globalThis.Number(object.updatedAt) : 0,
    };
  },

  toJSON(message: UserGroup): unknown {
    const obj: any = {};
    if (message.Id !== undefined) {
      obj.Id = ObjectId.toJSON(message.Id);
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.familyId !== "") {
      obj.familyId = message.familyId;
    }
    if (message.createdAt !== 0) {
      obj.createdAt = Math.round(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      obj.updatedAt = Math.round(message.updatedAt);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserGroup>, I>>(base?: I): UserGroup {
    return UserGroup.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserGroup>, I>>(object: I): UserGroup {
    const message = createBaseUserGroup();
    message.Id = (object.Id !== undefined && object.Id !== null) ? ObjectId.fromPartial(object.Id) : undefined;
    message.name = object.name ?? "";
    message.familyId = object.familyId ?? "";
    message.createdAt = object.createdAt ?? 0;
    message.updatedAt = object.updatedAt ?? 0;
    return message;
  },
};

function createBaseConsent(): Consent {
  return {
    Id: undefined,
    parentPhoneNumber: "",
    consentPhoneNumber: "",
    consentHandle: "",
    consentId: "",
    accounts: [],
    customerId: "",
    fiType: 0,
    consentStart: 0,
    consentExpiry: 0,
    dataRangeFrom: 0,
    dataRangeTo: 0,
    isDataProcessedOnce: false,
    createdAt: 0,
    updatedAt: 0,
    sessionId: "",
    dataProcessedCheckpointTimestamp: 0,
    consentRequestId: "",
  };
}

export const Consent: MessageFns<Consent> = {
  encode(message: Consent, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.Id !== undefined) {
      ObjectId.encode(message.Id, writer.uint32(10).fork()).join();
    }
    if (message.parentPhoneNumber !== "") {
      writer.uint32(18).string(message.parentPhoneNumber);
    }
    if (message.consentPhoneNumber !== "") {
      writer.uint32(26).string(message.consentPhoneNumber);
    }
    if (message.consentHandle !== "") {
      writer.uint32(34).string(message.consentHandle);
    }
    if (message.consentId !== "") {
      writer.uint32(42).string(message.consentId);
    }
    for (const v of message.accounts) {
      ConsentedAccount.encode(v!, writer.uint32(50).fork()).join();
    }
    if (message.customerId !== "") {
      writer.uint32(58).string(message.customerId);
    }
    if (message.fiType !== 0) {
      writer.uint32(64).int32(message.fiType);
    }
    if (message.consentStart !== 0) {
      writer.uint32(72).int64(message.consentStart);
    }
    if (message.consentExpiry !== 0) {
      writer.uint32(80).int64(message.consentExpiry);
    }
    if (message.dataRangeFrom !== 0) {
      writer.uint32(88).int64(message.dataRangeFrom);
    }
    if (message.dataRangeTo !== 0) {
      writer.uint32(96).int64(message.dataRangeTo);
    }
    if (message.isDataProcessedOnce !== false) {
      writer.uint32(104).bool(message.isDataProcessedOnce);
    }
    if (message.createdAt !== 0) {
      writer.uint32(112).int64(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      writer.uint32(120).int64(message.updatedAt);
    }
    if (message.sessionId !== "") {
      writer.uint32(130).string(message.sessionId);
    }
    if (message.dataProcessedCheckpointTimestamp !== 0) {
      writer.uint32(136).int64(message.dataProcessedCheckpointTimestamp);
    }
    if (message.consentRequestId !== "") {
      writer.uint32(146).string(message.consentRequestId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Consent {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseConsent();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.Id = ObjectId.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.parentPhoneNumber = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.consentPhoneNumber = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.consentHandle = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.consentId = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.accounts.push(ConsentedAccount.decode(reader, reader.uint32()));
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.customerId = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.fiType = reader.int32() as any;
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.consentStart = longToNumber(reader.int64());
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.consentExpiry = longToNumber(reader.int64());
          continue;
        }
        case 11: {
          if (tag !== 88) {
            break;
          }

          message.dataRangeFrom = longToNumber(reader.int64());
          continue;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.dataRangeTo = longToNumber(reader.int64());
          continue;
        }
        case 13: {
          if (tag !== 104) {
            break;
          }

          message.isDataProcessedOnce = reader.bool();
          continue;
        }
        case 14: {
          if (tag !== 112) {
            break;
          }

          message.createdAt = longToNumber(reader.int64());
          continue;
        }
        case 15: {
          if (tag !== 120) {
            break;
          }

          message.updatedAt = longToNumber(reader.int64());
          continue;
        }
        case 16: {
          if (tag !== 130) {
            break;
          }

          message.sessionId = reader.string();
          continue;
        }
        case 17: {
          if (tag !== 136) {
            break;
          }

          message.dataProcessedCheckpointTimestamp = longToNumber(reader.int64());
          continue;
        }
        case 18: {
          if (tag !== 146) {
            break;
          }

          message.consentRequestId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Consent {
    return {
      Id: isSet(object.Id) ? ObjectId.fromJSON(object.Id) : undefined,
      parentPhoneNumber: isSet(object.parentPhoneNumber) ? globalThis.String(object.parentPhoneNumber) : "",
      consentPhoneNumber: isSet(object.consentPhoneNumber) ? globalThis.String(object.consentPhoneNumber) : "",
      consentHandle: isSet(object.consentHandle) ? globalThis.String(object.consentHandle) : "",
      consentId: isSet(object.consentId) ? globalThis.String(object.consentId) : "",
      accounts: globalThis.Array.isArray(object?.accounts)
        ? object.accounts.map((e: any) => ConsentedAccount.fromJSON(e))
        : [],
      customerId: isSet(object.customerId) ? globalThis.String(object.customerId) : "",
      fiType: isSet(object.fiType) ? fiTypeFromJSON(object.fiType) : 0,
      consentStart: isSet(object.consentStart) ? globalThis.Number(object.consentStart) : 0,
      consentExpiry: isSet(object.consentExpiry) ? globalThis.Number(object.consentExpiry) : 0,
      dataRangeFrom: isSet(object.dataRangeFrom) ? globalThis.Number(object.dataRangeFrom) : 0,
      dataRangeTo: isSet(object.dataRangeTo) ? globalThis.Number(object.dataRangeTo) : 0,
      isDataProcessedOnce: isSet(object.isDataProcessedOnce) ? globalThis.Boolean(object.isDataProcessedOnce) : false,
      createdAt: isSet(object.createdAt) ? globalThis.Number(object.createdAt) : 0,
      updatedAt: isSet(object.updatedAt) ? globalThis.Number(object.updatedAt) : 0,
      sessionId: isSet(object.sessionId) ? globalThis.String(object.sessionId) : "",
      dataProcessedCheckpointTimestamp: isSet(object.dataProcessedCheckpointTimestamp)
        ? globalThis.Number(object.dataProcessedCheckpointTimestamp)
        : 0,
      consentRequestId: isSet(object.consentRequestId) ? globalThis.String(object.consentRequestId) : "",
    };
  },

  toJSON(message: Consent): unknown {
    const obj: any = {};
    if (message.Id !== undefined) {
      obj.Id = ObjectId.toJSON(message.Id);
    }
    if (message.parentPhoneNumber !== "") {
      obj.parentPhoneNumber = message.parentPhoneNumber;
    }
    if (message.consentPhoneNumber !== "") {
      obj.consentPhoneNumber = message.consentPhoneNumber;
    }
    if (message.consentHandle !== "") {
      obj.consentHandle = message.consentHandle;
    }
    if (message.consentId !== "") {
      obj.consentId = message.consentId;
    }
    if (message.accounts?.length) {
      obj.accounts = message.accounts.map((e) => ConsentedAccount.toJSON(e));
    }
    if (message.customerId !== "") {
      obj.customerId = message.customerId;
    }
    if (message.fiType !== 0) {
      obj.fiType = fiTypeToJSON(message.fiType);
    }
    if (message.consentStart !== 0) {
      obj.consentStart = Math.round(message.consentStart);
    }
    if (message.consentExpiry !== 0) {
      obj.consentExpiry = Math.round(message.consentExpiry);
    }
    if (message.dataRangeFrom !== 0) {
      obj.dataRangeFrom = Math.round(message.dataRangeFrom);
    }
    if (message.dataRangeTo !== 0) {
      obj.dataRangeTo = Math.round(message.dataRangeTo);
    }
    if (message.isDataProcessedOnce !== false) {
      obj.isDataProcessedOnce = message.isDataProcessedOnce;
    }
    if (message.createdAt !== 0) {
      obj.createdAt = Math.round(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      obj.updatedAt = Math.round(message.updatedAt);
    }
    if (message.sessionId !== "") {
      obj.sessionId = message.sessionId;
    }
    if (message.dataProcessedCheckpointTimestamp !== 0) {
      obj.dataProcessedCheckpointTimestamp = Math.round(message.dataProcessedCheckpointTimestamp);
    }
    if (message.consentRequestId !== "") {
      obj.consentRequestId = message.consentRequestId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Consent>, I>>(base?: I): Consent {
    return Consent.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Consent>, I>>(object: I): Consent {
    const message = createBaseConsent();
    message.Id = (object.Id !== undefined && object.Id !== null) ? ObjectId.fromPartial(object.Id) : undefined;
    message.parentPhoneNumber = object.parentPhoneNumber ?? "";
    message.consentPhoneNumber = object.consentPhoneNumber ?? "";
    message.consentHandle = object.consentHandle ?? "";
    message.consentId = object.consentId ?? "";
    message.accounts = object.accounts?.map((e) => ConsentedAccount.fromPartial(e)) || [];
    message.customerId = object.customerId ?? "";
    message.fiType = object.fiType ?? 0;
    message.consentStart = object.consentStart ?? 0;
    message.consentExpiry = object.consentExpiry ?? 0;
    message.dataRangeFrom = object.dataRangeFrom ?? 0;
    message.dataRangeTo = object.dataRangeTo ?? 0;
    message.isDataProcessedOnce = object.isDataProcessedOnce ?? false;
    message.createdAt = object.createdAt ?? 0;
    message.updatedAt = object.updatedAt ?? 0;
    message.sessionId = object.sessionId ?? "";
    message.dataProcessedCheckpointTimestamp = object.dataProcessedCheckpointTimestamp ?? 0;
    message.consentRequestId = object.consentRequestId ?? "";
    return message;
  },
};

function createBaseConsentedAccount(): ConsentedAccount {
  return { fipId: "", linkedAccRef: "", maskedAccNumber: "", isDeleted: false };
}

export const ConsentedAccount: MessageFns<ConsentedAccount> = {
  encode(message: ConsentedAccount, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.fipId !== "") {
      writer.uint32(10).string(message.fipId);
    }
    if (message.linkedAccRef !== "") {
      writer.uint32(18).string(message.linkedAccRef);
    }
    if (message.maskedAccNumber !== "") {
      writer.uint32(26).string(message.maskedAccNumber);
    }
    if (message.isDeleted !== false) {
      writer.uint32(32).bool(message.isDeleted);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ConsentedAccount {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseConsentedAccount();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.fipId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.linkedAccRef = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.maskedAccNumber = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.isDeleted = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ConsentedAccount {
    return {
      fipId: isSet(object.fipId) ? globalThis.String(object.fipId) : "",
      linkedAccRef: isSet(object.linkedAccRef) ? globalThis.String(object.linkedAccRef) : "",
      maskedAccNumber: isSet(object.maskedAccNumber) ? globalThis.String(object.maskedAccNumber) : "",
      isDeleted: isSet(object.isDeleted) ? globalThis.Boolean(object.isDeleted) : false,
    };
  },

  toJSON(message: ConsentedAccount): unknown {
    const obj: any = {};
    if (message.fipId !== "") {
      obj.fipId = message.fipId;
    }
    if (message.linkedAccRef !== "") {
      obj.linkedAccRef = message.linkedAccRef;
    }
    if (message.maskedAccNumber !== "") {
      obj.maskedAccNumber = message.maskedAccNumber;
    }
    if (message.isDeleted !== false) {
      obj.isDeleted = message.isDeleted;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ConsentedAccount>, I>>(base?: I): ConsentedAccount {
    return ConsentedAccount.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ConsentedAccount>, I>>(object: I): ConsentedAccount {
    const message = createBaseConsentedAccount();
    message.fipId = object.fipId ?? "";
    message.linkedAccRef = object.linkedAccRef ?? "";
    message.maskedAccNumber = object.maskedAccNumber ?? "";
    message.isDeleted = object.isDeleted ?? false;
    return message;
  },
};

function createBaseFipDetails(): FipDetails {
  return {
    Id: undefined,
    fipId: "",
    fipName: "",
    fiTypes: [],
    entityIconUri: "",
    entityLogoUri: "",
    entityLogoWithNameUri: "",
    code: "",
    enable: "",
    otpLength: 0,
    createdAt: 0,
    updatedAt: 0,
  };
}

export const FipDetails: MessageFns<FipDetails> = {
  encode(message: FipDetails, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.Id !== undefined) {
      ObjectId.encode(message.Id, writer.uint32(10).fork()).join();
    }
    if (message.fipId !== "") {
      writer.uint32(18).string(message.fipId);
    }
    if (message.fipName !== "") {
      writer.uint32(26).string(message.fipName);
    }
    writer.uint32(34).fork();
    for (const v of message.fiTypes) {
      writer.int32(v);
    }
    writer.join();
    if (message.entityIconUri !== "") {
      writer.uint32(42).string(message.entityIconUri);
    }
    if (message.entityLogoUri !== "") {
      writer.uint32(50).string(message.entityLogoUri);
    }
    if (message.entityLogoWithNameUri !== "") {
      writer.uint32(58).string(message.entityLogoWithNameUri);
    }
    if (message.code !== "") {
      writer.uint32(66).string(message.code);
    }
    if (message.enable !== "") {
      writer.uint32(74).string(message.enable);
    }
    if (message.otpLength !== 0) {
      writer.uint32(80).int32(message.otpLength);
    }
    if (message.createdAt !== 0) {
      writer.uint32(88).int64(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      writer.uint32(96).int64(message.updatedAt);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FipDetails {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFipDetails();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.Id = ObjectId.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.fipId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.fipName = reader.string();
          continue;
        }
        case 4: {
          if (tag === 32) {
            message.fiTypes.push(reader.int32() as any);

            continue;
          }

          if (tag === 34) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.fiTypes.push(reader.int32() as any);
            }

            continue;
          }

          break;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.entityIconUri = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.entityLogoUri = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.entityLogoWithNameUri = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.code = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.enable = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.otpLength = reader.int32();
          continue;
        }
        case 11: {
          if (tag !== 88) {
            break;
          }

          message.createdAt = longToNumber(reader.int64());
          continue;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.updatedAt = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FipDetails {
    return {
      Id: isSet(object.Id) ? ObjectId.fromJSON(object.Id) : undefined,
      fipId: isSet(object.fipId) ? globalThis.String(object.fipId) : "",
      fipName: isSet(object.fipName) ? globalThis.String(object.fipName) : "",
      fiTypes: globalThis.Array.isArray(object?.fiTypes) ? object.fiTypes.map((e: any) => fiTypeFromJSON(e)) : [],
      entityIconUri: isSet(object.entityIconUri) ? globalThis.String(object.entityIconUri) : "",
      entityLogoUri: isSet(object.entityLogoUri) ? globalThis.String(object.entityLogoUri) : "",
      entityLogoWithNameUri: isSet(object.entityLogoWithNameUri) ? globalThis.String(object.entityLogoWithNameUri) : "",
      code: isSet(object.code) ? globalThis.String(object.code) : "",
      enable: isSet(object.enable) ? globalThis.String(object.enable) : "",
      otpLength: isSet(object.otpLength) ? globalThis.Number(object.otpLength) : 0,
      createdAt: isSet(object.createdAt) ? globalThis.Number(object.createdAt) : 0,
      updatedAt: isSet(object.updatedAt) ? globalThis.Number(object.updatedAt) : 0,
    };
  },

  toJSON(message: FipDetails): unknown {
    const obj: any = {};
    if (message.Id !== undefined) {
      obj.Id = ObjectId.toJSON(message.Id);
    }
    if (message.fipId !== "") {
      obj.fipId = message.fipId;
    }
    if (message.fipName !== "") {
      obj.fipName = message.fipName;
    }
    if (message.fiTypes?.length) {
      obj.fiTypes = message.fiTypes.map((e) => fiTypeToJSON(e));
    }
    if (message.entityIconUri !== "") {
      obj.entityIconUri = message.entityIconUri;
    }
    if (message.entityLogoUri !== "") {
      obj.entityLogoUri = message.entityLogoUri;
    }
    if (message.entityLogoWithNameUri !== "") {
      obj.entityLogoWithNameUri = message.entityLogoWithNameUri;
    }
    if (message.code !== "") {
      obj.code = message.code;
    }
    if (message.enable !== "") {
      obj.enable = message.enable;
    }
    if (message.otpLength !== 0) {
      obj.otpLength = Math.round(message.otpLength);
    }
    if (message.createdAt !== 0) {
      obj.createdAt = Math.round(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      obj.updatedAt = Math.round(message.updatedAt);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FipDetails>, I>>(base?: I): FipDetails {
    return FipDetails.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FipDetails>, I>>(object: I): FipDetails {
    const message = createBaseFipDetails();
    message.Id = (object.Id !== undefined && object.Id !== null) ? ObjectId.fromPartial(object.Id) : undefined;
    message.fipId = object.fipId ?? "";
    message.fipName = object.fipName ?? "";
    message.fiTypes = object.fiTypes?.map((e) => e) || [];
    message.entityIconUri = object.entityIconUri ?? "";
    message.entityLogoUri = object.entityLogoUri ?? "";
    message.entityLogoWithNameUri = object.entityLogoWithNameUri ?? "";
    message.code = object.code ?? "";
    message.enable = object.enable ?? "";
    message.otpLength = object.otpLength ?? 0;
    message.createdAt = object.createdAt ?? 0;
    message.updatedAt = object.updatedAt ?? 0;
    return message;
  },
};

function createBaseUserAccount(): UserAccount {
  return {
    Id: undefined,
    userId: undefined,
    fiType: 0,
    familyId: "",
    consentId: "",
    linkedAccRef: "",
    accountId: undefined,
    createdAt: 0,
    updatedAt: 0,
  };
}

export const UserAccount: MessageFns<UserAccount> = {
  encode(message: UserAccount, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.Id !== undefined) {
      ObjectId.encode(message.Id, writer.uint32(10).fork()).join();
    }
    if (message.userId !== undefined) {
      ObjectId.encode(message.userId, writer.uint32(18).fork()).join();
    }
    if (message.fiType !== 0) {
      writer.uint32(24).int32(message.fiType);
    }
    if (message.familyId !== "") {
      writer.uint32(34).string(message.familyId);
    }
    if (message.consentId !== "") {
      writer.uint32(42).string(message.consentId);
    }
    if (message.linkedAccRef !== "") {
      writer.uint32(50).string(message.linkedAccRef);
    }
    if (message.accountId !== undefined) {
      ObjectId.encode(message.accountId, writer.uint32(58).fork()).join();
    }
    if (message.createdAt !== 0) {
      writer.uint32(64).int64(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      writer.uint32(72).int64(message.updatedAt);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserAccount {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserAccount();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.Id = ObjectId.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.userId = ObjectId.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.fiType = reader.int32() as any;
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.familyId = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.consentId = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.linkedAccRef = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.accountId = ObjectId.decode(reader, reader.uint32());
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.createdAt = longToNumber(reader.int64());
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.updatedAt = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserAccount {
    return {
      Id: isSet(object.Id) ? ObjectId.fromJSON(object.Id) : undefined,
      userId: isSet(object.userId) ? ObjectId.fromJSON(object.userId) : undefined,
      fiType: isSet(object.fiType) ? fiTypeFromJSON(object.fiType) : 0,
      familyId: isSet(object.familyId) ? globalThis.String(object.familyId) : "",
      consentId: isSet(object.consentId) ? globalThis.String(object.consentId) : "",
      linkedAccRef: isSet(object.linkedAccRef) ? globalThis.String(object.linkedAccRef) : "",
      accountId: isSet(object.accountId) ? ObjectId.fromJSON(object.accountId) : undefined,
      createdAt: isSet(object.createdAt) ? globalThis.Number(object.createdAt) : 0,
      updatedAt: isSet(object.updatedAt) ? globalThis.Number(object.updatedAt) : 0,
    };
  },

  toJSON(message: UserAccount): unknown {
    const obj: any = {};
    if (message.Id !== undefined) {
      obj.Id = ObjectId.toJSON(message.Id);
    }
    if (message.userId !== undefined) {
      obj.userId = ObjectId.toJSON(message.userId);
    }
    if (message.fiType !== 0) {
      obj.fiType = fiTypeToJSON(message.fiType);
    }
    if (message.familyId !== "") {
      obj.familyId = message.familyId;
    }
    if (message.consentId !== "") {
      obj.consentId = message.consentId;
    }
    if (message.linkedAccRef !== "") {
      obj.linkedAccRef = message.linkedAccRef;
    }
    if (message.accountId !== undefined) {
      obj.accountId = ObjectId.toJSON(message.accountId);
    }
    if (message.createdAt !== 0) {
      obj.createdAt = Math.round(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      obj.updatedAt = Math.round(message.updatedAt);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserAccount>, I>>(base?: I): UserAccount {
    return UserAccount.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserAccount>, I>>(object: I): UserAccount {
    const message = createBaseUserAccount();
    message.Id = (object.Id !== undefined && object.Id !== null) ? ObjectId.fromPartial(object.Id) : undefined;
    message.userId = (object.userId !== undefined && object.userId !== null)
      ? ObjectId.fromPartial(object.userId)
      : undefined;
    message.fiType = object.fiType ?? 0;
    message.familyId = object.familyId ?? "";
    message.consentId = object.consentId ?? "";
    message.linkedAccRef = object.linkedAccRef ?? "";
    message.accountId = (object.accountId !== undefined && object.accountId !== null)
      ? ObjectId.fromPartial(object.accountId)
      : undefined;
    message.createdAt = object.createdAt ?? 0;
    message.updatedAt = object.updatedAt ?? 0;
    return message;
  },
};

function createBaseDeletedAccountLog(): DeletedAccountLog {
  return { Id: undefined, fiType: 0, accountId: undefined, createdAt: 0 };
}

export const DeletedAccountLog: MessageFns<DeletedAccountLog> = {
  encode(message: DeletedAccountLog, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.Id !== undefined) {
      ObjectId.encode(message.Id, writer.uint32(10).fork()).join();
    }
    if (message.fiType !== 0) {
      writer.uint32(16).int32(message.fiType);
    }
    if (message.accountId !== undefined) {
      ObjectId.encode(message.accountId, writer.uint32(26).fork()).join();
    }
    if (message.createdAt !== 0) {
      writer.uint32(32).int64(message.createdAt);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeletedAccountLog {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeletedAccountLog();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.Id = ObjectId.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.fiType = reader.int32() as any;
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.accountId = ObjectId.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.createdAt = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DeletedAccountLog {
    return {
      Id: isSet(object.Id) ? ObjectId.fromJSON(object.Id) : undefined,
      fiType: isSet(object.fiType) ? fiTypeFromJSON(object.fiType) : 0,
      accountId: isSet(object.accountId) ? ObjectId.fromJSON(object.accountId) : undefined,
      createdAt: isSet(object.createdAt) ? globalThis.Number(object.createdAt) : 0,
    };
  },

  toJSON(message: DeletedAccountLog): unknown {
    const obj: any = {};
    if (message.Id !== undefined) {
      obj.Id = ObjectId.toJSON(message.Id);
    }
    if (message.fiType !== 0) {
      obj.fiType = fiTypeToJSON(message.fiType);
    }
    if (message.accountId !== undefined) {
      obj.accountId = ObjectId.toJSON(message.accountId);
    }
    if (message.createdAt !== 0) {
      obj.createdAt = Math.round(message.createdAt);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DeletedAccountLog>, I>>(base?: I): DeletedAccountLog {
    return DeletedAccountLog.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeletedAccountLog>, I>>(object: I): DeletedAccountLog {
    const message = createBaseDeletedAccountLog();
    message.Id = (object.Id !== undefined && object.Id !== null) ? ObjectId.fromPartial(object.Id) : undefined;
    message.fiType = object.fiType ?? 0;
    message.accountId = (object.accountId !== undefined && object.accountId !== null)
      ? ObjectId.fromPartial(object.accountId)
      : undefined;
    message.createdAt = object.createdAt ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
