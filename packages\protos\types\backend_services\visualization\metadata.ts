// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.2
//   protoc               v6.31.1
// source: backend_services/visualization/metadata.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import {
  type CallOptions,
  ChannelCredentials,
  Client,
  type ClientOptions,
  type ClientUnaryCall,
  type handleUnaryCall,
  makeGenericClientConstructor,
  Metadata,
  type ServiceError,
  type UntypedServiceImplementation,
} from "@grpc/grpc-js";

export const protobufPackage = "backend_services.visualization";

export interface FetchVisualizationMetadataRequest {
}

export interface FetchVisualizationMetadataResponse {
  metadata: Uint8Array;
}

function createBaseFetchVisualizationMetadataRequest(): FetchVisualizationMetadataRequest {
  return {};
}

export const FetchVisualizationMetadataRequest: MessageFns<FetchVisualizationMetadataRequest> = {
  encode(_: FetchVisualizationMetadataRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FetchVisualizationMetadataRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFetchVisualizationMetadataRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): FetchVisualizationMetadataRequest {
    return {};
  },

  toJSON(_: FetchVisualizationMetadataRequest): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<FetchVisualizationMetadataRequest>, I>>(
    base?: I,
  ): FetchVisualizationMetadataRequest {
    return FetchVisualizationMetadataRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FetchVisualizationMetadataRequest>, I>>(
    _: I,
  ): FetchVisualizationMetadataRequest {
    const message = createBaseFetchVisualizationMetadataRequest();
    return message;
  },
};

function createBaseFetchVisualizationMetadataResponse(): FetchVisualizationMetadataResponse {
  return { metadata: new Uint8Array(0) };
}

export const FetchVisualizationMetadataResponse: MessageFns<FetchVisualizationMetadataResponse> = {
  encode(message: FetchVisualizationMetadataResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.metadata.length !== 0) {
      writer.uint32(10).bytes(message.metadata);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FetchVisualizationMetadataResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFetchVisualizationMetadataResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.metadata = reader.bytes();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FetchVisualizationMetadataResponse {
    return { metadata: isSet(object.metadata) ? bytesFromBase64(object.metadata) : new Uint8Array(0) };
  },

  toJSON(message: FetchVisualizationMetadataResponse): unknown {
    const obj: any = {};
    if (message.metadata.length !== 0) {
      obj.metadata = base64FromBytes(message.metadata);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FetchVisualizationMetadataResponse>, I>>(
    base?: I,
  ): FetchVisualizationMetadataResponse {
    return FetchVisualizationMetadataResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FetchVisualizationMetadataResponse>, I>>(
    object: I,
  ): FetchVisualizationMetadataResponse {
    const message = createBaseFetchVisualizationMetadataResponse();
    message.metadata = object.metadata ?? new Uint8Array(0);
    return message;
  },
};

/** Service for Visualization Metadata Fetch */
export type VisualizationMetadataService = typeof VisualizationMetadataService;
export const VisualizationMetadataService = {
  fetchVisualizationMetadata: {
    path: "/backend_services.visualization.VisualizationMetadata/FetchVisualizationMetadata",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: FetchVisualizationMetadataRequest) =>
      Buffer.from(FetchVisualizationMetadataRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => FetchVisualizationMetadataRequest.decode(value),
    responseSerialize: (value: FetchVisualizationMetadataResponse) =>
      Buffer.from(FetchVisualizationMetadataResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => FetchVisualizationMetadataResponse.decode(value),
  },
} as const;

export interface VisualizationMetadataServer extends UntypedServiceImplementation {
  fetchVisualizationMetadata: handleUnaryCall<FetchVisualizationMetadataRequest, FetchVisualizationMetadataResponse>;
}

export interface VisualizationMetadataClient extends Client {
  fetchVisualizationMetadata(
    request: FetchVisualizationMetadataRequest,
    callback: (error: ServiceError | null, response: FetchVisualizationMetadataResponse) => void,
  ): ClientUnaryCall;
  fetchVisualizationMetadata(
    request: FetchVisualizationMetadataRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: FetchVisualizationMetadataResponse) => void,
  ): ClientUnaryCall;
  fetchVisualizationMetadata(
    request: FetchVisualizationMetadataRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: FetchVisualizationMetadataResponse) => void,
  ): ClientUnaryCall;
}

export const VisualizationMetadataClient = makeGenericClientConstructor(
  VisualizationMetadataService,
  "backend_services.visualization.VisualizationMetadata",
) as unknown as {
  new (address: string, credentials: ChannelCredentials, options?: Partial<ClientOptions>): VisualizationMetadataClient;
  service: typeof VisualizationMetadataService;
  serviceName: string;
};

function bytesFromBase64(b64: string): Uint8Array {
  if ((globalThis as any).Buffer) {
    return Uint8Array.from(globalThis.Buffer.from(b64, "base64"));
  } else {
    const bin = globalThis.atob(b64);
    const arr = new Uint8Array(bin.length);
    for (let i = 0; i < bin.length; ++i) {
      arr[i] = bin.charCodeAt(i);
    }
    return arr;
  }
}

function base64FromBytes(arr: Uint8Array): string {
  if ((globalThis as any).Buffer) {
    return globalThis.Buffer.from(arr).toString("base64");
  } else {
    const bin: string[] = [];
    arr.forEach((byte) => {
      bin.push(globalThis.String.fromCharCode(byte));
    });
    return globalThis.btoa(bin.join(""));
  }
}

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
