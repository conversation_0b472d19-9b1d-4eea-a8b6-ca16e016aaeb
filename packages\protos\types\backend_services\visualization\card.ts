// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.2
//   protoc               v6.31.1
// source: backend_services/visualization/card.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import {
  type CallOptions,
  ChannelCredentials,
  Client,
  type ClientOptions,
  type ClientReadableStream,
  type ClientUnaryCall,
  type handleServerStreamingCall,
  type handleUnaryCall,
  makeGenericClientConstructor,
  Metadata,
  type ServiceError,
  type UntypedServiceImplementation,
} from "@grpc/grpc-js";
import { SystemFilters, UserFilters } from "../../database/cards_filters";
import { TimeStep, timeStepFromJSON, timeStepToJSON } from "./shared";

export const protobufPackage = "backend_services.visualization";

export interface ApplyUserFiltersRequest {
  cardId: string;
  cardType: string;
  systemFilters: SystemFilters | undefined;
  userFilters: UserFilters | undefined;
}

export interface SaveUserFiltersRequest {
  cardId: string;
  cardType: string;
  priority: number;
  systemFilters: SystemFilters | undefined;
  userFilters: UserFilters | undefined;
}

export interface SaveUserFiltersResponse {
}

export interface UpdatePrioritiesRequest {
  priorityRequests: UpdatePrioritiesRequest_PriorityRequest[];
}

export interface UpdatePrioritiesRequest_PriorityRequest {
  cardId: string;
  cardType: string;
  systemFilters: SystemFilters | undefined;
  priority: number;
}

export interface UpdatePrioritiesResponse {
}

export interface FetchCardsRequest {
}

export interface Card {
  bankBalanceCard?: BankBalanceCard | undefined;
  totalWealthCard?: TotalWealthCard | undefined;
  bankAccountSummaryCard?: BankAccountSummaryCard | undefined;
  equitiesAccountSummaryCard?: EquitiesAccountSummaryCard | undefined;
  mutualFundsAccountSummaryCard?: MutualFundsAccountSummaryCard | undefined;
  mutualFundsXirrCard?: MutualFundsXIRRCard | undefined;
  mutualFundsReturnsCard?: MutualFundsReturnsCard | undefined;
  equitiesReturnsCard?: EquitiesReturnsCard | undefined;
  equitiesXirrCard?: EquitiesXIRRCard | undefined;
  id: string;
  type: string;
  shape: string;
  priority: number;
  systemFilters: SystemFilters | undefined;
  userFilters: UserFilters | undefined;
}

export interface FetchCardsResponse {
  cards: Card[];
}

export interface BankAccountSummaryCard {
  cardDetails: BankAccountSummaryCard_CardDetails | undefined;
}

export interface BankAccountSummaryCard_CardDetails {
  fipId: string;
  title: string;
  subTitle: string;
  value: number;
  /** 0.0 as no value */
  subValue: number;
  type: string;
  balanceDate: number;
}

export interface EquitiesAccountSummaryCard {
  cardDetails: EquitiesAccountSummaryCard_CardDetails | undefined;
}

export interface EquitiesAccountSummaryCard_CardDetails {
  fipId: string;
  title: string;
  subTitle: string;
  value: number;
  /** 0.0 as no value */
  subValue: number;
  type: string;
  holdingsDate: number;
  errorInfo: string[];
}

export interface LineChart {
  startTime: number;
  endTime: number;
  timeStep: TimeStep;
  listY: LineChart_YAxis[];
  listYName: string[];
  smooth: boolean;
}

export interface LineChart_YAxis {
  y: number[];
}

export interface BarChart {
  x: string[];
  listY: BarChart_YAxis[];
  listYName: string[];
  showValue: boolean;
}

export interface BarChart_YAxis {
  y: number[];
}

export interface PieChart {
  labels: string[];
  values: number[];
  showPercent: boolean;
  showNumber: boolean;
  /** TODO: Discuss */
  labelPlacement: boolean;
}

export interface Chart {
  pieChart?: PieChart | undefined;
  lineChart?: LineChart | undefined;
  barChart?: BarChart | undefined;
  type: string;
}

export interface BankBalanceCard {
  cardDetails: BankBalanceCard_CardDetails | undefined;
}

export interface BankBalanceCard_CardDetails {
  title: string;
  value: number;
  chart: Chart | undefined;
  errorInfo: string[];
}

export interface TotalWealthCard {
  cardDetails: TotalWealthCard_CardDetails | undefined;
}

export interface TotalWealthCard_CardDetails {
  title: string;
  subTitle: string;
  chart: Chart | undefined;
  errorInfo: string[];
}

export interface EquitiesXIRRCard {
  cardDetails: EquitiesXIRRCard_CardDetails | undefined;
}

export interface EquitiesXIRRCard_CardDetails {
  title: string;
  subTitle1: string;
  subValue1: number;
  subTitle2: string;
  subValue2: number;
  rateTitle: string;
  rate: number;
  chart: Chart | undefined;
  errorInfo: string[];
}

export interface EquitiesReturnsCard {
  cardDetails: EquitiesReturnsCard_CardDetails | undefined;
}

export interface EquitiesReturnsCard_CardDetails {
  title: string;
  subTitle: string[];
  value: number[];
  chart: Chart | undefined;
  errorInfo: string[];
}

export interface MutualFundsAccountSummaryCard {
  cardDetails: MutualFundsAccountSummaryCard_CardDetails | undefined;
}

export interface MutualFundsAccountSummaryCard_CardDetails {
  fipId: string;
  title: string;
  subTitle: string;
  value: number;
  /** 0.0 as no value */
  subValue: number;
  holdingsDate: number;
  errorInfo: string[];
}

export interface MutualFundsXIRRCard {
  cardDetails: MutualFundsXIRRCard_CardDetails | undefined;
}

export interface MutualFundsXIRRCard_CardDetails {
  title: string;
  subTitle1: string;
  subValue1: number;
  subTitle2: string;
  subValue2: number;
  rateTitle: string;
  rate: number;
  chart: Chart | undefined;
  errorInfo: string[];
}

export interface MutualFundsReturnsCard {
  cardDetails: MutualFundsReturnsCard_CardDetails | undefined;
}

export interface MutualFundsReturnsCard_CardDetails {
  title: string;
  subTitle: string[];
  value: number[];
  chart: Chart | undefined;
  errorInfo: string[];
}

function createBaseApplyUserFiltersRequest(): ApplyUserFiltersRequest {
  return { cardId: "", cardType: "", systemFilters: undefined, userFilters: undefined };
}

export const ApplyUserFiltersRequest: MessageFns<ApplyUserFiltersRequest> = {
  encode(message: ApplyUserFiltersRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.cardId !== "") {
      writer.uint32(10).string(message.cardId);
    }
    if (message.cardType !== "") {
      writer.uint32(18).string(message.cardType);
    }
    if (message.systemFilters !== undefined) {
      SystemFilters.encode(message.systemFilters, writer.uint32(26).fork()).join();
    }
    if (message.userFilters !== undefined) {
      UserFilters.encode(message.userFilters, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ApplyUserFiltersRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseApplyUserFiltersRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.cardId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.cardType = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.systemFilters = SystemFilters.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.userFilters = UserFilters.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ApplyUserFiltersRequest {
    return {
      cardId: isSet(object.cardId) ? globalThis.String(object.cardId) : "",
      cardType: isSet(object.cardType) ? globalThis.String(object.cardType) : "",
      systemFilters: isSet(object.systemFilters) ? SystemFilters.fromJSON(object.systemFilters) : undefined,
      userFilters: isSet(object.userFilters) ? UserFilters.fromJSON(object.userFilters) : undefined,
    };
  },

  toJSON(message: ApplyUserFiltersRequest): unknown {
    const obj: any = {};
    if (message.cardId !== "") {
      obj.cardId = message.cardId;
    }
    if (message.cardType !== "") {
      obj.cardType = message.cardType;
    }
    if (message.systemFilters !== undefined) {
      obj.systemFilters = SystemFilters.toJSON(message.systemFilters);
    }
    if (message.userFilters !== undefined) {
      obj.userFilters = UserFilters.toJSON(message.userFilters);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ApplyUserFiltersRequest>, I>>(base?: I): ApplyUserFiltersRequest {
    return ApplyUserFiltersRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ApplyUserFiltersRequest>, I>>(object: I): ApplyUserFiltersRequest {
    const message = createBaseApplyUserFiltersRequest();
    message.cardId = object.cardId ?? "";
    message.cardType = object.cardType ?? "";
    message.systemFilters = (object.systemFilters !== undefined && object.systemFilters !== null)
      ? SystemFilters.fromPartial(object.systemFilters)
      : undefined;
    message.userFilters = (object.userFilters !== undefined && object.userFilters !== null)
      ? UserFilters.fromPartial(object.userFilters)
      : undefined;
    return message;
  },
};

function createBaseSaveUserFiltersRequest(): SaveUserFiltersRequest {
  return { cardId: "", cardType: "", priority: 0, systemFilters: undefined, userFilters: undefined };
}

export const SaveUserFiltersRequest: MessageFns<SaveUserFiltersRequest> = {
  encode(message: SaveUserFiltersRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.cardId !== "") {
      writer.uint32(10).string(message.cardId);
    }
    if (message.cardType !== "") {
      writer.uint32(18).string(message.cardType);
    }
    if (message.priority !== 0) {
      writer.uint32(24).int64(message.priority);
    }
    if (message.systemFilters !== undefined) {
      SystemFilters.encode(message.systemFilters, writer.uint32(34).fork()).join();
    }
    if (message.userFilters !== undefined) {
      UserFilters.encode(message.userFilters, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SaveUserFiltersRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSaveUserFiltersRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.cardId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.cardType = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.priority = longToNumber(reader.int64());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.systemFilters = SystemFilters.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.userFilters = UserFilters.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SaveUserFiltersRequest {
    return {
      cardId: isSet(object.cardId) ? globalThis.String(object.cardId) : "",
      cardType: isSet(object.cardType) ? globalThis.String(object.cardType) : "",
      priority: isSet(object.priority) ? globalThis.Number(object.priority) : 0,
      systemFilters: isSet(object.systemFilters) ? SystemFilters.fromJSON(object.systemFilters) : undefined,
      userFilters: isSet(object.userFilters) ? UserFilters.fromJSON(object.userFilters) : undefined,
    };
  },

  toJSON(message: SaveUserFiltersRequest): unknown {
    const obj: any = {};
    if (message.cardId !== "") {
      obj.cardId = message.cardId;
    }
    if (message.cardType !== "") {
      obj.cardType = message.cardType;
    }
    if (message.priority !== 0) {
      obj.priority = Math.round(message.priority);
    }
    if (message.systemFilters !== undefined) {
      obj.systemFilters = SystemFilters.toJSON(message.systemFilters);
    }
    if (message.userFilters !== undefined) {
      obj.userFilters = UserFilters.toJSON(message.userFilters);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SaveUserFiltersRequest>, I>>(base?: I): SaveUserFiltersRequest {
    return SaveUserFiltersRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SaveUserFiltersRequest>, I>>(object: I): SaveUserFiltersRequest {
    const message = createBaseSaveUserFiltersRequest();
    message.cardId = object.cardId ?? "";
    message.cardType = object.cardType ?? "";
    message.priority = object.priority ?? 0;
    message.systemFilters = (object.systemFilters !== undefined && object.systemFilters !== null)
      ? SystemFilters.fromPartial(object.systemFilters)
      : undefined;
    message.userFilters = (object.userFilters !== undefined && object.userFilters !== null)
      ? UserFilters.fromPartial(object.userFilters)
      : undefined;
    return message;
  },
};

function createBaseSaveUserFiltersResponse(): SaveUserFiltersResponse {
  return {};
}

export const SaveUserFiltersResponse: MessageFns<SaveUserFiltersResponse> = {
  encode(_: SaveUserFiltersResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SaveUserFiltersResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSaveUserFiltersResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): SaveUserFiltersResponse {
    return {};
  },

  toJSON(_: SaveUserFiltersResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<SaveUserFiltersResponse>, I>>(base?: I): SaveUserFiltersResponse {
    return SaveUserFiltersResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SaveUserFiltersResponse>, I>>(_: I): SaveUserFiltersResponse {
    const message = createBaseSaveUserFiltersResponse();
    return message;
  },
};

function createBaseUpdatePrioritiesRequest(): UpdatePrioritiesRequest {
  return { priorityRequests: [] };
}

export const UpdatePrioritiesRequest: MessageFns<UpdatePrioritiesRequest> = {
  encode(message: UpdatePrioritiesRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.priorityRequests) {
      UpdatePrioritiesRequest_PriorityRequest.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdatePrioritiesRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdatePrioritiesRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.priorityRequests.push(UpdatePrioritiesRequest_PriorityRequest.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdatePrioritiesRequest {
    return {
      priorityRequests: globalThis.Array.isArray(object?.priorityRequests)
        ? object.priorityRequests.map((e: any) => UpdatePrioritiesRequest_PriorityRequest.fromJSON(e))
        : [],
    };
  },

  toJSON(message: UpdatePrioritiesRequest): unknown {
    const obj: any = {};
    if (message.priorityRequests?.length) {
      obj.priorityRequests = message.priorityRequests.map((e) => UpdatePrioritiesRequest_PriorityRequest.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdatePrioritiesRequest>, I>>(base?: I): UpdatePrioritiesRequest {
    return UpdatePrioritiesRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdatePrioritiesRequest>, I>>(object: I): UpdatePrioritiesRequest {
    const message = createBaseUpdatePrioritiesRequest();
    message.priorityRequests =
      object.priorityRequests?.map((e) => UpdatePrioritiesRequest_PriorityRequest.fromPartial(e)) || [];
    return message;
  },
};

function createBaseUpdatePrioritiesRequest_PriorityRequest(): UpdatePrioritiesRequest_PriorityRequest {
  return { cardId: "", cardType: "", systemFilters: undefined, priority: 0 };
}

export const UpdatePrioritiesRequest_PriorityRequest: MessageFns<UpdatePrioritiesRequest_PriorityRequest> = {
  encode(message: UpdatePrioritiesRequest_PriorityRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.cardId !== "") {
      writer.uint32(10).string(message.cardId);
    }
    if (message.cardType !== "") {
      writer.uint32(18).string(message.cardType);
    }
    if (message.systemFilters !== undefined) {
      SystemFilters.encode(message.systemFilters, writer.uint32(26).fork()).join();
    }
    if (message.priority !== 0) {
      writer.uint32(32).int64(message.priority);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdatePrioritiesRequest_PriorityRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdatePrioritiesRequest_PriorityRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.cardId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.cardType = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.systemFilters = SystemFilters.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.priority = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdatePrioritiesRequest_PriorityRequest {
    return {
      cardId: isSet(object.cardId) ? globalThis.String(object.cardId) : "",
      cardType: isSet(object.cardType) ? globalThis.String(object.cardType) : "",
      systemFilters: isSet(object.systemFilters) ? SystemFilters.fromJSON(object.systemFilters) : undefined,
      priority: isSet(object.priority) ? globalThis.Number(object.priority) : 0,
    };
  },

  toJSON(message: UpdatePrioritiesRequest_PriorityRequest): unknown {
    const obj: any = {};
    if (message.cardId !== "") {
      obj.cardId = message.cardId;
    }
    if (message.cardType !== "") {
      obj.cardType = message.cardType;
    }
    if (message.systemFilters !== undefined) {
      obj.systemFilters = SystemFilters.toJSON(message.systemFilters);
    }
    if (message.priority !== 0) {
      obj.priority = Math.round(message.priority);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdatePrioritiesRequest_PriorityRequest>, I>>(
    base?: I,
  ): UpdatePrioritiesRequest_PriorityRequest {
    return UpdatePrioritiesRequest_PriorityRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdatePrioritiesRequest_PriorityRequest>, I>>(
    object: I,
  ): UpdatePrioritiesRequest_PriorityRequest {
    const message = createBaseUpdatePrioritiesRequest_PriorityRequest();
    message.cardId = object.cardId ?? "";
    message.cardType = object.cardType ?? "";
    message.systemFilters = (object.systemFilters !== undefined && object.systemFilters !== null)
      ? SystemFilters.fromPartial(object.systemFilters)
      : undefined;
    message.priority = object.priority ?? 0;
    return message;
  },
};

function createBaseUpdatePrioritiesResponse(): UpdatePrioritiesResponse {
  return {};
}

export const UpdatePrioritiesResponse: MessageFns<UpdatePrioritiesResponse> = {
  encode(_: UpdatePrioritiesResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdatePrioritiesResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdatePrioritiesResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): UpdatePrioritiesResponse {
    return {};
  },

  toJSON(_: UpdatePrioritiesResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdatePrioritiesResponse>, I>>(base?: I): UpdatePrioritiesResponse {
    return UpdatePrioritiesResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdatePrioritiesResponse>, I>>(_: I): UpdatePrioritiesResponse {
    const message = createBaseUpdatePrioritiesResponse();
    return message;
  },
};

function createBaseFetchCardsRequest(): FetchCardsRequest {
  return {};
}

export const FetchCardsRequest: MessageFns<FetchCardsRequest> = {
  encode(_: FetchCardsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FetchCardsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFetchCardsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): FetchCardsRequest {
    return {};
  },

  toJSON(_: FetchCardsRequest): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<FetchCardsRequest>, I>>(base?: I): FetchCardsRequest {
    return FetchCardsRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FetchCardsRequest>, I>>(_: I): FetchCardsRequest {
    const message = createBaseFetchCardsRequest();
    return message;
  },
};

function createBaseCard(): Card {
  return {
    bankBalanceCard: undefined,
    totalWealthCard: undefined,
    bankAccountSummaryCard: undefined,
    equitiesAccountSummaryCard: undefined,
    mutualFundsAccountSummaryCard: undefined,
    mutualFundsXirrCard: undefined,
    mutualFundsReturnsCard: undefined,
    equitiesReturnsCard: undefined,
    equitiesXirrCard: undefined,
    id: "",
    type: "",
    shape: "",
    priority: 0,
    systemFilters: undefined,
    userFilters: undefined,
  };
}

export const Card: MessageFns<Card> = {
  encode(message: Card, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bankBalanceCard !== undefined) {
      BankBalanceCard.encode(message.bankBalanceCard, writer.uint32(10).fork()).join();
    }
    if (message.totalWealthCard !== undefined) {
      TotalWealthCard.encode(message.totalWealthCard, writer.uint32(18).fork()).join();
    }
    if (message.bankAccountSummaryCard !== undefined) {
      BankAccountSummaryCard.encode(message.bankAccountSummaryCard, writer.uint32(26).fork()).join();
    }
    if (message.equitiesAccountSummaryCard !== undefined) {
      EquitiesAccountSummaryCard.encode(message.equitiesAccountSummaryCard, writer.uint32(34).fork()).join();
    }
    if (message.mutualFundsAccountSummaryCard !== undefined) {
      MutualFundsAccountSummaryCard.encode(message.mutualFundsAccountSummaryCard, writer.uint32(42).fork()).join();
    }
    if (message.mutualFundsXirrCard !== undefined) {
      MutualFundsXIRRCard.encode(message.mutualFundsXirrCard, writer.uint32(50).fork()).join();
    }
    if (message.mutualFundsReturnsCard !== undefined) {
      MutualFundsReturnsCard.encode(message.mutualFundsReturnsCard, writer.uint32(58).fork()).join();
    }
    if (message.equitiesReturnsCard !== undefined) {
      EquitiesReturnsCard.encode(message.equitiesReturnsCard, writer.uint32(66).fork()).join();
    }
    if (message.equitiesXirrCard !== undefined) {
      EquitiesXIRRCard.encode(message.equitiesXirrCard, writer.uint32(74).fork()).join();
    }
    if (message.id !== "") {
      writer.uint32(82).string(message.id);
    }
    if (message.type !== "") {
      writer.uint32(90).string(message.type);
    }
    if (message.shape !== "") {
      writer.uint32(98).string(message.shape);
    }
    if (message.priority !== 0) {
      writer.uint32(104).int64(message.priority);
    }
    if (message.systemFilters !== undefined) {
      SystemFilters.encode(message.systemFilters, writer.uint32(114).fork()).join();
    }
    if (message.userFilters !== undefined) {
      UserFilters.encode(message.userFilters, writer.uint32(122).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Card {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCard();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bankBalanceCard = BankBalanceCard.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.totalWealthCard = TotalWealthCard.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.bankAccountSummaryCard = BankAccountSummaryCard.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.equitiesAccountSummaryCard = EquitiesAccountSummaryCard.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.mutualFundsAccountSummaryCard = MutualFundsAccountSummaryCard.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.mutualFundsXirrCard = MutualFundsXIRRCard.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.mutualFundsReturnsCard = MutualFundsReturnsCard.decode(reader, reader.uint32());
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.equitiesReturnsCard = EquitiesReturnsCard.decode(reader, reader.uint32());
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.equitiesXirrCard = EquitiesXIRRCard.decode(reader, reader.uint32());
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.type = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.shape = reader.string();
          continue;
        }
        case 13: {
          if (tag !== 104) {
            break;
          }

          message.priority = longToNumber(reader.int64());
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          message.systemFilters = SystemFilters.decode(reader, reader.uint32());
          continue;
        }
        case 15: {
          if (tag !== 122) {
            break;
          }

          message.userFilters = UserFilters.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Card {
    return {
      bankBalanceCard: isSet(object.bankBalanceCard) ? BankBalanceCard.fromJSON(object.bankBalanceCard) : undefined,
      totalWealthCard: isSet(object.totalWealthCard) ? TotalWealthCard.fromJSON(object.totalWealthCard) : undefined,
      bankAccountSummaryCard: isSet(object.bankAccountSummaryCard)
        ? BankAccountSummaryCard.fromJSON(object.bankAccountSummaryCard)
        : undefined,
      equitiesAccountSummaryCard: isSet(object.equitiesAccountSummaryCard)
        ? EquitiesAccountSummaryCard.fromJSON(object.equitiesAccountSummaryCard)
        : undefined,
      mutualFundsAccountSummaryCard: isSet(object.mutualFundsAccountSummaryCard)
        ? MutualFundsAccountSummaryCard.fromJSON(object.mutualFundsAccountSummaryCard)
        : undefined,
      mutualFundsXirrCard: isSet(object.mutualFundsXirrCard)
        ? MutualFundsXIRRCard.fromJSON(object.mutualFundsXirrCard)
        : undefined,
      mutualFundsReturnsCard: isSet(object.mutualFundsReturnsCard)
        ? MutualFundsReturnsCard.fromJSON(object.mutualFundsReturnsCard)
        : undefined,
      equitiesReturnsCard: isSet(object.equitiesReturnsCard)
        ? EquitiesReturnsCard.fromJSON(object.equitiesReturnsCard)
        : undefined,
      equitiesXirrCard: isSet(object.equitiesXirrCard) ? EquitiesXIRRCard.fromJSON(object.equitiesXirrCard) : undefined,
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      type: isSet(object.type) ? globalThis.String(object.type) : "",
      shape: isSet(object.shape) ? globalThis.String(object.shape) : "",
      priority: isSet(object.priority) ? globalThis.Number(object.priority) : 0,
      systemFilters: isSet(object.systemFilters) ? SystemFilters.fromJSON(object.systemFilters) : undefined,
      userFilters: isSet(object.userFilters) ? UserFilters.fromJSON(object.userFilters) : undefined,
    };
  },

  toJSON(message: Card): unknown {
    const obj: any = {};
    if (message.bankBalanceCard !== undefined) {
      obj.bankBalanceCard = BankBalanceCard.toJSON(message.bankBalanceCard);
    }
    if (message.totalWealthCard !== undefined) {
      obj.totalWealthCard = TotalWealthCard.toJSON(message.totalWealthCard);
    }
    if (message.bankAccountSummaryCard !== undefined) {
      obj.bankAccountSummaryCard = BankAccountSummaryCard.toJSON(message.bankAccountSummaryCard);
    }
    if (message.equitiesAccountSummaryCard !== undefined) {
      obj.equitiesAccountSummaryCard = EquitiesAccountSummaryCard.toJSON(message.equitiesAccountSummaryCard);
    }
    if (message.mutualFundsAccountSummaryCard !== undefined) {
      obj.mutualFundsAccountSummaryCard = MutualFundsAccountSummaryCard.toJSON(message.mutualFundsAccountSummaryCard);
    }
    if (message.mutualFundsXirrCard !== undefined) {
      obj.mutualFundsXirrCard = MutualFundsXIRRCard.toJSON(message.mutualFundsXirrCard);
    }
    if (message.mutualFundsReturnsCard !== undefined) {
      obj.mutualFundsReturnsCard = MutualFundsReturnsCard.toJSON(message.mutualFundsReturnsCard);
    }
    if (message.equitiesReturnsCard !== undefined) {
      obj.equitiesReturnsCard = EquitiesReturnsCard.toJSON(message.equitiesReturnsCard);
    }
    if (message.equitiesXirrCard !== undefined) {
      obj.equitiesXirrCard = EquitiesXIRRCard.toJSON(message.equitiesXirrCard);
    }
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.type !== "") {
      obj.type = message.type;
    }
    if (message.shape !== "") {
      obj.shape = message.shape;
    }
    if (message.priority !== 0) {
      obj.priority = Math.round(message.priority);
    }
    if (message.systemFilters !== undefined) {
      obj.systemFilters = SystemFilters.toJSON(message.systemFilters);
    }
    if (message.userFilters !== undefined) {
      obj.userFilters = UserFilters.toJSON(message.userFilters);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Card>, I>>(base?: I): Card {
    return Card.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Card>, I>>(object: I): Card {
    const message = createBaseCard();
    message.bankBalanceCard = (object.bankBalanceCard !== undefined && object.bankBalanceCard !== null)
      ? BankBalanceCard.fromPartial(object.bankBalanceCard)
      : undefined;
    message.totalWealthCard = (object.totalWealthCard !== undefined && object.totalWealthCard !== null)
      ? TotalWealthCard.fromPartial(object.totalWealthCard)
      : undefined;
    message.bankAccountSummaryCard =
      (object.bankAccountSummaryCard !== undefined && object.bankAccountSummaryCard !== null)
        ? BankAccountSummaryCard.fromPartial(object.bankAccountSummaryCard)
        : undefined;
    message.equitiesAccountSummaryCard =
      (object.equitiesAccountSummaryCard !== undefined && object.equitiesAccountSummaryCard !== null)
        ? EquitiesAccountSummaryCard.fromPartial(object.equitiesAccountSummaryCard)
        : undefined;
    message.mutualFundsAccountSummaryCard =
      (object.mutualFundsAccountSummaryCard !== undefined && object.mutualFundsAccountSummaryCard !== null)
        ? MutualFundsAccountSummaryCard.fromPartial(object.mutualFundsAccountSummaryCard)
        : undefined;
    message.mutualFundsXirrCard = (object.mutualFundsXirrCard !== undefined && object.mutualFundsXirrCard !== null)
      ? MutualFundsXIRRCard.fromPartial(object.mutualFundsXirrCard)
      : undefined;
    message.mutualFundsReturnsCard =
      (object.mutualFundsReturnsCard !== undefined && object.mutualFundsReturnsCard !== null)
        ? MutualFundsReturnsCard.fromPartial(object.mutualFundsReturnsCard)
        : undefined;
    message.equitiesReturnsCard = (object.equitiesReturnsCard !== undefined && object.equitiesReturnsCard !== null)
      ? EquitiesReturnsCard.fromPartial(object.equitiesReturnsCard)
      : undefined;
    message.equitiesXirrCard = (object.equitiesXirrCard !== undefined && object.equitiesXirrCard !== null)
      ? EquitiesXIRRCard.fromPartial(object.equitiesXirrCard)
      : undefined;
    message.id = object.id ?? "";
    message.type = object.type ?? "";
    message.shape = object.shape ?? "";
    message.priority = object.priority ?? 0;
    message.systemFilters = (object.systemFilters !== undefined && object.systemFilters !== null)
      ? SystemFilters.fromPartial(object.systemFilters)
      : undefined;
    message.userFilters = (object.userFilters !== undefined && object.userFilters !== null)
      ? UserFilters.fromPartial(object.userFilters)
      : undefined;
    return message;
  },
};

function createBaseFetchCardsResponse(): FetchCardsResponse {
  return { cards: [] };
}

export const FetchCardsResponse: MessageFns<FetchCardsResponse> = {
  encode(message: FetchCardsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.cards) {
      Card.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FetchCardsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFetchCardsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.cards.push(Card.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FetchCardsResponse {
    return { cards: globalThis.Array.isArray(object?.cards) ? object.cards.map((e: any) => Card.fromJSON(e)) : [] };
  },

  toJSON(message: FetchCardsResponse): unknown {
    const obj: any = {};
    if (message.cards?.length) {
      obj.cards = message.cards.map((e) => Card.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FetchCardsResponse>, I>>(base?: I): FetchCardsResponse {
    return FetchCardsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FetchCardsResponse>, I>>(object: I): FetchCardsResponse {
    const message = createBaseFetchCardsResponse();
    message.cards = object.cards?.map((e) => Card.fromPartial(e)) || [];
    return message;
  },
};

function createBaseBankAccountSummaryCard(): BankAccountSummaryCard {
  return { cardDetails: undefined };
}

export const BankAccountSummaryCard: MessageFns<BankAccountSummaryCard> = {
  encode(message: BankAccountSummaryCard, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.cardDetails !== undefined) {
      BankAccountSummaryCard_CardDetails.encode(message.cardDetails, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankAccountSummaryCard {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankAccountSummaryCard();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.cardDetails = BankAccountSummaryCard_CardDetails.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankAccountSummaryCard {
    return {
      cardDetails: isSet(object.cardDetails)
        ? BankAccountSummaryCard_CardDetails.fromJSON(object.cardDetails)
        : undefined,
    };
  },

  toJSON(message: BankAccountSummaryCard): unknown {
    const obj: any = {};
    if (message.cardDetails !== undefined) {
      obj.cardDetails = BankAccountSummaryCard_CardDetails.toJSON(message.cardDetails);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankAccountSummaryCard>, I>>(base?: I): BankAccountSummaryCard {
    return BankAccountSummaryCard.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankAccountSummaryCard>, I>>(object: I): BankAccountSummaryCard {
    const message = createBaseBankAccountSummaryCard();
    message.cardDetails = (object.cardDetails !== undefined && object.cardDetails !== null)
      ? BankAccountSummaryCard_CardDetails.fromPartial(object.cardDetails)
      : undefined;
    return message;
  },
};

function createBaseBankAccountSummaryCard_CardDetails(): BankAccountSummaryCard_CardDetails {
  return { fipId: "", title: "", subTitle: "", value: 0, subValue: 0, type: "", balanceDate: 0 };
}

export const BankAccountSummaryCard_CardDetails: MessageFns<BankAccountSummaryCard_CardDetails> = {
  encode(message: BankAccountSummaryCard_CardDetails, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.fipId !== "") {
      writer.uint32(10).string(message.fipId);
    }
    if (message.title !== "") {
      writer.uint32(18).string(message.title);
    }
    if (message.subTitle !== "") {
      writer.uint32(26).string(message.subTitle);
    }
    if (message.value !== 0) {
      writer.uint32(33).double(message.value);
    }
    if (message.subValue !== 0) {
      writer.uint32(41).double(message.subValue);
    }
    if (message.type !== "") {
      writer.uint32(50).string(message.type);
    }
    if (message.balanceDate !== 0) {
      writer.uint32(64).int64(message.balanceDate);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankAccountSummaryCard_CardDetails {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankAccountSummaryCard_CardDetails();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.fipId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.subTitle = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.value = reader.double();
          continue;
        }
        case 5: {
          if (tag !== 41) {
            break;
          }

          message.subValue = reader.double();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.type = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.balanceDate = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankAccountSummaryCard_CardDetails {
    return {
      fipId: isSet(object.fipId) ? globalThis.String(object.fipId) : "",
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      subTitle: isSet(object.subTitle) ? globalThis.String(object.subTitle) : "",
      value: isSet(object.value) ? globalThis.Number(object.value) : 0,
      subValue: isSet(object.subValue) ? globalThis.Number(object.subValue) : 0,
      type: isSet(object.type) ? globalThis.String(object.type) : "",
      balanceDate: isSet(object.balanceDate) ? globalThis.Number(object.balanceDate) : 0,
    };
  },

  toJSON(message: BankAccountSummaryCard_CardDetails): unknown {
    const obj: any = {};
    if (message.fipId !== "") {
      obj.fipId = message.fipId;
    }
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.subTitle !== "") {
      obj.subTitle = message.subTitle;
    }
    if (message.value !== 0) {
      obj.value = message.value;
    }
    if (message.subValue !== 0) {
      obj.subValue = message.subValue;
    }
    if (message.type !== "") {
      obj.type = message.type;
    }
    if (message.balanceDate !== 0) {
      obj.balanceDate = Math.round(message.balanceDate);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankAccountSummaryCard_CardDetails>, I>>(
    base?: I,
  ): BankAccountSummaryCard_CardDetails {
    return BankAccountSummaryCard_CardDetails.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankAccountSummaryCard_CardDetails>, I>>(
    object: I,
  ): BankAccountSummaryCard_CardDetails {
    const message = createBaseBankAccountSummaryCard_CardDetails();
    message.fipId = object.fipId ?? "";
    message.title = object.title ?? "";
    message.subTitle = object.subTitle ?? "";
    message.value = object.value ?? 0;
    message.subValue = object.subValue ?? 0;
    message.type = object.type ?? "";
    message.balanceDate = object.balanceDate ?? 0;
    return message;
  },
};

function createBaseEquitiesAccountSummaryCard(): EquitiesAccountSummaryCard {
  return { cardDetails: undefined };
}

export const EquitiesAccountSummaryCard: MessageFns<EquitiesAccountSummaryCard> = {
  encode(message: EquitiesAccountSummaryCard, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.cardDetails !== undefined) {
      EquitiesAccountSummaryCard_CardDetails.encode(message.cardDetails, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EquitiesAccountSummaryCard {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEquitiesAccountSummaryCard();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.cardDetails = EquitiesAccountSummaryCard_CardDetails.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): EquitiesAccountSummaryCard {
    return {
      cardDetails: isSet(object.cardDetails)
        ? EquitiesAccountSummaryCard_CardDetails.fromJSON(object.cardDetails)
        : undefined,
    };
  },

  toJSON(message: EquitiesAccountSummaryCard): unknown {
    const obj: any = {};
    if (message.cardDetails !== undefined) {
      obj.cardDetails = EquitiesAccountSummaryCard_CardDetails.toJSON(message.cardDetails);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<EquitiesAccountSummaryCard>, I>>(base?: I): EquitiesAccountSummaryCard {
    return EquitiesAccountSummaryCard.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EquitiesAccountSummaryCard>, I>>(object: I): EquitiesAccountSummaryCard {
    const message = createBaseEquitiesAccountSummaryCard();
    message.cardDetails = (object.cardDetails !== undefined && object.cardDetails !== null)
      ? EquitiesAccountSummaryCard_CardDetails.fromPartial(object.cardDetails)
      : undefined;
    return message;
  },
};

function createBaseEquitiesAccountSummaryCard_CardDetails(): EquitiesAccountSummaryCard_CardDetails {
  return { fipId: "", title: "", subTitle: "", value: 0, subValue: 0, type: "", holdingsDate: 0, errorInfo: [] };
}

export const EquitiesAccountSummaryCard_CardDetails: MessageFns<EquitiesAccountSummaryCard_CardDetails> = {
  encode(message: EquitiesAccountSummaryCard_CardDetails, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.fipId !== "") {
      writer.uint32(10).string(message.fipId);
    }
    if (message.title !== "") {
      writer.uint32(18).string(message.title);
    }
    if (message.subTitle !== "") {
      writer.uint32(26).string(message.subTitle);
    }
    if (message.value !== 0) {
      writer.uint32(33).double(message.value);
    }
    if (message.subValue !== 0) {
      writer.uint32(41).double(message.subValue);
    }
    if (message.type !== "") {
      writer.uint32(50).string(message.type);
    }
    if (message.holdingsDate !== 0) {
      writer.uint32(64).int64(message.holdingsDate);
    }
    for (const v of message.errorInfo) {
      writer.uint32(74).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EquitiesAccountSummaryCard_CardDetails {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEquitiesAccountSummaryCard_CardDetails();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.fipId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.subTitle = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.value = reader.double();
          continue;
        }
        case 5: {
          if (tag !== 41) {
            break;
          }

          message.subValue = reader.double();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.type = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.holdingsDate = longToNumber(reader.int64());
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.errorInfo.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): EquitiesAccountSummaryCard_CardDetails {
    return {
      fipId: isSet(object.fipId) ? globalThis.String(object.fipId) : "",
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      subTitle: isSet(object.subTitle) ? globalThis.String(object.subTitle) : "",
      value: isSet(object.value) ? globalThis.Number(object.value) : 0,
      subValue: isSet(object.subValue) ? globalThis.Number(object.subValue) : 0,
      type: isSet(object.type) ? globalThis.String(object.type) : "",
      holdingsDate: isSet(object.holdingsDate) ? globalThis.Number(object.holdingsDate) : 0,
      errorInfo: globalThis.Array.isArray(object?.errorInfo)
        ? object.errorInfo.map((e: any) => globalThis.String(e))
        : [],
    };
  },

  toJSON(message: EquitiesAccountSummaryCard_CardDetails): unknown {
    const obj: any = {};
    if (message.fipId !== "") {
      obj.fipId = message.fipId;
    }
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.subTitle !== "") {
      obj.subTitle = message.subTitle;
    }
    if (message.value !== 0) {
      obj.value = message.value;
    }
    if (message.subValue !== 0) {
      obj.subValue = message.subValue;
    }
    if (message.type !== "") {
      obj.type = message.type;
    }
    if (message.holdingsDate !== 0) {
      obj.holdingsDate = Math.round(message.holdingsDate);
    }
    if (message.errorInfo?.length) {
      obj.errorInfo = message.errorInfo;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<EquitiesAccountSummaryCard_CardDetails>, I>>(
    base?: I,
  ): EquitiesAccountSummaryCard_CardDetails {
    return EquitiesAccountSummaryCard_CardDetails.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EquitiesAccountSummaryCard_CardDetails>, I>>(
    object: I,
  ): EquitiesAccountSummaryCard_CardDetails {
    const message = createBaseEquitiesAccountSummaryCard_CardDetails();
    message.fipId = object.fipId ?? "";
    message.title = object.title ?? "";
    message.subTitle = object.subTitle ?? "";
    message.value = object.value ?? 0;
    message.subValue = object.subValue ?? 0;
    message.type = object.type ?? "";
    message.holdingsDate = object.holdingsDate ?? 0;
    message.errorInfo = object.errorInfo?.map((e) => e) || [];
    return message;
  },
};

function createBaseLineChart(): LineChart {
  return { startTime: 0, endTime: 0, timeStep: 0, listY: [], listYName: [], smooth: false };
}

export const LineChart: MessageFns<LineChart> = {
  encode(message: LineChart, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.startTime !== 0) {
      writer.uint32(8).int64(message.startTime);
    }
    if (message.endTime !== 0) {
      writer.uint32(16).int64(message.endTime);
    }
    if (message.timeStep !== 0) {
      writer.uint32(24).int32(message.timeStep);
    }
    for (const v of message.listY) {
      LineChart_YAxis.encode(v!, writer.uint32(34).fork()).join();
    }
    for (const v of message.listYName) {
      writer.uint32(42).string(v!);
    }
    if (message.smooth !== false) {
      writer.uint32(48).bool(message.smooth);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LineChart {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLineChart();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.startTime = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.endTime = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.timeStep = reader.int32() as any;
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.listY.push(LineChart_YAxis.decode(reader, reader.uint32()));
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.listYName.push(reader.string());
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.smooth = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): LineChart {
    return {
      startTime: isSet(object.startTime) ? globalThis.Number(object.startTime) : 0,
      endTime: isSet(object.endTime) ? globalThis.Number(object.endTime) : 0,
      timeStep: isSet(object.timeStep) ? timeStepFromJSON(object.timeStep) : 0,
      listY: globalThis.Array.isArray(object?.listY) ? object.listY.map((e: any) => LineChart_YAxis.fromJSON(e)) : [],
      listYName: globalThis.Array.isArray(object?.listYName)
        ? object.listYName.map((e: any) => globalThis.String(e))
        : [],
      smooth: isSet(object.smooth) ? globalThis.Boolean(object.smooth) : false,
    };
  },

  toJSON(message: LineChart): unknown {
    const obj: any = {};
    if (message.startTime !== 0) {
      obj.startTime = Math.round(message.startTime);
    }
    if (message.endTime !== 0) {
      obj.endTime = Math.round(message.endTime);
    }
    if (message.timeStep !== 0) {
      obj.timeStep = timeStepToJSON(message.timeStep);
    }
    if (message.listY?.length) {
      obj.listY = message.listY.map((e) => LineChart_YAxis.toJSON(e));
    }
    if (message.listYName?.length) {
      obj.listYName = message.listYName;
    }
    if (message.smooth !== false) {
      obj.smooth = message.smooth;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<LineChart>, I>>(base?: I): LineChart {
    return LineChart.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LineChart>, I>>(object: I): LineChart {
    const message = createBaseLineChart();
    message.startTime = object.startTime ?? 0;
    message.endTime = object.endTime ?? 0;
    message.timeStep = object.timeStep ?? 0;
    message.listY = object.listY?.map((e) => LineChart_YAxis.fromPartial(e)) || [];
    message.listYName = object.listYName?.map((e) => e) || [];
    message.smooth = object.smooth ?? false;
    return message;
  },
};

function createBaseLineChart_YAxis(): LineChart_YAxis {
  return { y: [] };
}

export const LineChart_YAxis: MessageFns<LineChart_YAxis> = {
  encode(message: LineChart_YAxis, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.y) {
      writer.double(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LineChart_YAxis {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLineChart_YAxis();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 9) {
            message.y.push(reader.double());

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.y.push(reader.double());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): LineChart_YAxis {
    return { y: globalThis.Array.isArray(object?.y) ? object.y.map((e: any) => globalThis.Number(e)) : [] };
  },

  toJSON(message: LineChart_YAxis): unknown {
    const obj: any = {};
    if (message.y?.length) {
      obj.y = message.y;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<LineChart_YAxis>, I>>(base?: I): LineChart_YAxis {
    return LineChart_YAxis.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LineChart_YAxis>, I>>(object: I): LineChart_YAxis {
    const message = createBaseLineChart_YAxis();
    message.y = object.y?.map((e) => e) || [];
    return message;
  },
};

function createBaseBarChart(): BarChart {
  return { x: [], listY: [], listYName: [], showValue: false };
}

export const BarChart: MessageFns<BarChart> = {
  encode(message: BarChart, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.x) {
      writer.uint32(10).string(v!);
    }
    for (const v of message.listY) {
      BarChart_YAxis.encode(v!, writer.uint32(18).fork()).join();
    }
    for (const v of message.listYName) {
      writer.uint32(26).string(v!);
    }
    if (message.showValue !== false) {
      writer.uint32(32).bool(message.showValue);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BarChart {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBarChart();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.x.push(reader.string());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.listY.push(BarChart_YAxis.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.listYName.push(reader.string());
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.showValue = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BarChart {
    return {
      x: globalThis.Array.isArray(object?.x) ? object.x.map((e: any) => globalThis.String(e)) : [],
      listY: globalThis.Array.isArray(object?.listY) ? object.listY.map((e: any) => BarChart_YAxis.fromJSON(e)) : [],
      listYName: globalThis.Array.isArray(object?.listYName)
        ? object.listYName.map((e: any) => globalThis.String(e))
        : [],
      showValue: isSet(object.showValue) ? globalThis.Boolean(object.showValue) : false,
    };
  },

  toJSON(message: BarChart): unknown {
    const obj: any = {};
    if (message.x?.length) {
      obj.x = message.x;
    }
    if (message.listY?.length) {
      obj.listY = message.listY.map((e) => BarChart_YAxis.toJSON(e));
    }
    if (message.listYName?.length) {
      obj.listYName = message.listYName;
    }
    if (message.showValue !== false) {
      obj.showValue = message.showValue;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BarChart>, I>>(base?: I): BarChart {
    return BarChart.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BarChart>, I>>(object: I): BarChart {
    const message = createBaseBarChart();
    message.x = object.x?.map((e) => e) || [];
    message.listY = object.listY?.map((e) => BarChart_YAxis.fromPartial(e)) || [];
    message.listYName = object.listYName?.map((e) => e) || [];
    message.showValue = object.showValue ?? false;
    return message;
  },
};

function createBaseBarChart_YAxis(): BarChart_YAxis {
  return { y: [] };
}

export const BarChart_YAxis: MessageFns<BarChart_YAxis> = {
  encode(message: BarChart_YAxis, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.y) {
      writer.double(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BarChart_YAxis {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBarChart_YAxis();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 9) {
            message.y.push(reader.double());

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.y.push(reader.double());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BarChart_YAxis {
    return { y: globalThis.Array.isArray(object?.y) ? object.y.map((e: any) => globalThis.Number(e)) : [] };
  },

  toJSON(message: BarChart_YAxis): unknown {
    const obj: any = {};
    if (message.y?.length) {
      obj.y = message.y;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BarChart_YAxis>, I>>(base?: I): BarChart_YAxis {
    return BarChart_YAxis.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BarChart_YAxis>, I>>(object: I): BarChart_YAxis {
    const message = createBaseBarChart_YAxis();
    message.y = object.y?.map((e) => e) || [];
    return message;
  },
};

function createBasePieChart(): PieChart {
  return { labels: [], values: [], showPercent: false, showNumber: false, labelPlacement: false };
}

export const PieChart: MessageFns<PieChart> = {
  encode(message: PieChart, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.labels) {
      writer.uint32(10).string(v!);
    }
    writer.uint32(18).fork();
    for (const v of message.values) {
      writer.double(v);
    }
    writer.join();
    if (message.showPercent !== false) {
      writer.uint32(24).bool(message.showPercent);
    }
    if (message.showNumber !== false) {
      writer.uint32(32).bool(message.showNumber);
    }
    if (message.labelPlacement !== false) {
      writer.uint32(40).bool(message.labelPlacement);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PieChart {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePieChart();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.labels.push(reader.string());
          continue;
        }
        case 2: {
          if (tag === 17) {
            message.values.push(reader.double());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.values.push(reader.double());
            }

            continue;
          }

          break;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.showPercent = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.showNumber = reader.bool();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.labelPlacement = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PieChart {
    return {
      labels: globalThis.Array.isArray(object?.labels) ? object.labels.map((e: any) => globalThis.String(e)) : [],
      values: globalThis.Array.isArray(object?.values) ? object.values.map((e: any) => globalThis.Number(e)) : [],
      showPercent: isSet(object.showPercent) ? globalThis.Boolean(object.showPercent) : false,
      showNumber: isSet(object.showNumber) ? globalThis.Boolean(object.showNumber) : false,
      labelPlacement: isSet(object.labelPlacement) ? globalThis.Boolean(object.labelPlacement) : false,
    };
  },

  toJSON(message: PieChart): unknown {
    const obj: any = {};
    if (message.labels?.length) {
      obj.labels = message.labels;
    }
    if (message.values?.length) {
      obj.values = message.values;
    }
    if (message.showPercent !== false) {
      obj.showPercent = message.showPercent;
    }
    if (message.showNumber !== false) {
      obj.showNumber = message.showNumber;
    }
    if (message.labelPlacement !== false) {
      obj.labelPlacement = message.labelPlacement;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PieChart>, I>>(base?: I): PieChart {
    return PieChart.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PieChart>, I>>(object: I): PieChart {
    const message = createBasePieChart();
    message.labels = object.labels?.map((e) => e) || [];
    message.values = object.values?.map((e) => e) || [];
    message.showPercent = object.showPercent ?? false;
    message.showNumber = object.showNumber ?? false;
    message.labelPlacement = object.labelPlacement ?? false;
    return message;
  },
};

function createBaseChart(): Chart {
  return { pieChart: undefined, lineChart: undefined, barChart: undefined, type: "" };
}

export const Chart: MessageFns<Chart> = {
  encode(message: Chart, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pieChart !== undefined) {
      PieChart.encode(message.pieChart, writer.uint32(10).fork()).join();
    }
    if (message.lineChart !== undefined) {
      LineChart.encode(message.lineChart, writer.uint32(18).fork()).join();
    }
    if (message.barChart !== undefined) {
      BarChart.encode(message.barChart, writer.uint32(26).fork()).join();
    }
    if (message.type !== "") {
      writer.uint32(34).string(message.type);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Chart {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseChart();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.pieChart = PieChart.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.lineChart = LineChart.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.barChart = BarChart.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.type = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Chart {
    return {
      pieChart: isSet(object.pieChart) ? PieChart.fromJSON(object.pieChart) : undefined,
      lineChart: isSet(object.lineChart) ? LineChart.fromJSON(object.lineChart) : undefined,
      barChart: isSet(object.barChart) ? BarChart.fromJSON(object.barChart) : undefined,
      type: isSet(object.type) ? globalThis.String(object.type) : "",
    };
  },

  toJSON(message: Chart): unknown {
    const obj: any = {};
    if (message.pieChart !== undefined) {
      obj.pieChart = PieChart.toJSON(message.pieChart);
    }
    if (message.lineChart !== undefined) {
      obj.lineChart = LineChart.toJSON(message.lineChart);
    }
    if (message.barChart !== undefined) {
      obj.barChart = BarChart.toJSON(message.barChart);
    }
    if (message.type !== "") {
      obj.type = message.type;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Chart>, I>>(base?: I): Chart {
    return Chart.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Chart>, I>>(object: I): Chart {
    const message = createBaseChart();
    message.pieChart = (object.pieChart !== undefined && object.pieChart !== null)
      ? PieChart.fromPartial(object.pieChart)
      : undefined;
    message.lineChart = (object.lineChart !== undefined && object.lineChart !== null)
      ? LineChart.fromPartial(object.lineChart)
      : undefined;
    message.barChart = (object.barChart !== undefined && object.barChart !== null)
      ? BarChart.fromPartial(object.barChart)
      : undefined;
    message.type = object.type ?? "";
    return message;
  },
};

function createBaseBankBalanceCard(): BankBalanceCard {
  return { cardDetails: undefined };
}

export const BankBalanceCard: MessageFns<BankBalanceCard> = {
  encode(message: BankBalanceCard, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.cardDetails !== undefined) {
      BankBalanceCard_CardDetails.encode(message.cardDetails, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankBalanceCard {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankBalanceCard();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.cardDetails = BankBalanceCard_CardDetails.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankBalanceCard {
    return {
      cardDetails: isSet(object.cardDetails) ? BankBalanceCard_CardDetails.fromJSON(object.cardDetails) : undefined,
    };
  },

  toJSON(message: BankBalanceCard): unknown {
    const obj: any = {};
    if (message.cardDetails !== undefined) {
      obj.cardDetails = BankBalanceCard_CardDetails.toJSON(message.cardDetails);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankBalanceCard>, I>>(base?: I): BankBalanceCard {
    return BankBalanceCard.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankBalanceCard>, I>>(object: I): BankBalanceCard {
    const message = createBaseBankBalanceCard();
    message.cardDetails = (object.cardDetails !== undefined && object.cardDetails !== null)
      ? BankBalanceCard_CardDetails.fromPartial(object.cardDetails)
      : undefined;
    return message;
  },
};

function createBaseBankBalanceCard_CardDetails(): BankBalanceCard_CardDetails {
  return { title: "", value: 0, chart: undefined, errorInfo: [] };
}

export const BankBalanceCard_CardDetails: MessageFns<BankBalanceCard_CardDetails> = {
  encode(message: BankBalanceCard_CardDetails, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.title !== "") {
      writer.uint32(10).string(message.title);
    }
    if (message.value !== 0) {
      writer.uint32(17).double(message.value);
    }
    if (message.chart !== undefined) {
      Chart.encode(message.chart, writer.uint32(26).fork()).join();
    }
    for (const v of message.errorInfo) {
      writer.uint32(34).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankBalanceCard_CardDetails {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankBalanceCard_CardDetails();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.value = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.chart = Chart.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.errorInfo.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankBalanceCard_CardDetails {
    return {
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      value: isSet(object.value) ? globalThis.Number(object.value) : 0,
      chart: isSet(object.chart) ? Chart.fromJSON(object.chart) : undefined,
      errorInfo: globalThis.Array.isArray(object?.errorInfo)
        ? object.errorInfo.map((e: any) => globalThis.String(e))
        : [],
    };
  },

  toJSON(message: BankBalanceCard_CardDetails): unknown {
    const obj: any = {};
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.value !== 0) {
      obj.value = message.value;
    }
    if (message.chart !== undefined) {
      obj.chart = Chart.toJSON(message.chart);
    }
    if (message.errorInfo?.length) {
      obj.errorInfo = message.errorInfo;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankBalanceCard_CardDetails>, I>>(base?: I): BankBalanceCard_CardDetails {
    return BankBalanceCard_CardDetails.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankBalanceCard_CardDetails>, I>>(object: I): BankBalanceCard_CardDetails {
    const message = createBaseBankBalanceCard_CardDetails();
    message.title = object.title ?? "";
    message.value = object.value ?? 0;
    message.chart = (object.chart !== undefined && object.chart !== null) ? Chart.fromPartial(object.chart) : undefined;
    message.errorInfo = object.errorInfo?.map((e) => e) || [];
    return message;
  },
};

function createBaseTotalWealthCard(): TotalWealthCard {
  return { cardDetails: undefined };
}

export const TotalWealthCard: MessageFns<TotalWealthCard> = {
  encode(message: TotalWealthCard, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.cardDetails !== undefined) {
      TotalWealthCard_CardDetails.encode(message.cardDetails, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TotalWealthCard {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTotalWealthCard();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.cardDetails = TotalWealthCard_CardDetails.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TotalWealthCard {
    return {
      cardDetails: isSet(object.cardDetails) ? TotalWealthCard_CardDetails.fromJSON(object.cardDetails) : undefined,
    };
  },

  toJSON(message: TotalWealthCard): unknown {
    const obj: any = {};
    if (message.cardDetails !== undefined) {
      obj.cardDetails = TotalWealthCard_CardDetails.toJSON(message.cardDetails);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TotalWealthCard>, I>>(base?: I): TotalWealthCard {
    return TotalWealthCard.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TotalWealthCard>, I>>(object: I): TotalWealthCard {
    const message = createBaseTotalWealthCard();
    message.cardDetails = (object.cardDetails !== undefined && object.cardDetails !== null)
      ? TotalWealthCard_CardDetails.fromPartial(object.cardDetails)
      : undefined;
    return message;
  },
};

function createBaseTotalWealthCard_CardDetails(): TotalWealthCard_CardDetails {
  return { title: "", subTitle: "", chart: undefined, errorInfo: [] };
}

export const TotalWealthCard_CardDetails: MessageFns<TotalWealthCard_CardDetails> = {
  encode(message: TotalWealthCard_CardDetails, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.title !== "") {
      writer.uint32(10).string(message.title);
    }
    if (message.subTitle !== "") {
      writer.uint32(18).string(message.subTitle);
    }
    if (message.chart !== undefined) {
      Chart.encode(message.chart, writer.uint32(26).fork()).join();
    }
    for (const v of message.errorInfo) {
      writer.uint32(34).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TotalWealthCard_CardDetails {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTotalWealthCard_CardDetails();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.subTitle = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.chart = Chart.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.errorInfo.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TotalWealthCard_CardDetails {
    return {
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      subTitle: isSet(object.subTitle) ? globalThis.String(object.subTitle) : "",
      chart: isSet(object.chart) ? Chart.fromJSON(object.chart) : undefined,
      errorInfo: globalThis.Array.isArray(object?.errorInfo)
        ? object.errorInfo.map((e: any) => globalThis.String(e))
        : [],
    };
  },

  toJSON(message: TotalWealthCard_CardDetails): unknown {
    const obj: any = {};
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.subTitle !== "") {
      obj.subTitle = message.subTitle;
    }
    if (message.chart !== undefined) {
      obj.chart = Chart.toJSON(message.chart);
    }
    if (message.errorInfo?.length) {
      obj.errorInfo = message.errorInfo;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TotalWealthCard_CardDetails>, I>>(base?: I): TotalWealthCard_CardDetails {
    return TotalWealthCard_CardDetails.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TotalWealthCard_CardDetails>, I>>(object: I): TotalWealthCard_CardDetails {
    const message = createBaseTotalWealthCard_CardDetails();
    message.title = object.title ?? "";
    message.subTitle = object.subTitle ?? "";
    message.chart = (object.chart !== undefined && object.chart !== null) ? Chart.fromPartial(object.chart) : undefined;
    message.errorInfo = object.errorInfo?.map((e) => e) || [];
    return message;
  },
};

function createBaseEquitiesXIRRCard(): EquitiesXIRRCard {
  return { cardDetails: undefined };
}

export const EquitiesXIRRCard: MessageFns<EquitiesXIRRCard> = {
  encode(message: EquitiesXIRRCard, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.cardDetails !== undefined) {
      EquitiesXIRRCard_CardDetails.encode(message.cardDetails, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EquitiesXIRRCard {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEquitiesXIRRCard();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.cardDetails = EquitiesXIRRCard_CardDetails.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): EquitiesXIRRCard {
    return {
      cardDetails: isSet(object.cardDetails) ? EquitiesXIRRCard_CardDetails.fromJSON(object.cardDetails) : undefined,
    };
  },

  toJSON(message: EquitiesXIRRCard): unknown {
    const obj: any = {};
    if (message.cardDetails !== undefined) {
      obj.cardDetails = EquitiesXIRRCard_CardDetails.toJSON(message.cardDetails);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<EquitiesXIRRCard>, I>>(base?: I): EquitiesXIRRCard {
    return EquitiesXIRRCard.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EquitiesXIRRCard>, I>>(object: I): EquitiesXIRRCard {
    const message = createBaseEquitiesXIRRCard();
    message.cardDetails = (object.cardDetails !== undefined && object.cardDetails !== null)
      ? EquitiesXIRRCard_CardDetails.fromPartial(object.cardDetails)
      : undefined;
    return message;
  },
};

function createBaseEquitiesXIRRCard_CardDetails(): EquitiesXIRRCard_CardDetails {
  return {
    title: "",
    subTitle1: "",
    subValue1: 0,
    subTitle2: "",
    subValue2: 0,
    rateTitle: "",
    rate: 0,
    chart: undefined,
    errorInfo: [],
  };
}

export const EquitiesXIRRCard_CardDetails: MessageFns<EquitiesXIRRCard_CardDetails> = {
  encode(message: EquitiesXIRRCard_CardDetails, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.title !== "") {
      writer.uint32(10).string(message.title);
    }
    if (message.subTitle1 !== "") {
      writer.uint32(18).string(message.subTitle1);
    }
    if (message.subValue1 !== 0) {
      writer.uint32(25).double(message.subValue1);
    }
    if (message.subTitle2 !== "") {
      writer.uint32(34).string(message.subTitle2);
    }
    if (message.subValue2 !== 0) {
      writer.uint32(41).double(message.subValue2);
    }
    if (message.rateTitle !== "") {
      writer.uint32(50).string(message.rateTitle);
    }
    if (message.rate !== 0) {
      writer.uint32(57).double(message.rate);
    }
    if (message.chart !== undefined) {
      Chart.encode(message.chart, writer.uint32(66).fork()).join();
    }
    for (const v of message.errorInfo) {
      writer.uint32(74).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EquitiesXIRRCard_CardDetails {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEquitiesXIRRCard_CardDetails();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.subTitle1 = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.subValue1 = reader.double();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.subTitle2 = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 41) {
            break;
          }

          message.subValue2 = reader.double();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.rateTitle = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 57) {
            break;
          }

          message.rate = reader.double();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.chart = Chart.decode(reader, reader.uint32());
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.errorInfo.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): EquitiesXIRRCard_CardDetails {
    return {
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      subTitle1: isSet(object.subTitle1) ? globalThis.String(object.subTitle1) : "",
      subValue1: isSet(object.subValue1) ? globalThis.Number(object.subValue1) : 0,
      subTitle2: isSet(object.subTitle2) ? globalThis.String(object.subTitle2) : "",
      subValue2: isSet(object.subValue2) ? globalThis.Number(object.subValue2) : 0,
      rateTitle: isSet(object.rateTitle) ? globalThis.String(object.rateTitle) : "",
      rate: isSet(object.rate) ? globalThis.Number(object.rate) : 0,
      chart: isSet(object.chart) ? Chart.fromJSON(object.chart) : undefined,
      errorInfo: globalThis.Array.isArray(object?.errorInfo)
        ? object.errorInfo.map((e: any) => globalThis.String(e))
        : [],
    };
  },

  toJSON(message: EquitiesXIRRCard_CardDetails): unknown {
    const obj: any = {};
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.subTitle1 !== "") {
      obj.subTitle1 = message.subTitle1;
    }
    if (message.subValue1 !== 0) {
      obj.subValue1 = message.subValue1;
    }
    if (message.subTitle2 !== "") {
      obj.subTitle2 = message.subTitle2;
    }
    if (message.subValue2 !== 0) {
      obj.subValue2 = message.subValue2;
    }
    if (message.rateTitle !== "") {
      obj.rateTitle = message.rateTitle;
    }
    if (message.rate !== 0) {
      obj.rate = message.rate;
    }
    if (message.chart !== undefined) {
      obj.chart = Chart.toJSON(message.chart);
    }
    if (message.errorInfo?.length) {
      obj.errorInfo = message.errorInfo;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<EquitiesXIRRCard_CardDetails>, I>>(base?: I): EquitiesXIRRCard_CardDetails {
    return EquitiesXIRRCard_CardDetails.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EquitiesXIRRCard_CardDetails>, I>>(object: I): EquitiesXIRRCard_CardDetails {
    const message = createBaseEquitiesXIRRCard_CardDetails();
    message.title = object.title ?? "";
    message.subTitle1 = object.subTitle1 ?? "";
    message.subValue1 = object.subValue1 ?? 0;
    message.subTitle2 = object.subTitle2 ?? "";
    message.subValue2 = object.subValue2 ?? 0;
    message.rateTitle = object.rateTitle ?? "";
    message.rate = object.rate ?? 0;
    message.chart = (object.chart !== undefined && object.chart !== null) ? Chart.fromPartial(object.chart) : undefined;
    message.errorInfo = object.errorInfo?.map((e) => e) || [];
    return message;
  },
};

function createBaseEquitiesReturnsCard(): EquitiesReturnsCard {
  return { cardDetails: undefined };
}

export const EquitiesReturnsCard: MessageFns<EquitiesReturnsCard> = {
  encode(message: EquitiesReturnsCard, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.cardDetails !== undefined) {
      EquitiesReturnsCard_CardDetails.encode(message.cardDetails, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EquitiesReturnsCard {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEquitiesReturnsCard();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.cardDetails = EquitiesReturnsCard_CardDetails.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): EquitiesReturnsCard {
    return {
      cardDetails: isSet(object.cardDetails) ? EquitiesReturnsCard_CardDetails.fromJSON(object.cardDetails) : undefined,
    };
  },

  toJSON(message: EquitiesReturnsCard): unknown {
    const obj: any = {};
    if (message.cardDetails !== undefined) {
      obj.cardDetails = EquitiesReturnsCard_CardDetails.toJSON(message.cardDetails);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<EquitiesReturnsCard>, I>>(base?: I): EquitiesReturnsCard {
    return EquitiesReturnsCard.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EquitiesReturnsCard>, I>>(object: I): EquitiesReturnsCard {
    const message = createBaseEquitiesReturnsCard();
    message.cardDetails = (object.cardDetails !== undefined && object.cardDetails !== null)
      ? EquitiesReturnsCard_CardDetails.fromPartial(object.cardDetails)
      : undefined;
    return message;
  },
};

function createBaseEquitiesReturnsCard_CardDetails(): EquitiesReturnsCard_CardDetails {
  return { title: "", subTitle: [], value: [], chart: undefined, errorInfo: [] };
}

export const EquitiesReturnsCard_CardDetails: MessageFns<EquitiesReturnsCard_CardDetails> = {
  encode(message: EquitiesReturnsCard_CardDetails, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.title !== "") {
      writer.uint32(10).string(message.title);
    }
    for (const v of message.subTitle) {
      writer.uint32(18).string(v!);
    }
    writer.uint32(26).fork();
    for (const v of message.value) {
      writer.double(v);
    }
    writer.join();
    if (message.chart !== undefined) {
      Chart.encode(message.chart, writer.uint32(34).fork()).join();
    }
    for (const v of message.errorInfo) {
      writer.uint32(42).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EquitiesReturnsCard_CardDetails {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEquitiesReturnsCard_CardDetails();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.subTitle.push(reader.string());
          continue;
        }
        case 3: {
          if (tag === 25) {
            message.value.push(reader.double());

            continue;
          }

          if (tag === 26) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.value.push(reader.double());
            }

            continue;
          }

          break;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.chart = Chart.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.errorInfo.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): EquitiesReturnsCard_CardDetails {
    return {
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      subTitle: globalThis.Array.isArray(object?.subTitle) ? object.subTitle.map((e: any) => globalThis.String(e)) : [],
      value: globalThis.Array.isArray(object?.value) ? object.value.map((e: any) => globalThis.Number(e)) : [],
      chart: isSet(object.chart) ? Chart.fromJSON(object.chart) : undefined,
      errorInfo: globalThis.Array.isArray(object?.errorInfo)
        ? object.errorInfo.map((e: any) => globalThis.String(e))
        : [],
    };
  },

  toJSON(message: EquitiesReturnsCard_CardDetails): unknown {
    const obj: any = {};
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.subTitle?.length) {
      obj.subTitle = message.subTitle;
    }
    if (message.value?.length) {
      obj.value = message.value;
    }
    if (message.chart !== undefined) {
      obj.chart = Chart.toJSON(message.chart);
    }
    if (message.errorInfo?.length) {
      obj.errorInfo = message.errorInfo;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<EquitiesReturnsCard_CardDetails>, I>>(base?: I): EquitiesReturnsCard_CardDetails {
    return EquitiesReturnsCard_CardDetails.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EquitiesReturnsCard_CardDetails>, I>>(
    object: I,
  ): EquitiesReturnsCard_CardDetails {
    const message = createBaseEquitiesReturnsCard_CardDetails();
    message.title = object.title ?? "";
    message.subTitle = object.subTitle?.map((e) => e) || [];
    message.value = object.value?.map((e) => e) || [];
    message.chart = (object.chart !== undefined && object.chart !== null) ? Chart.fromPartial(object.chart) : undefined;
    message.errorInfo = object.errorInfo?.map((e) => e) || [];
    return message;
  },
};

function createBaseMutualFundsAccountSummaryCard(): MutualFundsAccountSummaryCard {
  return { cardDetails: undefined };
}

export const MutualFundsAccountSummaryCard: MessageFns<MutualFundsAccountSummaryCard> = {
  encode(message: MutualFundsAccountSummaryCard, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.cardDetails !== undefined) {
      MutualFundsAccountSummaryCard_CardDetails.encode(message.cardDetails, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MutualFundsAccountSummaryCard {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMutualFundsAccountSummaryCard();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.cardDetails = MutualFundsAccountSummaryCard_CardDetails.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MutualFundsAccountSummaryCard {
    return {
      cardDetails: isSet(object.cardDetails)
        ? MutualFundsAccountSummaryCard_CardDetails.fromJSON(object.cardDetails)
        : undefined,
    };
  },

  toJSON(message: MutualFundsAccountSummaryCard): unknown {
    const obj: any = {};
    if (message.cardDetails !== undefined) {
      obj.cardDetails = MutualFundsAccountSummaryCard_CardDetails.toJSON(message.cardDetails);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MutualFundsAccountSummaryCard>, I>>(base?: I): MutualFundsAccountSummaryCard {
    return MutualFundsAccountSummaryCard.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MutualFundsAccountSummaryCard>, I>>(
    object: I,
  ): MutualFundsAccountSummaryCard {
    const message = createBaseMutualFundsAccountSummaryCard();
    message.cardDetails = (object.cardDetails !== undefined && object.cardDetails !== null)
      ? MutualFundsAccountSummaryCard_CardDetails.fromPartial(object.cardDetails)
      : undefined;
    return message;
  },
};

function createBaseMutualFundsAccountSummaryCard_CardDetails(): MutualFundsAccountSummaryCard_CardDetails {
  return { fipId: "", title: "", subTitle: "", value: 0, subValue: 0, holdingsDate: 0, errorInfo: [] };
}

export const MutualFundsAccountSummaryCard_CardDetails: MessageFns<MutualFundsAccountSummaryCard_CardDetails> = {
  encode(message: MutualFundsAccountSummaryCard_CardDetails, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.fipId !== "") {
      writer.uint32(10).string(message.fipId);
    }
    if (message.title !== "") {
      writer.uint32(18).string(message.title);
    }
    if (message.subTitle !== "") {
      writer.uint32(26).string(message.subTitle);
    }
    if (message.value !== 0) {
      writer.uint32(33).double(message.value);
    }
    if (message.subValue !== 0) {
      writer.uint32(41).double(message.subValue);
    }
    if (message.holdingsDate !== 0) {
      writer.uint32(56).int64(message.holdingsDate);
    }
    for (const v of message.errorInfo) {
      writer.uint32(66).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MutualFundsAccountSummaryCard_CardDetails {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMutualFundsAccountSummaryCard_CardDetails();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.fipId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.subTitle = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.value = reader.double();
          continue;
        }
        case 5: {
          if (tag !== 41) {
            break;
          }

          message.subValue = reader.double();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.holdingsDate = longToNumber(reader.int64());
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.errorInfo.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MutualFundsAccountSummaryCard_CardDetails {
    return {
      fipId: isSet(object.fipId) ? globalThis.String(object.fipId) : "",
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      subTitle: isSet(object.subTitle) ? globalThis.String(object.subTitle) : "",
      value: isSet(object.value) ? globalThis.Number(object.value) : 0,
      subValue: isSet(object.subValue) ? globalThis.Number(object.subValue) : 0,
      holdingsDate: isSet(object.holdingsDate) ? globalThis.Number(object.holdingsDate) : 0,
      errorInfo: globalThis.Array.isArray(object?.errorInfo)
        ? object.errorInfo.map((e: any) => globalThis.String(e))
        : [],
    };
  },

  toJSON(message: MutualFundsAccountSummaryCard_CardDetails): unknown {
    const obj: any = {};
    if (message.fipId !== "") {
      obj.fipId = message.fipId;
    }
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.subTitle !== "") {
      obj.subTitle = message.subTitle;
    }
    if (message.value !== 0) {
      obj.value = message.value;
    }
    if (message.subValue !== 0) {
      obj.subValue = message.subValue;
    }
    if (message.holdingsDate !== 0) {
      obj.holdingsDate = Math.round(message.holdingsDate);
    }
    if (message.errorInfo?.length) {
      obj.errorInfo = message.errorInfo;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MutualFundsAccountSummaryCard_CardDetails>, I>>(
    base?: I,
  ): MutualFundsAccountSummaryCard_CardDetails {
    return MutualFundsAccountSummaryCard_CardDetails.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MutualFundsAccountSummaryCard_CardDetails>, I>>(
    object: I,
  ): MutualFundsAccountSummaryCard_CardDetails {
    const message = createBaseMutualFundsAccountSummaryCard_CardDetails();
    message.fipId = object.fipId ?? "";
    message.title = object.title ?? "";
    message.subTitle = object.subTitle ?? "";
    message.value = object.value ?? 0;
    message.subValue = object.subValue ?? 0;
    message.holdingsDate = object.holdingsDate ?? 0;
    message.errorInfo = object.errorInfo?.map((e) => e) || [];
    return message;
  },
};

function createBaseMutualFundsXIRRCard(): MutualFundsXIRRCard {
  return { cardDetails: undefined };
}

export const MutualFundsXIRRCard: MessageFns<MutualFundsXIRRCard> = {
  encode(message: MutualFundsXIRRCard, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.cardDetails !== undefined) {
      MutualFundsXIRRCard_CardDetails.encode(message.cardDetails, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MutualFundsXIRRCard {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMutualFundsXIRRCard();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.cardDetails = MutualFundsXIRRCard_CardDetails.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MutualFundsXIRRCard {
    return {
      cardDetails: isSet(object.cardDetails) ? MutualFundsXIRRCard_CardDetails.fromJSON(object.cardDetails) : undefined,
    };
  },

  toJSON(message: MutualFundsXIRRCard): unknown {
    const obj: any = {};
    if (message.cardDetails !== undefined) {
      obj.cardDetails = MutualFundsXIRRCard_CardDetails.toJSON(message.cardDetails);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MutualFundsXIRRCard>, I>>(base?: I): MutualFundsXIRRCard {
    return MutualFundsXIRRCard.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MutualFundsXIRRCard>, I>>(object: I): MutualFundsXIRRCard {
    const message = createBaseMutualFundsXIRRCard();
    message.cardDetails = (object.cardDetails !== undefined && object.cardDetails !== null)
      ? MutualFundsXIRRCard_CardDetails.fromPartial(object.cardDetails)
      : undefined;
    return message;
  },
};

function createBaseMutualFundsXIRRCard_CardDetails(): MutualFundsXIRRCard_CardDetails {
  return {
    title: "",
    subTitle1: "",
    subValue1: 0,
    subTitle2: "",
    subValue2: 0,
    rateTitle: "",
    rate: 0,
    chart: undefined,
    errorInfo: [],
  };
}

export const MutualFundsXIRRCard_CardDetails: MessageFns<MutualFundsXIRRCard_CardDetails> = {
  encode(message: MutualFundsXIRRCard_CardDetails, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.title !== "") {
      writer.uint32(10).string(message.title);
    }
    if (message.subTitle1 !== "") {
      writer.uint32(18).string(message.subTitle1);
    }
    if (message.subValue1 !== 0) {
      writer.uint32(25).double(message.subValue1);
    }
    if (message.subTitle2 !== "") {
      writer.uint32(34).string(message.subTitle2);
    }
    if (message.subValue2 !== 0) {
      writer.uint32(41).double(message.subValue2);
    }
    if (message.rateTitle !== "") {
      writer.uint32(50).string(message.rateTitle);
    }
    if (message.rate !== 0) {
      writer.uint32(57).double(message.rate);
    }
    if (message.chart !== undefined) {
      Chart.encode(message.chart, writer.uint32(66).fork()).join();
    }
    for (const v of message.errorInfo) {
      writer.uint32(74).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MutualFundsXIRRCard_CardDetails {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMutualFundsXIRRCard_CardDetails();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.subTitle1 = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.subValue1 = reader.double();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.subTitle2 = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 41) {
            break;
          }

          message.subValue2 = reader.double();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.rateTitle = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 57) {
            break;
          }

          message.rate = reader.double();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.chart = Chart.decode(reader, reader.uint32());
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.errorInfo.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MutualFundsXIRRCard_CardDetails {
    return {
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      subTitle1: isSet(object.subTitle1) ? globalThis.String(object.subTitle1) : "",
      subValue1: isSet(object.subValue1) ? globalThis.Number(object.subValue1) : 0,
      subTitle2: isSet(object.subTitle2) ? globalThis.String(object.subTitle2) : "",
      subValue2: isSet(object.subValue2) ? globalThis.Number(object.subValue2) : 0,
      rateTitle: isSet(object.rateTitle) ? globalThis.String(object.rateTitle) : "",
      rate: isSet(object.rate) ? globalThis.Number(object.rate) : 0,
      chart: isSet(object.chart) ? Chart.fromJSON(object.chart) : undefined,
      errorInfo: globalThis.Array.isArray(object?.errorInfo)
        ? object.errorInfo.map((e: any) => globalThis.String(e))
        : [],
    };
  },

  toJSON(message: MutualFundsXIRRCard_CardDetails): unknown {
    const obj: any = {};
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.subTitle1 !== "") {
      obj.subTitle1 = message.subTitle1;
    }
    if (message.subValue1 !== 0) {
      obj.subValue1 = message.subValue1;
    }
    if (message.subTitle2 !== "") {
      obj.subTitle2 = message.subTitle2;
    }
    if (message.subValue2 !== 0) {
      obj.subValue2 = message.subValue2;
    }
    if (message.rateTitle !== "") {
      obj.rateTitle = message.rateTitle;
    }
    if (message.rate !== 0) {
      obj.rate = message.rate;
    }
    if (message.chart !== undefined) {
      obj.chart = Chart.toJSON(message.chart);
    }
    if (message.errorInfo?.length) {
      obj.errorInfo = message.errorInfo;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MutualFundsXIRRCard_CardDetails>, I>>(base?: I): MutualFundsXIRRCard_CardDetails {
    return MutualFundsXIRRCard_CardDetails.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MutualFundsXIRRCard_CardDetails>, I>>(
    object: I,
  ): MutualFundsXIRRCard_CardDetails {
    const message = createBaseMutualFundsXIRRCard_CardDetails();
    message.title = object.title ?? "";
    message.subTitle1 = object.subTitle1 ?? "";
    message.subValue1 = object.subValue1 ?? 0;
    message.subTitle2 = object.subTitle2 ?? "";
    message.subValue2 = object.subValue2 ?? 0;
    message.rateTitle = object.rateTitle ?? "";
    message.rate = object.rate ?? 0;
    message.chart = (object.chart !== undefined && object.chart !== null) ? Chart.fromPartial(object.chart) : undefined;
    message.errorInfo = object.errorInfo?.map((e) => e) || [];
    return message;
  },
};

function createBaseMutualFundsReturnsCard(): MutualFundsReturnsCard {
  return { cardDetails: undefined };
}

export const MutualFundsReturnsCard: MessageFns<MutualFundsReturnsCard> = {
  encode(message: MutualFundsReturnsCard, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.cardDetails !== undefined) {
      MutualFundsReturnsCard_CardDetails.encode(message.cardDetails, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MutualFundsReturnsCard {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMutualFundsReturnsCard();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.cardDetails = MutualFundsReturnsCard_CardDetails.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MutualFundsReturnsCard {
    return {
      cardDetails: isSet(object.cardDetails)
        ? MutualFundsReturnsCard_CardDetails.fromJSON(object.cardDetails)
        : undefined,
    };
  },

  toJSON(message: MutualFundsReturnsCard): unknown {
    const obj: any = {};
    if (message.cardDetails !== undefined) {
      obj.cardDetails = MutualFundsReturnsCard_CardDetails.toJSON(message.cardDetails);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MutualFundsReturnsCard>, I>>(base?: I): MutualFundsReturnsCard {
    return MutualFundsReturnsCard.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MutualFundsReturnsCard>, I>>(object: I): MutualFundsReturnsCard {
    const message = createBaseMutualFundsReturnsCard();
    message.cardDetails = (object.cardDetails !== undefined && object.cardDetails !== null)
      ? MutualFundsReturnsCard_CardDetails.fromPartial(object.cardDetails)
      : undefined;
    return message;
  },
};

function createBaseMutualFundsReturnsCard_CardDetails(): MutualFundsReturnsCard_CardDetails {
  return { title: "", subTitle: [], value: [], chart: undefined, errorInfo: [] };
}

export const MutualFundsReturnsCard_CardDetails: MessageFns<MutualFundsReturnsCard_CardDetails> = {
  encode(message: MutualFundsReturnsCard_CardDetails, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.title !== "") {
      writer.uint32(10).string(message.title);
    }
    for (const v of message.subTitle) {
      writer.uint32(18).string(v!);
    }
    writer.uint32(26).fork();
    for (const v of message.value) {
      writer.double(v);
    }
    writer.join();
    if (message.chart !== undefined) {
      Chart.encode(message.chart, writer.uint32(34).fork()).join();
    }
    for (const v of message.errorInfo) {
      writer.uint32(42).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MutualFundsReturnsCard_CardDetails {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMutualFundsReturnsCard_CardDetails();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.subTitle.push(reader.string());
          continue;
        }
        case 3: {
          if (tag === 25) {
            message.value.push(reader.double());

            continue;
          }

          if (tag === 26) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.value.push(reader.double());
            }

            continue;
          }

          break;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.chart = Chart.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.errorInfo.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MutualFundsReturnsCard_CardDetails {
    return {
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      subTitle: globalThis.Array.isArray(object?.subTitle) ? object.subTitle.map((e: any) => globalThis.String(e)) : [],
      value: globalThis.Array.isArray(object?.value) ? object.value.map((e: any) => globalThis.Number(e)) : [],
      chart: isSet(object.chart) ? Chart.fromJSON(object.chart) : undefined,
      errorInfo: globalThis.Array.isArray(object?.errorInfo)
        ? object.errorInfo.map((e: any) => globalThis.String(e))
        : [],
    };
  },

  toJSON(message: MutualFundsReturnsCard_CardDetails): unknown {
    const obj: any = {};
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.subTitle?.length) {
      obj.subTitle = message.subTitle;
    }
    if (message.value?.length) {
      obj.value = message.value;
    }
    if (message.chart !== undefined) {
      obj.chart = Chart.toJSON(message.chart);
    }
    if (message.errorInfo?.length) {
      obj.errorInfo = message.errorInfo;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MutualFundsReturnsCard_CardDetails>, I>>(
    base?: I,
  ): MutualFundsReturnsCard_CardDetails {
    return MutualFundsReturnsCard_CardDetails.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MutualFundsReturnsCard_CardDetails>, I>>(
    object: I,
  ): MutualFundsReturnsCard_CardDetails {
    const message = createBaseMutualFundsReturnsCard_CardDetails();
    message.title = object.title ?? "";
    message.subTitle = object.subTitle?.map((e) => e) || [];
    message.value = object.value?.map((e) => e) || [];
    message.chart = (object.chart !== undefined && object.chart !== null) ? Chart.fromPartial(object.chart) : undefined;
    message.errorInfo = object.errorInfo?.map((e) => e) || [];
    return message;
  },
};

/** Service for Cards Fetch */
export type VisualizationService = typeof VisualizationService;
export const VisualizationService = {
  fetchCards: {
    path: "/backend_services.visualization.Visualization/FetchCards",
    requestStream: false,
    responseStream: true,
    requestSerialize: (value: FetchCardsRequest) => Buffer.from(FetchCardsRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => FetchCardsRequest.decode(value),
    responseSerialize: (value: FetchCardsResponse) => Buffer.from(FetchCardsResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => FetchCardsResponse.decode(value),
  },
  applyUserFilters: {
    path: "/backend_services.visualization.Visualization/ApplyUserFilters",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: ApplyUserFiltersRequest) => Buffer.from(ApplyUserFiltersRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => ApplyUserFiltersRequest.decode(value),
    responseSerialize: (value: Card) => Buffer.from(Card.encode(value).finish()),
    responseDeserialize: (value: Buffer) => Card.decode(value),
  },
  saveUserFilters: {
    path: "/backend_services.visualization.Visualization/SaveUserFilters",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: SaveUserFiltersRequest) => Buffer.from(SaveUserFiltersRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => SaveUserFiltersRequest.decode(value),
    responseSerialize: (value: SaveUserFiltersResponse) => Buffer.from(SaveUserFiltersResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => SaveUserFiltersResponse.decode(value),
  },
  updatePriorities: {
    path: "/backend_services.visualization.Visualization/UpdatePriorities",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: UpdatePrioritiesRequest) => Buffer.from(UpdatePrioritiesRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => UpdatePrioritiesRequest.decode(value),
    responseSerialize: (value: UpdatePrioritiesResponse) =>
      Buffer.from(UpdatePrioritiesResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => UpdatePrioritiesResponse.decode(value),
  },
} as const;

export interface VisualizationServer extends UntypedServiceImplementation {
  fetchCards: handleServerStreamingCall<FetchCardsRequest, FetchCardsResponse>;
  applyUserFilters: handleUnaryCall<ApplyUserFiltersRequest, Card>;
  saveUserFilters: handleUnaryCall<SaveUserFiltersRequest, SaveUserFiltersResponse>;
  updatePriorities: handleUnaryCall<UpdatePrioritiesRequest, UpdatePrioritiesResponse>;
}

export interface VisualizationClient extends Client {
  fetchCards(request: FetchCardsRequest, options?: Partial<CallOptions>): ClientReadableStream<FetchCardsResponse>;
  fetchCards(
    request: FetchCardsRequest,
    metadata?: Metadata,
    options?: Partial<CallOptions>,
  ): ClientReadableStream<FetchCardsResponse>;
  applyUserFilters(
    request: ApplyUserFiltersRequest,
    callback: (error: ServiceError | null, response: Card) => void,
  ): ClientUnaryCall;
  applyUserFilters(
    request: ApplyUserFiltersRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: Card) => void,
  ): ClientUnaryCall;
  applyUserFilters(
    request: ApplyUserFiltersRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: Card) => void,
  ): ClientUnaryCall;
  saveUserFilters(
    request: SaveUserFiltersRequest,
    callback: (error: ServiceError | null, response: SaveUserFiltersResponse) => void,
  ): ClientUnaryCall;
  saveUserFilters(
    request: SaveUserFiltersRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: SaveUserFiltersResponse) => void,
  ): ClientUnaryCall;
  saveUserFilters(
    request: SaveUserFiltersRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: SaveUserFiltersResponse) => void,
  ): ClientUnaryCall;
  updatePriorities(
    request: UpdatePrioritiesRequest,
    callback: (error: ServiceError | null, response: UpdatePrioritiesResponse) => void,
  ): ClientUnaryCall;
  updatePriorities(
    request: UpdatePrioritiesRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: UpdatePrioritiesResponse) => void,
  ): ClientUnaryCall;
  updatePriorities(
    request: UpdatePrioritiesRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: UpdatePrioritiesResponse) => void,
  ): ClientUnaryCall;
}

export const VisualizationClient = makeGenericClientConstructor(
  VisualizationService,
  "backend_services.visualization.Visualization",
) as unknown as {
  new (address: string, credentials: ChannelCredentials, options?: Partial<ClientOptions>): VisualizationClient;
  service: typeof VisualizationService;
  serviceName: string;
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
