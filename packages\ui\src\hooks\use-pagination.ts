"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { TransactionGroup } from "../components/TransactionsTable";
import { mergeTransactionGroups } from "../lib/helper";

type UsePaginationOptions<TransactionGroup> = {
  initialData: TransactionGroup[];
  initialPage: number;
  fetchMore: (page: number) => Promise<TransactionGroup[]>;
};

export function usePagination({
  initialData,
  initialPage,
  fetchMore,
}: UsePaginationOptions<TransactionGroup>) {
  const [data, setData] = useState<TransactionGroup[]>(initialData);
  const [page, setPage] = useState(initialPage);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const observer = useRef<IntersectionObserver | null>(null);

  const lastItemRef = useCallback(
    (node: HTMLElement) => {
      if (loading || !hasMore) return;
      if (observer.current) observer.current.disconnect();

      observer.current = new IntersectionObserver((entries) => {
        if (entries[0]?.isIntersecting) {
          setPage((prevPage) => prevPage + 1);
        }
      });

      if (node) observer.current.observe(node);
    },
    [loading, hasMore],
  );

  useEffect(() => {
    if (page === initialPage) return;
    setLoading(true);
    fetchMore(page)
      .then((newData) => {
        if (newData.length === 0) {
          setHasMore(false);
        } else {
          setData((prev) => mergeTransactionGroups(prev, newData));
        }
      })
      .finally(() => setLoading(false));
  }, [page]);

  const resetPagination = useCallback(() => {
    setData(initialData);
    setPage(initialPage);
    setHasMore(true);
  }, [initialData, initialPage]);

  return {
    data,
    lastItemRef,
    setPage,
    hasMore,
    setHasMore,
    resetPagination,
  };
}
