import * as React from "react";
import type { SVGProps } from "react";
const SvgClothing = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="currentColor"
    viewBox="0 0 37 31"
    {...props}
  >
    <path d="M7.211.711c.2-.174.45-.27.71-.27h2.416c.19 2.7 2.287 4.815 4.83 4.815s4.64-2.116 4.83-4.814h2.416c.26 0 .51.095.71.27l6.26 5.43c.377.33.512.878.342 1.361l-1.516 4.29c-.235.671-.97.979-1.573.645l-1.777-1.111a.34.34 0 0 0-.366.001.41.41 0 0 0-.182.347V24.11c0 .657-.497 1.204-1.127 1.204H7.149c-.63 0-1.127-.547-1.127-1.204V11.675a.41.41 0 0 0-.183-.348.34.34 0 0 0-.366 0l-1.78 1.125c-.602.327-1.33.019-1.566-.649l-1.519-4.3a1.26 1.26 0 0 1 .343-1.36z" />
    <path
      stroke="#FAF8FC"
      strokeWidth={2}
      d="M31.691 17.88a3 3 0 0 1 2.943 2.418l1.209 6.108.03.173a3 3 0 0 1-2.974 3.409H22.66a3 3 0 0 1-2.943-3.582l1.209-6.108.063-.26a3 3 0 0 1 2.88-2.158z"
    />
    <path
      stroke="#C4A4DC"
      strokeWidth={1.5}
      d="m25.062 19.3 1.162-1.93h3.362l1.222 2.047"
    />
  </svg>
);
export default SvgClothing;
