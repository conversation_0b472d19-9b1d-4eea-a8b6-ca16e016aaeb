syntax = "proto3";
package database;
import "database/custom_type.proto";

message TimeSpan {
    repeated string allowed_values = 1; // @gotags: bson:"-"
    string value = 2;
}
  
message BankBalanceCardUserFilters {
    TimeSpan time_span = 1;
}
message EquitiesXIRRCardUserFilters {
    TimeSpan time_span = 1;
}
message MutualFundsXIRRCardUserFilters {
    TimeSpan time_span = 1;
}
message EquitiesReturnsCardUserFilters {
    TimeSpan time_span = 1;
}
message MutualFundsReturnsCardUserFilters {
    TimeSpan time_span = 1;
}

message BankAccountSummaryCardSystemFilters {
    ObjectId account_id = 1;
}
message EquitiesAccountSummaryCardSystemFilters {
    ObjectId account_id = 1;
}
message MutualFundsAccountSummaryCardSystemFilters {
    ObjectId account_id = 1;
}

message WrapOneOfSystemFilter{
    oneof record {
        BankAccountSummaryCardSystemFilters bank_account_summary_card_system_filters = 1;
        EquitiesAccountSummaryCardSystemFilters equities_account_summary_card_system_filters = 2;
        MutualFundsAccountSummaryCardSystemFilters mutual_funds_account_summary_card_system_filters = 3;
    }
}

message WrapOneOfUserFilter {
    oneof record {
        BankBalanceCardUserFilters bank_balance_card_filters = 1;
        EquitiesXIRRCardUserFilters equities_xirr_card_filters = 2;
        MutualFundsXIRRCardUserFilters mutual_funds_xirr_card_filters = 3;
        EquitiesReturnsCardUserFilters equities_returns_card_filters = 4;
        MutualFundsReturnsCardUserFilters mutual_funds_returns_card_filters = 5;
    }
} 

message UserFilters {
    WrapOneOfUserFilter filter = 1;
    UserGroupFilters user_group_filters = 2;
}

message SystemFilters {
    WrapOneOfSystemFilter filter = 1;
}

message UserGroupFilters {
    repeated string user_group_ids = 1;
    repeated string user_ids = 2;
}

enum AccountsOf {
    ACCOUNTS_OF_UNSPECIFIED = 0;
    ACCOUNTS_OF_USER = 1;
    ACCOUNTS_OF_GROUP = 2;
}

message AccountsFilter {
    repeated string account_ids = 1;
    AccountsOf accounts_of = 2;
}

// flag:is-collection=true;
message CardFilters {
    ObjectId _id = 1;
    string phone_number = 2;
    string card_type = 3;
    string card_id = 4;
    int64 priority = 5;
    SystemFilters system_filters = 6;
    UserFilters user_filters = 7;
    int64 created_at = 8;
    int64 updated_at = 9;
}

// flag:is-collection=true;
message UserDefaults {
    ObjectId _id = 1;
    string phone_number = 2;
    UserGroupFilters user_group_filters = 3;
    AccountsFilter account_filters = 4;
    int64 created_at = 5;
    int64 updated_at = 6;
}
