import { <PERSON>a, StoryObj } from "@storybook/react";
import {
  creditCategories,
  debitCategories,
  dummyAccount,
  dummyRecurrentTransactions,
  RecurrentTransactionCard,
  RecurrentTxnContainer,
} from "@repo/ui";

// Note: With keepCase: false in gRPC config, data should already be in camelCase
// If dummy data is still in snake_case, it should be updated to camelCase
const enhancedTxns = dummyRecurrentTransactions.txns.map((txn) => {
  const matchingAccount = dummyAccount.accounts.find(
    (acc) => acc.accountId.trim() === txn.txnCard.accountId
  );

  if (matchingAccount) {
    const newTxnCard = {
      ...txn.txnCard,
      account: matchingAccount,
    };

    return {
      ...txn,
      txnCard: newTxnCard,
    };
  }

  return txn;
});

const enhancedTransactions = {
  summaries: dummyRecurrentTransactions.summaries,
  txns: enhancedTxns as RecurrentTransactionCard[],
};
const categories = [...creditCategories, ...debitCategories];

const meta: Meta<typeof RecurrentTxnContainer> = {
  title: "Recurrent Transaction/Recurrent Transactions Container",
  component: RecurrentTxnContainer,
  tags: ["autodocs"],
  args: {
    transactions: enhancedTransactions,
    categories: categories,
  },
};
export default meta;
type Story = StoryObj<typeof RecurrentTxnContainer>;

export const Default: Story = {};
