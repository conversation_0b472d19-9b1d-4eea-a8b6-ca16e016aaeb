syntax = "proto3";
package rebit.fi_schemas.deposit;
import "rebit/fi_schemas/deposit/deposit.proto";

message FinarkeinDepositAccount {
    repeated Holder profile = 1; // @gotags: json:"profile"
    Summary summary = 2; // @gotags: json:"summary"
    repeated Transaction transactions = 3; // @gotags: json:"transactions"
    TransactionsMeta transactions_meta = 4; // @gotags: json:"transactionsMeta"
}


message TransactionsMeta {
    int64 fromTimestamp = 1; // @gotags: json:"fromTimestamp"
    int64 toTimestamp = 2; // @gotags: json:"toTimestamp"
}
  