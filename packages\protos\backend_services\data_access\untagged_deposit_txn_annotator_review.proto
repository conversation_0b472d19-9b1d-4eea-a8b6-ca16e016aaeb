syntax = "proto3";
package backend_services.data_access;

import "database/custom_type.proto";
import "database/fi_deposit.proto";

service UntaggedDepositTxnAnnotatorReview {
    rpc GetUntaggedGroupedTransactions(GetUntaggedGroupedTransactionsRequest) returns (GetUntaggedGroupedTransactionsResponse) {};
    rpc MarkUntaggedGroupProcessedByAnnotator(MarkUntaggedGroupProcessedByAnnotatorRequest) returns (MarkUntaggedGroupProcessedByAnnotatorResponse) {};
    rpc MarkUntaggedGroupProcessedByReviewer(MarkUntaggedGroupProcessedByReviewerRequest) returns (MarkUntaggedGroupProcessedByReviewerResponse) {};
    rpc UpdateAnnotatorKeywordTagSuggestion(UpdateAnnotatorKeywordTagSuggestionRequest) returns (UpdateAnnotatorKeywordTagSuggestionResponse) {};
}

message GetUntaggedGroupedTransactionsRequest{}
message GetUntaggedGroupedTransactionsResponse{
    repeated database.UntaggedDepositTxnAnnotatorReview groups = 1;
}

message MarkUntaggedGroupProcessedByAnnotatorRequest{
    database.ObjectId id = 1;
}
message MarkUntaggedGroupProcessedByAnnotatorResponse{}

message MarkUntaggedGroupProcessedByReviewerRequest{
    database.ObjectId id = 1;
}
message MarkUntaggedGroupProcessedByReviewerResponse{}

message UpdateAnnotatorKeywordTagSuggestionRequest{
    database.ObjectId id = 1;
    database.UntaggedDepositTxnAnnotatorReview.KeywordToTagMapping suggested_tag_mapping = 2;
}
message UpdateAnnotatorKeywordTagSuggestionResponse{}