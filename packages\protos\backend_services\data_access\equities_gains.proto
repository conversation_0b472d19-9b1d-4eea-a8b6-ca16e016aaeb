syntax = "proto3";
package backend_services.data_access;
import "database/fi_equities.proto";

service EquitiesGains {
    rpc UpdateGainsEquitiesAccount(UpdateGainsEquitiesAccountRequest) returns (UpdateGainsEquitiesAccountResponse) {};
	rpc GetGainsEquitiesAccount(GetGainsEquitiesAccountRequest) returns (GetGainsEquitiesAccountResponse) {};
	rpc DeleteEquitiesGainsAccountsData(DeleteEquitiesGainsAccountsDataRequest) returns (DeleteEquitiesGainsAccountsDataResponse) {};
	rpc GetEquitiesUnrealizedGains(GetEquitiesUnrealizedGainsRequest) returns (GetEquitiesUnrealizedGainsResponse) {};
	rpc SaveEquitiesGains(SaveEquitiesGainsRequest) returns (SaveEquitiesGainsResponse) {};
}


message UpdateGainsEquitiesAccountRequest{
	repeated database.EquitiesGain records = 1;
}
message UpdateGainsEquitiesAccountResponse{}

message GetGainsEquitiesAccountRequest{
	string account_id = 1;
	bool unrealized = 2;
	int64 timestamp_after = 3;
    int64 timestamp_before = 4;
}
message GetGainsEquitiesAccountResponse{
	repeated database.EquitiesGain records = 1;
}

message DeleteEquitiesGainsAccountsDataRequest{
	repeated string account_ids = 1;
}
message DeleteEquitiesGainsAccountsDataResponse{}

message GetEquitiesGainsByIdsRequest {
	repeated string ids = 1;
}

message GetEquitiesGainsByIdsResponse {
	repeated database.EquitiesGain records = 1;
}

message GetEquitiesUnrealizedGainsRequest {
	repeated string account_ids = 1;
}

message GetEquitiesUnrealizedGainsResponse {
	repeated database.EquitiesGain records = 1;
}

message SaveEquitiesGainsRequest {
	string account_id = 1;
	int64 from = 2;
	repeated database.EquitiesGain gains = 3;
}

message SaveEquitiesGainsResponse {
}