syntax = "proto3";
package backend_services.data_access;
import "database/fi_equities.proto";

service EquitiesAccountHoldings {
    rpc UpdateHoldingsEquitiesAccount(UpdateHoldingsEquitiesAccountRequest) returns (UpdateHoldingsEquitiesAccountResponse) {};
	rpc GetMonthlyHoldingsEquitiesAccount(GetMonthlyHoldingsEquitiesAccountRequest) returns (GetMonthlyHoldingsEquitiesAccountResponse) {};
	rpc DeleteEquitiesAccountHoldingsAccountsData(DeleteEquitiesAccountHoldingsAccountsDataRequest) returns (DeleteEquitiesAccountHoldingsAccountsDataResponse) {};
}


message UpdateHoldingsEquitiesAccountRequest{
	repeated database.EquitiesAccountHoldings records = 1;
}
message UpdateHoldingsEquitiesAccountResponse{}


message GetMonthlyHoldingsEquitiesAccountRequest{
	string account_id = 1;
	int64 summary_date = 2;
}
message GetMonthlyHoldingsEquitiesAccountResponse{
	database.EquitiesAccountHoldings record = 1;
}

message DeleteEquitiesAccountHoldingsAccountsDataRequest{
	repeated string account_ids = 1;
}
message DeleteEquitiesAccountHoldingsAccountsDataResponse{}