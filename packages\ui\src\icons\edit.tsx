import * as React from "react";
import type { SVGProps } from "react";
const SvgEdit = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="none"
    viewBox="0 0 24 24"
    {...props}
  >
    <path
      stroke="#905BB5"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={1.127}
      d="M10.764 5.594H5.503A1.503 1.503 0 0 0 4 7.097v10.52a1.503 1.503 0 0 0 1.503 1.504h10.521a1.503 1.503 0 0 0 1.503-1.503v-5.26"
    />
    <path
      stroke="#905BB5"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={1.127}
      d="M16.399 4.467a1.594 1.594 0 0 1 2.254 2.254l-7.14 7.14-3.005.751.751-3.006z"
    />
  </svg>
);
export default SvgEdit;
