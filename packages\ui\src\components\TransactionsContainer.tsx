"use client";

import { useState, useEffect } from "react";
import {
  TransactionsTable,
  TransactionGroup,
  TransactionCardWithDate,
  TransactionActionTypes,
  TransactionActionFunction,
  FetchDocuments,
  TransactionDocument,
  TransactionTypeEnum,
  Category,
  TransactionCategory,
} from "./TransactionsTable";
import { NoTransactionsFound } from "./NoTransactionsFound";
import {
  AppliedFilters,
  FilterContainer,
  defaultBookmarkOptions,
} from "./FilterContainer";
import { creditCategories, debitCategories } from "../data/dummyData";

import {
  SidebarProvider,
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarInset,
} from "./ui/sidebar";
import { TransactionDetails } from "./TransactionDetails";
import { Button } from "./ui/button";
import { AddEditTag } from "./AddEditTag";
import { Group, Member } from "./MemberGroupFilter";
import { useToast } from "../hooks/use-toast";
import { ScrollArea } from "./ui/scroll-area";

const categories = [...creditCategories, ...debitCategories];
const SidebarContentType = {
  NONE: "none",
  FILTER: "filter",
  TRANSACTION_DETAILS: "transaction_details",
  TAG: "tag",
};

const defaultFilters: AppliedFilters = {
  selectedRange: undefined,
  selectedAmountRange: [0, 200000],
  selectedDateLabel: "All time",
  selectedMembers: [],
  selectedGroups: [],
  selectedAccounts: { accountIds: new Set() },
  selectedBookmarkOptions: defaultBookmarkOptions,
  selectedTags: [],
  selectedTransactionType: "All",
  selectedTagStatus: "All",
  selectedCategories: {},
  categoryFilters: [],
};

export type TransactionsContainerProps = {
  transactions: TransactionGroup[];
  changeCategoryAction: (
    transactionId: string,
    categoryId: string,
    subCategoryId: string,
  ) => Promise<void>;
  updateTransaction: TransactionActionFunction;
  fetchTransactionDocuments?: (
    transactionId: string,
  ) => Promise<FetchDocuments>;
  deleteTransactionDocuments: (
    transactionId: string,
    fileNames: string[],
  ) => Promise<{ ok: boolean }>;
  uploadFile?: (transactionId: string, file: File) => void;
  defaultFilterState?: boolean;
  onFilterStateChange?: (isOpen: boolean) => void;
  onFilterBadgesChange?: (badges: string[]) => void;
  initialFilters?: AppliedFilters;
  onClearFilterFunc?: (
    clearFilterFn: (value: string | undefined) => void,
  ) => void;
  onApplyAllFilters?: (filters: AppliedFilters) => void;
  onResetUrlFilters?: () => void;
  initialBadges?: string[];
  lastItemRef: (node: HTMLTableRowElement) => void;
  hasMore: boolean;
  onCategoriesData?: (categories: Category[]) => void;
  members?: Member[];
  groups?: Group[];
};

function TransactionsContainer({
  transactions,
  changeCategoryAction,
  updateTransaction,
  fetchTransactionDocuments,
  deleteTransactionDocuments,
  uploadFile,
  defaultFilterState = false,
  initialFilters,
  onFilterStateChange,
  onFilterBadgesChange,
  onClearFilterFunc,
  onApplyAllFilters,
  onResetUrlFilters,
  lastItemRef,
  hasMore,
  onCategoriesData,
  members = [],
  groups = [],
}: TransactionsContainerProps) {
  const { toast } = useToast();
  const [sidebarContentType, setSidebarContentType] = useState(
    defaultFilterState ? SidebarContentType.FILTER : SidebarContentType.NONE,
  );
  const [openTransaction, setOpenTransaction] = useState<
    TransactionCardWithDate | undefined
  >();
  const [openTransactionId, setOpenTransactionId] = useState<
    string | undefined
  >();
  const [currentTransactionDocs, setCurrentTransactionDocs] = useState<
    TransactionDocument[]
  >([]);
  const [filterBadges, setFilterBadges] = useState<string[]>([]);

  const [filters, setFilters] = useState<AppliedFilters>(
    initialFilters || defaultFilters,
  );

  const [selectedCategory, setSelectedCategory] = useState<
    Category | undefined
  >();

  useEffect(() => {
    if (defaultFilterState) {
      setSidebarContentType(SidebarContentType.FILTER);
    } else if (sidebarContentType === SidebarContentType.FILTER) {
      setSidebarContentType(SidebarContentType.NONE);
    }
  }, [defaultFilterState, sidebarContentType]);

  useEffect(() => {
    if (onClearFilterFunc) {
      onClearFilterFunc(handleClearFilters);
    }
  }, [onClearFilterFunc]);

  useEffect(() => {
    if (onCategoriesData) {
      onCategoriesData(categories);
    }
  }, [onCategoriesData, categories]);

  useEffect(() => {
    if (onFilterBadgesChange) {
      onFilterBadgesChange(filterBadges);
    }
  }, [filterBadges, onFilterBadgesChange, filters]);

  const isSidebarOpen = sidebarContentType !== SidebarContentType.NONE;

  const handleSidebarStateChange = (newContentType: string) => {
    setSidebarContentType(newContentType);

    if (
      onFilterStateChange &&
      (newContentType === SidebarContentType.FILTER ||
        (sidebarContentType === SidebarContentType.FILTER &&
          newContentType !== SidebarContentType.FILTER))
    ) {
      onFilterStateChange(newContentType === SidebarContentType.FILTER);
    }
  };

  const handleCloseSidebar = () => {
    handleSidebarStateChange(SidebarContentType.NONE);

    if (sidebarContentType === SidebarContentType.TRANSACTION_DETAILS) {
      setOpenTransactionId(undefined);
    }
  };

  const handleApplyFilters = (newFilters: AppliedFilters) => {
    const categoryFilters: TransactionCategory[] = [];
    Object.entries(newFilters.selectedCategories).forEach(
      ([categoryId, subcategoryIds]) => {
        const category = categories.find((c) => c.id === categoryId);
        if (!category) {
          return;
        }

        if (subcategoryIds.length === 0) {
          categoryFilters.push({
            categoryCollection: "global",
            categoryId: categoryId,
            subcategoryId: "",
          });
        } else {
          subcategoryIds.forEach((subcategoryId) => {
            const subcategory = category.subCategories.find(
              (s) => s.id === subcategoryId,
            );
            if (!subcategory) {
              return;
            }

            categoryFilters.push({
              categoryCollection: "global",
              categoryId: categoryId,
              subcategoryId: subcategoryId,
            });
          });
        }
      },
    );

    newFilters.categoryFilters = categoryFilters;

    setFilters(newFilters);
    updateFilterBadges(newFilters);
    if (onApplyAllFilters) {
      onApplyAllFilters(newFilters);
    }

    handleCloseSidebar();
  };

  const updateFilterBadges = (currentFilters: AppliedFilters) => {
    const badges: string[] = [];
    if (
      currentFilters.selectedDateLabel &&
      currentFilters.selectedDateLabel !== "All time"
    ) {
      badges.push(currentFilters.selectedDateLabel);
    }
    if (
      currentFilters.selectedAmountRange[0]! > 0 ||
      currentFilters.selectedAmountRange[1]! < 200000
    ) {
      badges.push(
        `₹${currentFilters.selectedAmountRange[0]} - ₹${currentFilters.selectedAmountRange[1]}`,
      );
    }
    if (currentFilters.selectedMembers.length > 0 && members.length > 0) {
      badges.push(
        ...currentFilters.selectedMembers.map((id) => {
          const member = members.find((m) => m.id === id);
          return member ? member.name : id;
        }),
      );
    } else if (currentFilters.selectedMembers.length > 0) {
      badges.push(...currentFilters.selectedMembers);
    }

    if (currentFilters.selectedMembers.length === 0) {
      if (currentFilters.selectedGroups.length > 0 && groups.length > 0) {
        badges.push(
          ...currentFilters.selectedGroups.map((id) => {
            const group = groups.find((g) => g.id === id);
            return group ? group.groupName : id;
          }),
        );
      } else if (currentFilters.selectedGroups.length > 0) {
        badges.push(...currentFilters.selectedGroups);
      }
    }

    if (Object.keys(currentFilters.selectedAccounts).length > 0) {
      const accountMemberMap = new Map<string, string>();
      const accountGroupMap = new Map<string, string>();

      if (members.length > 0) {
        members.forEach((member) => {
          if (member.accounts) {
            member.accounts.forEach((account) => {
              const accountId = account.accountId;
              if (accountId) {
                accountMemberMap.set(accountId, member.name);
              }
            });
          }
        });
      }

      if (groups.length > 0) {
        groups.forEach((group) => {
          if (group.members) {
            group.members.forEach((member) => {
              if (member.accounts) {
                member.accounts.forEach((account) => {
                  const accountId = account.accountId;
                  if (accountId) {
                    accountGroupMap.set(accountId, group.groupName);
                  }
                });
              }
            });
          }
        });
      }

      Object.keys(currentFilters.selectedAccounts).forEach((accountId) => {
        const memberName = accountMemberMap.get(accountId);
        if (memberName && !badges.includes(memberName)) {
          badges.push(memberName);
        }

        if (currentFilters.selectedMembers.length === 0) {
          const groupName = accountGroupMap.get(accountId);
          if (groupName && !badges.includes(groupName)) {
            badges.push(groupName);
          }
        }
      });
    }

    if (
      currentFilters.selectedTransactionType &&
      currentFilters.selectedTransactionType !== "All"
    ) {
      badges.push(currentFilters.selectedTransactionType);
    }

    if (
      currentFilters.selectedTagStatus &&
      currentFilters.selectedTagStatus !== "All"
    ) {
      badges.push(currentFilters.selectedTagStatus);
    }

    if (currentFilters.selectedBookmarkOptions.showFavorites) {
      badges.push("Favorites");
    }
    if (currentFilters.selectedBookmarkOptions.excludedFromCashflow) {
      badges.push("Excluded from cashflow");
    }
    if (currentFilters.selectedBookmarkOptions.withNotes) {
      badges.push("With notes");
    }

    const subcategoryMap = new Map<
      string,
      { name: string; categoryName: string }
    >();
    categories.forEach((category) => {
      category.subCategories.forEach((subcategory) => {
        subcategoryMap.set(subcategory.id, {
          name: subcategory.name,
          categoryName: category.name,
        });
      });
    });

    Object.entries(currentFilters.selectedCategories).forEach(
      ([categoryId, subcategoryIds]) => {
        if (subcategoryIds.length > 0) {
          const category = categories.find((c) => c.id === categoryId);
          if (!category) {
            return;
          }
          const categoryName = category.name;
          let categoryAdded = false;
          const generalSubcategories: string[] = [];
          const subcategories: string[] = [];

          subcategoryIds.forEach((subId) => {
            let subCategory = category.subCategories.find(
              (s) => s.id === subId,
            );

            if (!subCategory) {
              const subcategoryInfo = subcategoryMap.get(subId);
              if (subcategoryInfo) {
                subcategories.push(subcategoryInfo.name);
              }
              return;
            }
            if (
              subCategory.name.toLowerCase() === "others" ||
              subCategory.name.toLowerCase() === "general"
            ) {
              generalSubcategories.push(subCategory.name);
            } else {
              subcategories.push(subCategory.name);
            }
          });
          if (
            generalSubcategories.length > 0 &&
            !badges.includes(categoryName)
          ) {
            badges.push(categoryName);
            categoryAdded = true;
          }
          badges.push(...subcategories);
        }
      },
    );

    setFilterBadges(badges);
  };

  const handleClearFilters = (value?: string) => {
    if (!value) return;
    if (value === "ALL") {
      setFilters(defaultFilters);
      setFilterBadges([]);
      if (onResetUrlFilters) {
        onResetUrlFilters();
      }

      if (onApplyAllFilters) {
        onApplyAllFilters(defaultFilters);
      }

      if (onFilterBadgesChange) {
        onFilterBadgesChange([]);
      }

      return;
    }
    const isDateFilter =
      value === filters.selectedDateLabel ||
      (value.includes("-") &&
        (value.includes("Start") ||
          /[A-Za-z]{3}\s\d{1,2},\s\d{4}/.test(value)));

    if (isDateFilter) {
      let updatedFilters;
      setFilters((prev) => {
        const updated = {
          ...prev,
          selectedRange: undefined,
          selectedDateLabel: "All time",
        };
        updatedFilters = updated;
        return updated;
      });
      setFilterBadges((prev) => prev.filter((badge) => badge !== value));
      if (onApplyAllFilters && updatedFilters) {
        onApplyAllFilters(updatedFilters);
      }
      return;
    }

    if (value.includes("₹")) {
      let updatedFilters;
      setFilters((prev) => {
        const updated = {
          ...prev,
          selectedAmountRange: [0, 200000],
        };
        updatedFilters = updated;
        return updated;
      });
      setFilterBadges((prev) => prev.filter((badge) => badge !== value));
      if (onApplyAllFilters && updatedFilters) {
        onApplyAllFilters(updatedFilters);
      }
      return;
    }

    if (["incoming", "outgoing"].includes(value.toLowerCase())) {
      let updatedFilters;
      setFilters((prev) => {
        const updated = {
          ...prev,
          selectedTransactionType: "All",
        };
        updatedFilters = updated;
        return updated;
      });
      setFilterBadges((prev) => prev.filter((badge) => badge !== value));
      if (onApplyAllFilters && updatedFilters) {
        onApplyAllFilters(updatedFilters);
      }
      return;
    }

    if (["tagged", "untagged"].includes(value.toLowerCase())) {
      let updatedFilters;
      setFilters((prev) => {
        const updated = {
          ...prev,
          selectedTagStatus: "All",
        };
        updatedFilters = updated;
        return updated;
      });
      setFilterBadges((prev) => prev.filter((badge) => badge !== value));
      if (onApplyAllFilters && updatedFilters) {
        onApplyAllFilters(updatedFilters);
      }
      return;
    }

    if (value === "Favorites") {
      let updatedFilters;
      setFilters((prev) => {
        const updated = {
          ...prev,
          selectedBookmarkOptions: {
            ...prev.selectedBookmarkOptions,
            showFavorites: false,
          },
        };
        updatedFilters = updated;
        return updated;
      });
      setFilterBadges((prev) => prev.filter((badge) => badge !== value));
      if (onApplyAllFilters && updatedFilters) {
        onApplyAllFilters(updatedFilters);
      }
      return;
    }

    if (value === "Excluded from cashflow") {
      let updatedFilters;
      setFilters((prev) => {
        const updated = {
          ...prev,
          selectedBookmarkOptions: {
            ...prev.selectedBookmarkOptions,
            excludedFromCashflow: false,
          },
        };
        updatedFilters = updated;
        return updated;
      });
      setFilterBadges((prev) => prev.filter((badge) => badge !== value));
      if (onApplyAllFilters && updatedFilters) {
        onApplyAllFilters(updatedFilters);
      }
      return;
    }

    if (value === "With notes") {
      let updatedFilters;
      setFilters((prev) => {
        const updated = {
          ...prev,
          selectedBookmarkOptions: {
            ...prev.selectedBookmarkOptions,
            withNotes: false,
          },
        };
        updatedFilters = updated;
        return updated;
      });
      setFilterBadges((prev) => prev.filter((badge) => badge !== value));
      if (onApplyAllFilters && updatedFilters) {
        onApplyAllFilters(updatedFilters);
      }
      return;
    }
    let memberMatch;
    if (members.length > 0) {
      memberMatch = members.find((m) => m.name === value);
    }

    if (memberMatch) {
      const memberAccountIds = memberMatch.accounts
        ? memberMatch.accounts.map(
          (account) => account.accountId
        )
        : [];

      let updatedFilters;
      setFilters((prev) => {
        const updatedMembers = prev.selectedMembers.filter(
          (id) => id !== memberMatch.id,
        );
        const updatedAccountIds = new Set(prev.selectedAccounts.accountIds);
        memberAccountIds.forEach((accountId) => {
          if (accountId) updatedAccountIds.delete(accountId);
        });
        const updatedAccounts = { accountIds: updatedAccountIds };
        const updated = {
          ...prev,
          selectedMembers: updatedMembers,
          selectedAccounts: updatedAccounts,
        };
        updatedFilters = updated;
        return updated;
      });
      setFilterBadges((prev) => prev.filter((badge) => badge !== value));
      if (onApplyAllFilters && updatedFilters) {
        onApplyAllFilters(updatedFilters);
      }
      return;
    }

    let groupMatch;
    if (groups.length > 0) {
      groupMatch = groups.find((g) => g.groupName === value);
    }

    if (groupMatch) {
      const groupAccountIds: string[] = [];
      if (groupMatch.members) {
        groupMatch.members.forEach((member) => {
          if (member.accounts) {
            member.accounts.forEach((account) => {
              const accountId = account.accountId;
              if (accountId) groupAccountIds.push(accountId);
            });
          }
        });
      }

      let updatedFilters;
      setFilters((prev) => {
        const updatedGroups = prev.selectedGroups.filter(
          (id) => id !== groupMatch.id,
        );
        const updatedAccountIds = new Set(prev.selectedAccounts.accountIds);
        groupAccountIds.forEach((accountId) => {
          updatedAccountIds.delete(accountId);
        });
        const updatedAccounts = { accountIds: updatedAccountIds };
        const updated = {
          ...prev,
          selectedGroups: updatedGroups,
          selectedAccounts: updatedAccounts,
        };
        updatedFilters = updated;
        return updated;
      });
      setFilterBadges((prev) => prev.filter((badge) => badge !== value));
      if (onApplyAllFilters && updatedFilters) {
        onApplyAllFilters(updatedFilters);
      }
      return;
    }

    for (const category of categories) {
      if (category.name === value) {
        let updatedFilters;
        setFilters((prev) => {
          const updated = { ...prev };
          delete updated.selectedCategories[category.id];
          updated.categoryFilters = updated?.categoryFilters?.filter(
            (filter) => filter.categoryId !== category.id
          );

          updatedFilters = updated;
          return updated;
        });
        setFilterBadges((prev) => prev.filter((badge) => badge !== value));
        if (onApplyAllFilters && updatedFilters) {
          onApplyAllFilters(updatedFilters);
        }
        return;
      }
      const categoryMatch = categories.find(
        (category) => category.name === value,
      );
      if (categoryMatch) {
        let updatedFilters;
        setFilters((prev) => {
          const updated = { ...prev };
          delete updated.selectedCategories[categoryMatch.id];
          updated.categoryFilters = updated?.categoryFilters?.filter(
            (filter) => filter.categoryId !== categoryMatch.id
          );
          updatedFilters = updated;
          return updated;
        });
        setFilterBadges((prev) => prev.filter((badge) => badge !== value));
        if (onApplyAllFilters && updatedFilters) {
          onApplyAllFilters(updatedFilters);
        }
        return;
      }
      const subcategoryMatch = categories
        .flatMap((category) =>
          category.subCategories.map((subcategory) => ({
            category,
            subcategory,
          })),
        )
        .find((item) => item.subcategory.name === value);

      if (subcategoryMatch) {
        let updatedFilters;
        setFilters((prev) => {
          const updated = { ...prev };
          if (updated.selectedCategories[subcategoryMatch.category.id]) {
            updated.selectedCategories[subcategoryMatch.category.id] =
              updated.selectedCategories[subcategoryMatch.category.id]!.filter(
                (id) => id !== subcategoryMatch.subcategory.id,
              );
            updated.categoryFilters = updated?.categoryFilters?.filter(
              (filter) =>
                !(filter.categoryId === subcategoryMatch.category.id &&
                  filter.subcategoryId === subcategoryMatch.subcategory.id)
            );

            if (
              updated.selectedCategories[subcategoryMatch.category.id]!
                .length === 0
            ) {
              delete updated.selectedCategories[subcategoryMatch.category.id];
            }
          }
          updatedFilters = updated;
          return updated;
        });
        setFilterBadges((prev) => prev.filter((badge) => badge !== value));
        if (onApplyAllFilters && updatedFilters) {
          onApplyAllFilters(updatedFilters);
        }
        return;
      }
    }

    setFilterBadges((prev) => prev.filter((badge) => badge !== value));
  };

  async function handleTransactionClick(transaction: TransactionCardWithDate) {
    if (
      sidebarContentType === SidebarContentType.TRANSACTION_DETAILS &&
      openTransaction?.txnId === transaction.txnId
    ) {
      handleCloseSidebar();
      return;
    }

    setCurrentTransactionDocs([]);

    if (sidebarContentType === SidebarContentType.FILTER) {
      handleSidebarStateChange(SidebarContentType.NONE);
    }

    if (transaction.documentsCount > 0 && fetchTransactionDocuments) {
      const txnDocs = await fetchTransactionDocuments(transaction.txnId);
      setCurrentTransactionDocs(txnDocs.documents);
    }

    setOpenTransaction(transaction);
    setOpenTransactionId(transaction.txnId);
    handleSidebarStateChange(SidebarContentType.TRANSACTION_DETAILS);
  }

  function handleAddEditTagClick(transaction: TransactionCardWithDate) {
    setOpenTransaction(transaction);
    setOpenTransactionId(transaction.txnId);
    setSelectedCategory(
      categories.find((category) => category.id == (transaction?.tag?.categoryId))
    );
    handleSidebarStateChange(SidebarContentType.TAG);
  }

  const handleSavedChanged = (txnId: string, flag: boolean) => {
    updateTransaction(TransactionActionTypes.SAVE_TRANSACTION, {
      transactionId: txnId,
      isSaved: flag,
    });
    toast({
      description: flag ? "Transaction saved!" : "Transaction unsaved!",
    });
  };

  const handleExcludedCashflowChanged = (txnId: string, flag: boolean) => {
    updateTransaction(TransactionActionTypes.EXCLUDE_CASH_FLOW, {
      transactionId: txnId,
      isExcludedFromCashFlow: flag,
    });
    toast({
      description: flag
        ? "Excluded from cash flow applied!"
        : "Excluded from cash flow removed!",
    });
  };

  const handleNotesChanged = (txnId: string, notes: string) => {
    updateTransaction(TransactionActionTypes.NOTES, {
      transactionId: txnId,
      notes: notes,
    });
    toast({ description: "Notes updated" });
  };

  const handleUploadFile = (txnId: string, file: File) => {
    if (uploadFile) {
      uploadFile(txnId, file);
      toast({ description: "File uploaded" });
    }
  };

  const handleDeleteFile = async (txnId: string, fileName: string) => {
    const response = await deleteTransactionDocuments(txnId, [fileName]);
    if (response.ok) {
      setCurrentTransactionDocs((prev) =>
        prev.filter((item) => (item.objectName) !== fileName)
      );
      toast({ description: "File deleted" });
    }
  };

  let transactionsView;

  if (transactions.length <= 0) {
    transactionsView = (
      <div className="flex items-center justify-center h-full">
        <NoTransactionsFound />
      </div>
    );
  } else {
    transactionsView = (
      <TransactionsTable
        categories={categories}
        transactionGroups={transactions}
        openTransactionId={openTransactionId}
        onTransactionClick={handleTransactionClick}
        onChangeReceiver={() => {}}
        onAddEditTagClick={handleAddEditTagClick}
        lastItemRef={lastItemRef}
        hasMore={hasMore}
      />
    );
  }

  const getSidebarTitle = () => {
    switch (sidebarContentType) {
      case SidebarContentType.FILTER:
        return "Filters";
      case SidebarContentType.TRANSACTION_DETAILS:
        return "Transaction Details";
      case SidebarContentType.TAG:
        return openTransaction?.tag?.categoryId ? "Edit tag" : "Add tag";
      default:
        return "";
    }
  };

  const renderSidebarContent = () => {
    if (sidebarContentType === SidebarContentType.FILTER) {
      return (
        <FilterContainer
          filters={filters}
          onApply={handleApplyFilters}
          onClear={handleClearFilters}
          onClose={handleCloseSidebar}
          members={members || []}
          groups={groups || []}
        />
      );
    }
    if (
      sidebarContentType === SidebarContentType.TRANSACTION_DETAILS &&
      openTransaction
    ) {
      return (
        <TransactionDetails
          transaction={openTransaction}
          date={openTransaction.date}
          currentTransactionDocs={currentTransactionDocs}
          categories={categories}
          onSavedChanged={handleSavedChanged}
          onExcludedCashflowChanged={handleExcludedCashflowChanged}
          onNotesChanged={handleNotesChanged}
          onUploadFile={handleUploadFile}
          onDeleteFile={handleDeleteFile}
          onAddEditTagClick={handleAddEditTagClick}
        />
      );
    }
    if (sidebarContentType === SidebarContentType.TAG && openTransaction) {
      return (
        <AddEditTag
          selectedCategory={selectedCategory}
          categories={
            openTransaction.txnType === TransactionTypeEnum.CREDIT
              ? creditCategories
              : debitCategories
          }
          onSelectCategory={(category) => {
            if (category && !Array.isArray(category)) {
              const selectedSubCategory = category.subcategory_id[0] || "";
              changeCategoryAction(
                openTransaction.txnId,
                category.category_id,
                selectedSubCategory,
              );
              handleCloseSidebar();
            }
          }}
          transaction={{
            entity: openTransaction.merchantName,
            amount: openTransaction.amount,
            date: openTransaction.date,
            narration: openTransaction.narration,
            tag: openTransaction.tag,
          }}
        />
      );
    }
    return null;
  };

  return (
    <SidebarProvider
      open={isSidebarOpen}
      className="p-3 gap-3 w-full has-[[data-variant=inset]]:bg-transparent overflow-x-hidden h-full"
      style={{
        ["--sidebar-width" as any]: "500px",
      }}
    >
      <SidebarInset className="rounded-xl overflow-hidden">
        {transactionsView}
      </SidebarInset>
      <Sidebar
        side="right"
        variant="inset"
        className="overflow-hidden p-0 rounded-xl bg-white"
      >
        <SidebarHeader
          className={`p-3 ${sidebarContentType === SidebarContentType.FILTER ? "border-b" : ""}`}
        >
          <div className="flex justify-between items-center">
            <h2 className={"text-xl font-bold"}>{getSidebarTitle()}</h2>
            <Button size="sm" variant="outline" onClick={handleCloseSidebar}>
              Close
            </Button>
          </div>
        </SidebarHeader>

        <SidebarContent
          className={
            sidebarContentType === SidebarContentType.TRANSACTION_DETAILS
              ? "px-3 pb-3"
              : ""
          }
        >
          <ScrollArea>{renderSidebarContent()}</ScrollArea>
        </SidebarContent>
      </Sidebar>
    </SidebarProvider>
  );
}

export { TransactionsContainer };
