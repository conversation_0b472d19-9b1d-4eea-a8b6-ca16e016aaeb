import * as grpc from "@grpc/grpc-js";
import * as protoLoader from "@grpc/proto-loader";
import path from "path";
import { GRPC_CONFIG } from "../constants";

export interface GrpcClientConfig {
  protoPath: string;
  servicePath: string;
  serviceConstructor: string;
}

const clientCache = new Map<string, grpc.Client>();

export function createGrpcClient<T extends grpc.Client>(config: GrpcClientConfig): T {
  const cacheKey = `${config.protoPath}:${config.servicePath}:${config.serviceConstructor}`;
  
  if (clientCache.has(cacheKey)) {
    return clientCache.get(cacheKey) as T;
  }

  const protoFilePath = path.resolve(GRPC_CONFIG.PROTO_DIR_PATH, config.protoPath);
  const options = {
    ...GRPC_CONFIG.OPTIONS,
    includeDirs: [path.resolve(GRPC_CONFIG.PROTO_DIR_PATH)],
  };

  const packageDefinition = protoLoader.loadSync(protoFilePath, options);
  // Using Record<string, unknown> instead of any
  const proto = grpc.loadPackageDefinition(packageDefinition) as Record<string, unknown>;

  // Navigate to the correct service using the servicePath (e.g., "backend_services.user")
  const service = config.servicePath.split('.').reduce((obj, path) => obj[path] as Record<string, unknown>, proto);
  
  const client = new (service[config.serviceConstructor] as typeof grpc.Client)(
    GRPC_CONFIG.GRPC_SERVER_URI,
    grpc.credentials.createSsl()
  ) as T;

  clientCache.set(cacheKey, client);
  return client;
}

export const requestMetadata = new grpc.Metadata();