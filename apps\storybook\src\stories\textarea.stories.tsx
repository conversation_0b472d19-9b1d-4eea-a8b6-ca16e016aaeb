import { Textarea } from "@repo/ui";
import { Meta, StoryObj } from "@storybook/react";

const meta: Meta<typeof Textarea> = {
  title: "Components/Textarea",
  component: Textarea,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Textarea>;

export const Empty: Story = {
  args: {
    value: "",
    placeholder: "Type text here...",
  },
};

export const Filled: Story = {
  args: {
    value: "Lorem ipsum dolor sit amet consectetur adipisicing elit.",
  },
};
