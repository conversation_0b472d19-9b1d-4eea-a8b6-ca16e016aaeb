# Cuspweb

Welcome to the **Cuspweb** project! We've used `pnpm` as the package manager and `Storybook` for component-driven development.

## 🚧 Prerequisites

Ensure you have the following tools installed before proceeding:

- [Node.js](https://nodejs.org/) (>= 16.13.0)
- We are using [Corepack](https://nodejs.org/api/corepack.html) to manage the package manager we are using in this project, which is [pnpm](https://pnpm.io/). So, before running any `pnpm` commands in this project, please setup Corepack as follows:

  ```bash
  corepack enable && corepack enable npm
  ```

  To learn more about how Corepack works, please check out this [article](https://www.totaltypescript.com/how-to-use-corepack).

## 🚀 Getting Started

### 1. Clone the Repository

```bash
git clone https://gitlab.com/InfoCusp/cuspweb.git
cd cuspweb
```

### 2. Install Dependencies

Use pnpm to install the required dependencies:

```bash
pnpm install
```

### 3. Start Development Server

To run Web app and Storybook for component development:

```bash
pnpm dev
```

Web app will start at http://localhost:3000.
Storybook will start at http://localhost:6006.

#### 3.1 Start Web app only

```bash
pnpm dev --filter web
```

#### 3.1 Start Storybook only

```bash
pnpm dev --filter storybook
```

## 📁 Project Structure

```
├── apps/
│   ├── storybook/          # Storybook configuration and stories
│   └── web/               # Next.js web application
├── packages/
│   ├── config-eslint/     # Shared ESLint configurations
│   ├── config-tailwind/   # Shared Tailwind CSS configurations
│   ├── config-typescript/ # Shared TypeScript configurations
│   ├── e2e-tests/        # End-to-end tests using Playwright
│   ├── protos/           # Protocol buffer definitions
│   │   ├── backend_services/
│   │   │   ├── data_access/
│   │   │   ├── external/
│   │   │   ├── user/
│   │   │   └── visualization/
│   │   ├── common/
│   │   │   └── common.proto
│   │   ├── database/
│   │   │   ├── cards_filters.proto
│   │   │   ├── core.proto
│   │   │   ├── custom_type.proto
│   │   │   ├── fi_deposit.proto
│   │   │   ├── fi_equities.proto
│   │   │   ├── fi_mutual_funds.proto
│   │   │   ├── public_data.proto
│   │   │   ├── settings.proto
│   │   │   └── transaction_category.proto
│   │   ├── rebit/
│   │   │   └── core.proto
│   │   └── workflows/
│   └── ui/               # Shared UI components
│       ├── src/
│       │   ├── components/      # React components
│       │   │   ├── ui/         # Base UI components
│       │   │   ├── AddEditTag.tsx
│       │   │   ├── Autocomplete.tsx
│       │   │   ├── DateFilter.tsx
│       │   │   ├── FilterContainer.tsx
│       │   │   ├── HomePage.tsx
│       │   │   ├── NavigationRail.tsx
│       │   │   ├── TransactionDetails.tsx
│       │   │   └── TransactionsTable.tsx
│       │   ├── data/          # Data utilities
│       │   ├── hooks/         # Custom React hooks
│       │   ├── icons/         # SVG icons and assets
│       │   ├── lib/           # Utility functions
│       │   └── styles.css     # Global styles
│       ├── assets/           # Static assets
│       └── test/            # Component tests
|       |_ package.json
├── package.json
├── pnpm-workspace.yaml
└── turbo.json
```

### Key Directories

- `apps/`: Contains all the application code
  - `web/`: Main Next.js web application
  - `storybook/`: Storybook configuration for UI development
- `packages/`: Contains shared packages and configurations
  - `ui/`: Reusable UI components
  - `protos/`: Protocol buffer definitions and services
  - `config-*/`: Shared configurations for TypeScript, ESLint, and Tailwind
  - `e2e-tests/`: End-to-end tests for the applications
