import * as React from "react";
import type { SVGProps } from "react";
const SvgMisc = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="currentColor"
    viewBox="0 0 31 30"
    {...props}
  >
    <path d="M7.447 2.446a1.22 1.22 0 0 1 2.114 0l4.497 7.79A1.22 1.22 0 0 1 13 12.065H4.007a1.22 1.22 0 0 1-1.057-1.83z" />
    <circle cx={22.571} cy={6.627} r={5.288} />
    <rect width={10.512} height={10.512} x={3.188} y={16.839} rx={1.22} />
    <path d="M21.877 16.102c.219-.675 1.173-.675 1.392 0l.838 2.576c.098.302.379.506.696.506h2.71c.709 0 1.004.908.43 1.325l-2.192 1.593a.73.73 0 0 0-.266.818l.837 2.577c.22.675-.553 1.236-1.127.819l-2.192-1.593a.73.73 0 0 0-.86 0l-2.192 1.593c-.574.417-1.346-.144-1.127-.82l.837-2.576a.73.73 0 0 0-.266-.819l-2.192-1.592c-.574-.417-.279-1.325.43-1.325h2.71a.73.73 0 0 0 .696-.506z" />
  </svg>
);
export default SvgMisc;
