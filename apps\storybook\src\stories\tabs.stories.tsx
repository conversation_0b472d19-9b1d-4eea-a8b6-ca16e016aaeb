import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger, TabsContent } from "@repo/ui"; // Adjust the import path based on your file structure

const meta: Meta = {
  title: "Components/Tabs",
  component: Tabs,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Tabs>;

export const Default: Story = {
  render: () => (
    <Tabs defaultValue="tab1">
      <TabsList>
        <TabsTrigger value="tab1">Tab 1</TabsTrigger>
        <TabsTrigger value="tab2">Tab 2</TabsTrigger>
        <TabsTrigger value="tab3">Tab 3</TabsTrigger>
      </TabsList>
      <TabsContent value="tab1">
        <p>This is the content for Tab 1.</p>
      </TabsContent>
      <TabsContent value="tab2">
        <p>This is the content for Tab 2.</p>
      </TabsContent>
      <TabsContent value="tab3">
        <p>This is the content for Tab 3.</p>
      </TabsContent>
    </Tabs>
  ),
};
