{"name": "@repo/e2e-tests", "version": "0.0.0", "private": true, "scripts": {"test:e2e": "playwright test", "test:ui": "playwright test --ui", "test:debug": "playwright test --debug", "test:report": "playwright show-report", "test:pages": "playwright test --project=pages-chromium", "test:components": "playwright test --project=components-chromium", "test:visual": "playwright test --project=visual", "test:accessibility": "playwright test --project=accessibility", "test:api": "playwright test --project=api", "test:ci": "playwright test --reporter=html,github", "install:browsers": "playwright install chromium"}, "devDependencies": {"@axe-core/playwright": "^4.8.5", "@playwright/test": "^1.42.1", "@repo/typescript-config": "workspace:*", "@types/node": "^20.17.47", "dotenv": "^16.4.5", "pixelmatch": "^5.3.0", "pngjs": "^7.0.0"}, "dependencies": {"web": "workspace:*"}}