syntax = "proto3";
package backend_services.data_access;
import "database/fi_equities.proto";

service FiEquitiesTransaction {
    rpc UpsertEquitiesAccountsTransactions(UpsertEquitiesAccountsTransactionsRequest) returns (UpsertEquitiesAccountsTransactionsResponse) {};
	rpc GetEquityTransactions(GetEquityTransactionsRequest) returns (GetEquityTransactionsResponse) {};
    rpc GetEquityTransactionsInAccounts(GetEquityTransactionsInAccountsRequest) returns (GetEquityTransactionsInAccountsResponse) {}
    rpc DeleteFiEquitiesAccountsTransactions(DeleteFiEquitiesAccountsTransactionsRequest) returns (DeleteFiEquitiesAccountsTransactionsResponse) {};
    rpc CopyFiEquitiesAccountsTxnsToDeleteCol(CopyFiEquitiesAccountsTxnsToDeleteColRequest) returns (CopyFiEquitiesAccountsTxnsToDeleteColResponse) {};
    rpc FindAnyEquitiesTxnMatchingIds(FindAnyEquitiesTxnMatchingIdsRequest) returns (FindAnyEquitiesTxnMatchingIdsResponse) {};
}


message UpsertEquitiesAccountsTransactionsRequest {
    repeated database.FiEquitiesAccountTransaction records = 1;
}
message UpsertEquitiesAccountsTransactionsResponse {
	map<int64, string> inserted_docs_index_to_id_map = 1;
}


message GetEquityTransactionsRequest {
    string account_id = 1;
    int64 from = 2;
}
message GetEquityTransactionsResponse {
	repeated database.FiEquitiesAccountTransaction records = 1;
}

message CopyFiEquitiesAccountsTxnsToDeleteColRequest{
	repeated string account_ids = 1;
}
message CopyFiEquitiesAccountsTxnsToDeleteColResponse {}

message DeleteFiEquitiesAccountsTransactionsRequest{
	repeated string account_ids = 1;
}
message DeleteFiEquitiesAccountsTransactionsResponse{}


message FindAnyEquitiesTxnMatchingIdsRequest{
    string account_id = 1;
    repeated string transaction_ids = 2;
}
message FindAnyEquitiesTxnMatchingIdsResponse{
    database.FiEquitiesAccountTransaction record = 1;
}

message GetEquityTransactionsInAccountsRequest {
    repeated string account_ids = 1;
    int64 timestamp_after = 2;
}

message GetEquityTransactionsInAccountsResponse {
    repeated database.FiEquitiesAccountTransaction records = 1;
}