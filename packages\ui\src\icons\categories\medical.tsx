import * as React from "react";
import type { SVGProps } from "react";
const SvgMedical = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="none"
    viewBox="0 0 36 36"
    {...props}
  >
    <g clipPath="url(#medical_svg__a)">
      <path
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={3}
        d="M34.726 9.66v5.645H29.36M1.478 26.267v-5.645h5.366"
      />
      <path
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={3}
        d="M5.248 13.456a13.455 13.455 0 0 1 22.201-5.024l6.937 6.519m-32.89 5.98 6.936 6.518a13.455 13.455 0 0 0 22.202-5.023"
      />
      <path
        fill="currentColor"
        fillRule="evenodd"
        d="M20.369 13.754a.964.964 0 0 0-.964-.963h-1.928a.964.964 0 0 0-.964.963v2.892h-2.892a.964.964 0 0 0-.964.964v1.928c0 .532.432.964.964.964h2.892v2.891c0 .533.432.964.964.964h1.928a.964.964 0 0 0 .964-.964v-2.891h2.891a.964.964 0 0 0 .964-.964V17.61a.964.964 0 0 0-.964-.964h-2.891z"
        clipRule="evenodd"
      />
    </g>
    <defs>
      <clipPath id="medical_svg__a">
        <path fill="#fff" d="M0 0h35.881v35.881H0z" />
      </clipPath>
    </defs>
  </svg>
);
export default SvgMedical;
