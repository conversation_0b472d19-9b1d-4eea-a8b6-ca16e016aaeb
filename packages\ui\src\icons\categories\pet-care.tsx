import * as React from "react";
import type { SVGProps } from "react";
const SvgPetCare = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="currentColor"
    viewBox="0 0 31 30"
    {...props}
  >
    <path d="M20.378 8.371a3.26 3.26 0 0 0 2.308-5.57 3.26 3.26 0 0 0-2.308-.954 3.26 3.26 0 0 0-2.308 5.57 3.26 3.26 0 0 0 2.308.954m-12.985 3.23a3.26 3.26 0 0 0-5.57-2.307 3.26 3.26 0 0 0-.954 2.308 3.26 3.26 0 0 0 5.57 2.308 3.26 3.26 0 0 0 .954-2.308m19.476-3.23a3.26 3.26 0 0 0-2.307 5.57 3.26 3.26 0 0 0 2.307.954 3.26 3.26 0 0 0 2.308-5.57 3.26 3.26 0 0 0-2.308-.954m-16.215 0a3.26 3.26 0 0 0 2.308-5.57 3.26 3.26 0 0 0-2.308-.954 3.26 3.26 0 0 0-2.307 5.57 3.26 3.26 0 0 0 2.307.954m11.231 6.923a6.56 6.56 0 0 0-3.477-4.647 6.39 6.39 0 0 0-5.785 0 6.5 6.5 0 0 0-3.477 4.647 6.5 6.5 0 0 0-3.815 3.878 6.43 6.43 0 0 0 .493 5.416 6.46 6.46 0 0 0 4.43 3.138 6.49 6.49 0 0 0 5.262-1.353 6.37 6.37 0 0 0 5.262 1.323c1.877-.37 3.476-1.447 4.43-3.108a6.44 6.44 0 0 0 .493-5.416 6.51 6.51 0 0 0-3.816-3.878" />
  </svg>
);
export default SvgPetCare;
