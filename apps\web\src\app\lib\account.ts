import * as grpc from "@grpc/grpc-js";
import { Account } from "@repo/ui";
import { requestMetadata, createGrpcClient } from "./utils/create-grpc-client";
import { GRPC_CONFIG } from "./constants";

interface GetAccountsResponse {
  accounts: Account[];
}

type AccountClient = grpc.Client & {
  GetAccounts: (
    request: object,
    metadata: grpc.Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: GetAccountsResponse,
    ) => void,
  ) => void;
};

const accountClient: AccountClient = createGrpcClient<AccountClient>({
  protoPath: GRPC_CONFIG.PROTO_FILES.SETTINGS,
  servicePath: "backend_services.user",
  serviceConstructor: "Settings",
});

/**
 * Gets accounts from the API
 * @param authToken The authentication token
 * @returns A promise that resolves to the accounts response
 */
export function getAccounts(authToken: string): Promise<GetAccountsResponse> {
  requestMetadata.set("authorization", authToken);
  return new Promise((resolve, reject) => {
    accountClient.GetAccounts({}, requestMetadata, (error, response) => {
      if (error) {
        reject(error);
      } else {
        resolve(response);
      }
    });
  });
}
