import * as React from "react";
import type { SVGProps } from "react";
const SvgTax = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="currentColor"
    viewBox="0 0 29 30"
    {...props}
  >
    <path
      fillRule="evenodd"
      d="M28.385 21.68a14.72 14.72 0 0 1-9.392 7.646C11.115 31.461 3.003 26.765.87 18.886-1.227 11.008 3.43 2.896 11.348.762c4.618-1.242 9.314-.117 12.846 2.6l-4.851 5.783c-1.708-1.203-3.882-1.67-6.055-1.087a7.236 7.236 0 0 0-5.123 8.888c1.01 3.842 5.007 6.17 8.888 5.122a7.18 7.18 0 0 0 4.657-3.92zm-.116-10.013c.66 2.523.388 5.046-.621 7.219l-4.812-2.523a5.6 5.6 0 0 0 .194-3.298 5.3 5.3 0 0 0-1.747-2.678l3.493-4.153c1.63 1.358 2.91 3.221 3.493 5.433"
      clipRule="evenodd"
    />
  </svg>
);
export default SvgTax;
