syntax = "proto3";
package backend_services.data_access.public_data;

service PublicDataMutualFundInfo {
    rpc CreateMutualFund(stream MutualFundInfoRequest) returns (MutualFundInfoResponse){};
}

message MutualFundInfoRequest {
    string scheme_code = 1;
    string amc_name = 2;
    string scheme_name = 3;
    string scheme_category = 4;
    string scheme_type = 5;
    string isin_payout_growth = 6;
    string isin_reinvestment = 7;
    string scheme_subcategory = 8;
    string plan = 9;
    string option = 10;
    string idcw_type = 11;
}
message MutualFundInfoResponse{}
