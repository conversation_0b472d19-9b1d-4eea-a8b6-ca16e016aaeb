import React from "react";

const CustomTooltip = ({ value }: { value?: string }) => {
  // CSS variables for triangle
  const b = "9px"; // base
  const h = "9px"; // height
  const p = "50%"; // position
  const r = "8px"; // radius

  return (
    <div className="inline-block" style={{ transform: "translateY(-10px)" }}>
      <div
        className="relative h-6 flex items-center justify-center px-2 text-white text-xs 
                  min-w-10 max-w-40 text-center bg-primary z-10 mb-2.5"
        style={{
          borderRadius: `${r} ${r} calc(min(${r}, 100% - ${p} - ${b}/2)) calc(min(${r}, ${p} - ${b}/2))`,
        }}
      >
        {value}

        <div
          className="absolute bg-primary"
          style={{
            content: '""',
            zIndex: "-1",
            inset: `0 0 calc(-1*${h})`,
            clipPath: `polygon(
              min(100%, calc(${p} + ${b}/2)) calc(100% - ${h}),
              ${p} 100%,
              max(0%, calc(${p} - ${b}/2)) calc(100% - ${h}),
              50% 50%
            )`,
          }}
        />
      </div>
    </div>
  );
};

export { CustomTooltip };
