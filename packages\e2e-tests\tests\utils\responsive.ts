import { Page } from "@playwright/test";

/**
 * Common device viewport sizes
 */
export const VIEWPORT_SIZES = {
  MOBILE_SMALL: { width: 320, height: 568 }, // iPhone SE
  MOBILE: { width: 375, height: 667 }, // iPhone 8
  MOBILE_LARGE: { width: 414, height: 896 }, // iPhone 11 Pro Max
  TABLET: { width: 768, height: 1024 }, // iPad
  TABLET_LARGE: { width: 1024, height: 1366 }, // iPad Pro
  DESKTOP: { width: 1280, height: 800 }, // Small laptop
  DESKTOP_LARGE: { width: 1920, height: 1080 }, // Large desktop
};

/**
 * Viewport size with a name
 */
export interface NamedViewport {
  name: string;
  width: number;
  height: number;
}

/**
 * Common device viewports with names
 */
export const NAMED_VIEWPORTS: NamedViewport[] = [
  { name: "Mobile Small", ...VIEWPORT_SIZES.MOBILE_SMALL },
  { name: "Mobile", ...VIEWPORT_SIZES.MOBILE },
  { name: "Mobile Large", ...VIEWPORT_SIZES.MOBILE_LARGE },
  { name: "Tablet", ...VIEWPORT_SIZES.TABLET },
  { name: "Tablet Large", ...VIEWPORT_SIZES.TABLET_LARGE },
  { name: "Desktop", ...VIEWPORT_SIZES.DESKTOP },
  { name: "Desktop Large", ...VIEWPORT_SIZES.DESKTOP_LARGE },
];

/**
 * Set the viewport size
 * @param page - Playwright page
 * @param size - Viewport size
 */
export async function setViewportSize(
  page: Page,
  size: { width: number; height: number },
): Promise<void> {
  await page.setViewportSize(size);
}

/**
 * Run a test across multiple viewport sizes
 * @param page - Playwright page
 * @param viewports - Array of viewport sizes to test
 * @param testFn - Test function to run for each viewport
 */
export async function testAcrossViewports(
  page: Page,
  viewports: NamedViewport[],
  testFn: (viewport: NamedViewport) => Promise<void>,
): Promise<void> {
  for (const viewport of viewports) {
    // Set viewport size
    await setViewportSize(page, {
      width: viewport.width,
      height: viewport.height,
    });

    // Run test function
    await testFn(viewport);
  }
}
