syntax = "proto3";
package rebit.fi_schemas.deposit;

/*
  REBIT FI Type - Deposit Schema as per https://specifications.rebit.org.in/api_schema/account_aggregator/documentation/deposit_v1.2.html
*/


// Basic Profile of the account which should include the Account Owner information, maskedAccNumber and linkedAccRef numbers, type of account specific to the FI type and any other generic details as might be pertinent for the specified FI type.
message Profile {
  Holders holders = 1; // @gotags: json:"Holders"
}

message Account {
  string type = 1; // @gotags: json:"type"
  string masked_acc_number = 2; // @gotags: json:"maskedAccNumber"
  float version = 3; // @gotags: json:"version,string"
  string linked_acc_ref = 4; // @gotags: json:"linkedAccRef"
  // Basic Profile of the account which should include the Account Owner information, maskedAccNumber and linkedAccRef numbers, type of account specific to the FI type and any other generic details as might be pertinent for the specified FI type.
  Profile profile = 5; // @gotags: json:"Profile"
  // The value of the account, term of the deposits if relevant and any other data that summarizes the funds in the account.
  Summary summary = 6; // @gotags: json:"Summary"
  // Details of all transactions that have been posted in an account.
  Transactions transactions = 7; // @gotags: json:"Transactions"
}

enum AccountType {
  // Default
  SAVINGS = 0;
  CURRENT = 1;
}

// Personal details of the deposit account holder
message Holder {
  // Name of account holder
  string name = 1; // @gotags: json:"name"
  // Date of birth of account holder
  DateOfBirth dob = 2; // @gotags: json:"dob"
  // Mobile number of account holder
  string mobile = 3; // @gotags: json:"mobile"
  // Status of nominee registered with the account
  string nominee = 4; // @gotags: json:"nominee"
  // Landline number of account holder
  string landline = 5; // @gotags: json:"landline"
  // Address of account holder
  string address = 6; // @gotags: json:"address"
  // Email ID of account holder
  string email = 7; // @gotags: json:"email"
  // PAN number of account holder
  string pan = 8; // @gotags: json:"pan"
  // KYC status whether its completed or pending
  bool ckyc_compliance = 9; // @gotags: json:"ckycCompliance"
  // Type of account held by an individual whether its single or jointly operated.
  // Note: This differs between TSP and ReBIT
  string type = 10; // @gotags: json:"type"

  // Note: This differs between TSP and ReBIT
  string masked_acc_number = 11; // @gotags: json:"maskedAccNumber"
  string linked_acc_ref = 12; // @gotags: json:"linkedAccRef"

  message DateOfBirth {
    int64 value = 1;
  }
}

message Holders {
  // Type of account held by an individual whether its single or jointly operated.
  string type = 1; // @gotags: json:"type"
  // Personal details of the deposit account holder
  repeated Holder holder = 2; // @gotags: json:"Holder"
}

enum HoldersType {
  // Default
  SINGLE = 0;
  JOINT = 1;
}

enum HoldingNominee {
  // Default
  REGISTERED = 0;
  NOT_REGISTERED = 1;
}

message Pending {
  // The amount invested or to be invested in deposits such as Fixed or Recurring.
  string transaction_type = 1; // @gotags: json:"transactionType"
  // Pending amount
  double amount = 2; // @gotags: json:"amount"
}

enum StatusTypes {
  // Default
  ACTIVE = 0;
  INACTIVE = 1;
}

enum SummaryFacility {
  // Default
  OD = 0;
  CC = 1;
}

// The value of the account, term of the deposits if relevant and any other data that summarizes the funds in the account.
message Summary {
  // Available Balance.
  string current_balance = 1; // @gotags: json:"currentBalance"
  // Currency in which transaction taken place.
  string currency = 2; // @gotags: json:"currency"
  // Currency conversion exchange rate for the day.
  string exchge_rate = 3; // @gotags: json:"exchgeRate"
  // Date and time stamp for which current balance recorded.
  BalanceDateTime balance_date_time = 4; // @gotags: json:"balanceDateTime"
  // Type of account whether is saving or current
  string type = 5; // @gotags: json:"type"
  // Location of branch where investment was made
  string branch = 6; // @gotags: json:"branch"
  // Additional facility like Overdraft or Sweep In applicable for the given account.
  string facility = 7; // @gotags: json:"facility"
  // IFSC code of the issued bank branch
  string ifsc_code = 8; // @gotags: json:"ifscCode"
  // MICR code is uniquely identifies a bank and a branch participating in an ECS.
  string micr_code = 9; // @gotags: json:"micrCode"
  // Opening date of the deposit account
  string opening_date = 10; // @gotags: json:"openingDate"
  // The portion of the sanctioned limit that is available to be drawn, as of this point in time
  string current_o_d_limit = 11; // @gotags: json:"currentODLimit"
  // Sanctioned limit (overall limit given by the bank, on the OD/CC facility).
  string drawing_limit = 12; // @gotags: json:"drawingLimit"
  // An account status with either active or inactive. An inactive account includes the dormant, inactive, or closed account.
  string status = 13; // @gotags: json:"status"
  
  // Note: This differs between TSP and ReBIT
  // The amount invested or to be invested in deposits such as Fixed or Recurring.
  string pending_transaction_type = 14; // @gotags: json:"pending_transactionType"
  // Pending amount
  double pending_amount = 15; // @gotags: json:"pending_amount"

  message BalanceDateTime {
    int64 value = 1;
  }
}

message Transaction {
  // Type of account transaction either debit or credit
  string type = 1; // @gotags: json:"type"
  // Mode of investment captured whether online/demat or physical form.
  string mode = 2; // @gotags: json:"mode"
  // Amount of transaction.
  double amount = 3; // @gotags: json:"amount"
  // Available balance.
  double current_balance = 4; // @gotags: json:"currentBalance"
  // Transaction date time stamp for particular record when transaction taken place
  TransactionTimestamp transaction_timestamp = 5; // @gotags: json:"transactionTimestamp"
  TransactionTimestamp value_date = 6; // @gotags: json:"valueDate"
  // Unique id of the transaction
  string txn_id = 7; // @gotags: json:"txnId"
  // Narration is additional details in form of description of remark associated with investment.
  string narration = 8; // @gotags: json:"narration"
  // The cheque or reference no for the given transaction.
  string reference = 9; // @gotags: json:"reference"

  message TransactionTimestamp {
    int64 value = 1;
  }
}

enum TransactionMode {
  // Default
  CASH = 0;
  ATM = 1;
  CARD = 2;
  UPI = 3;
  FT = 4;
  OTHERS = 5;
}

enum TransactionType {
  // Default
  CREDIT = 0;
  DEBIT = 1;
}

// Details of all transactions that have been posted in an account.
message Transactions {
  // Start date of transaction or period for which details are require.
  string start_date = 1; // @gotags: json:"startDate"
  // End date of transaction or period for which details are require.
  string end_date = 2; // @gotags: json:"endDate"
  repeated Transaction transaction = 3; // @gotags: json:"Transaction"
}
