import * as React from "react";
import type { SVGProps } from "react";
const SvgSubscription = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="currentColor"
    viewBox="0 0 31 30"
    {...props}
  >
    <path d="M30.132 6.162c0-1.768-1.544-3.206-3.44-3.206H23.77l.002-1.492c0-.577-.502-1.045-1.122-1.045s-1.12.468-1.12 1.045v1.493H9.472V1.464c0-.577-.502-1.045-1.121-1.045-.62 0-1.121.468-1.121 1.045v1.493H4.308C2.412 2.957.87 4.396.87 6.163v3.543c0 .577.502 1.045 1.121 1.045h27.02c.62 0 1.122-.468 1.122-1.045zM30.13 26.476V13.484c0-.578-.502-1.046-1.121-1.046H1.99c-.62 0-1.12.468-1.12 1.046v12.992c0 1.767 1.54 3.205 3.437 3.205h22.385c1.896 0 3.438-1.438 3.438-3.206" />
  </svg>
);
export default SvgSubscription;
