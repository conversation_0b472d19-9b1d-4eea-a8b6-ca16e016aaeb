syntax = "proto3";
package backend_services.data_access;

import "database/core.proto";

service DeletedAccountLog {
    rpc InsertDeletedAccountLog(InsertDeletedAccountLogRequest) returns (InsertDeletedAccountLogResponse) {};
    rpc GetAccountsToDelete(GetAccountsToDeleteRequest) returns (GetAccountsToDeleteResponse) {};
    rpc DeleteAccountDeletedLogs(DeleteAccountDeletedLogsRequest) returns (DeleteAccountDeletedLogsResponse) {};
}

message InsertDeletedAccountLogRequest {
	repeated database.DeletedAccountLog records = 1;
}
message InsertDeletedAccountLogResponse {};

message GetAccountsToDeleteRequest {
}
message GetAccountsToDeleteResponse {
    repeated database.DeletedAccountLog accounts = 1;
}

message DeleteAccountDeletedLogsRequest {
    repeated string ids = 1;
}
message DeleteAccountDeletedLogsResponse {}