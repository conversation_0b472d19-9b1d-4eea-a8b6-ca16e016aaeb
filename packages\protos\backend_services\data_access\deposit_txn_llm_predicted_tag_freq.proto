syntax = "proto3";

package backend_services.data_access;
import "database/custom_type.proto";

service DepositTxnLlmPredictedTagFreq {
    rpc GetDepositTxnLlmGivenTags(GetDepositTxnLlmGivenTagsRequest) returns (GetDepositTxnLlmGivenTagsResponse) {};
    rpc DeleteAllDepositTxnLlmPredictedTagFreq(DeleteAllDepositTxnLlmPredictedTagFreqRequest) returns (DeleteAllDepositTxnLlmPredictedTagFreqResponse) {};
}

message DeleteAllDepositTxnLlmPredictedTagFreqRequest {}
message DeleteAllDepositTxnLlmPredictedTagFreqResponse {}

message GetDepositTxnLlmGivenTagsRequest{
    message Transaction {
        string txn_id = 1;
        string txn_type = 2;
        repeated string Keywords = 3;
    }

    repeated Transaction transactions = 1;
    database.ObjectId user_id = 2;
}
message GetDepositTxnLlmGivenTagsResponse{
    message Record {
        string txn_id = 1;
        database.ObjectId category_id = 2;
        database.ObjectId subcategory_id = 3;
        double likelihood_product = 4;
        double normalized_likelihood_product = 5;
    }

    repeated Record records = 10;
}