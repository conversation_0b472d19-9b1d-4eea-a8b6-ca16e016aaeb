// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.2
//   protoc               v6.31.1
// source: backend_services/visualization/wealth.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import {
  type CallOptions,
  ChannelCredentials,
  Client,
  type ClientOptions,
  type ClientReadableStream,
  type handleServerStreamingCall,
  makeGenericClientConstructor,
  Metadata,
  type UntypedServiceImplementation,
} from "@grpc/grpc-js";
import { AccountsFilter, UserGroupFilters } from "../../database/cards_filters";
import { FiType, fiTypeFromJSON, fiTypeToJSON } from "../../database/core";
import { CategoryCashflow, OverallCashflow } from "./shared";

export const protobufPackage = "backend_services.visualization";

export enum WorthCategory {
  VALUE_CATEGORY_UNSPECIFIED = 0,
  VALUE_CATEGORY_SAVINGS = 1,
  VALUE_CATEGORY_INVESTMENTS = 2,
  UNRECOGNIZED = -1,
}

export function worthCategoryFromJSON(object: any): WorthCategory {
  switch (object) {
    case 0:
    case "VALUE_CATEGORY_UNSPECIFIED":
      return WorthCategory.VALUE_CATEGORY_UNSPECIFIED;
    case 1:
    case "VALUE_CATEGORY_SAVINGS":
      return WorthCategory.VALUE_CATEGORY_SAVINGS;
    case 2:
    case "VALUE_CATEGORY_INVESTMENTS":
      return WorthCategory.VALUE_CATEGORY_INVESTMENTS;
    case -1:
    case "UNRECOGNIZED":
    default:
      return WorthCategory.UNRECOGNIZED;
  }
}

export function worthCategoryToJSON(object: WorthCategory): string {
  switch (object) {
    case WorthCategory.VALUE_CATEGORY_UNSPECIFIED:
      return "VALUE_CATEGORY_UNSPECIFIED";
    case WorthCategory.VALUE_CATEGORY_SAVINGS:
      return "VALUE_CATEGORY_SAVINGS";
    case WorthCategory.VALUE_CATEGORY_INVESTMENTS:
      return "VALUE_CATEGORY_INVESTMENTS";
    case WorthCategory.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface FetchWidgetsRequest {
  userGroups: UserGroupFilters | undefined;
  accountsFilter: AccountsFilter | undefined;
}

export interface FetchWidgetsResponse {
  widgets: Widget[];
}

export interface Widget {
  totalNetWorthWidget?: TotalNetWorthWidget | undefined;
  userNetWorthWidget?: UserNetWorthWidget | undefined;
  groupNetWorthWidget?: GroupNetWorthWidget | undefined;
  groupSavingsWidget?: GroupSavingsWidget | undefined;
  userSavingsWidget?: UserSavingsWidget | undefined;
  investmentsWorthWidget?: InvestmentsWorthWidget | undefined;
  cashflowWidget?: CashflowWidget | undefined;
}

export interface TotalNetWorthWidget {
  totalNetWorth: number;
  categoryWorths: TotalNetWorthWidget_CategoryWorth[];
}

export interface TotalNetWorthWidget_CategoryWorth {
  worth: number;
  worthCategory: WorthCategory;
}

export interface GroupSavingsWidget {
  totalBalance: number;
  savings: GroupSavingsWidget_GroupSavings[];
}

export interface GroupSavingsWidget_GroupSavings {
  balance: number;
  percentage: number;
  groupId: string;
}

export interface UserSavingsWidget {
  totalBalance: number;
  savings: UserSavingsWidget_UserSavings[];
}

export interface UserSavingsWidget_UserSavings {
  balance: number;
  percentage: number;
  userId: string;
}

export interface InvestmentsWorthWidget {
  totalValue: number;
  instrumentValues: InvestmentsWorthWidget_InstrumentValue[];
}

export interface InvestmentsWorthWidget_InstrumentValue {
  value: number;
  instrument: FiType;
}

export interface UserNetWorthWidget {
  netWorths: UserNetWorthWidget_UserNetWorth[];
}

export interface UserNetWorthWidget_UserNetWorth {
  netWorth: number;
  userId: string;
}

export interface GroupNetWorthWidget {
  netWorths: GroupNetWorthWidget_GroupNetWorth[];
}

export interface GroupNetWorthWidget_GroupNetWorth {
  netWorth: number;
  groupId: string;
}

export interface CashflowWidget {
  overallCashflows: OverallCashflow[];
  categoryCashflows: CategoryCashflow[];
}

function createBaseFetchWidgetsRequest(): FetchWidgetsRequest {
  return { userGroups: undefined, accountsFilter: undefined };
}

export const FetchWidgetsRequest: MessageFns<FetchWidgetsRequest> = {
  encode(message: FetchWidgetsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userGroups !== undefined) {
      UserGroupFilters.encode(message.userGroups, writer.uint32(10).fork()).join();
    }
    if (message.accountsFilter !== undefined) {
      AccountsFilter.encode(message.accountsFilter, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FetchWidgetsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFetchWidgetsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userGroups = UserGroupFilters.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.accountsFilter = AccountsFilter.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FetchWidgetsRequest {
    return {
      userGroups: isSet(object.userGroups) ? UserGroupFilters.fromJSON(object.userGroups) : undefined,
      accountsFilter: isSet(object.accountsFilter) ? AccountsFilter.fromJSON(object.accountsFilter) : undefined,
    };
  },

  toJSON(message: FetchWidgetsRequest): unknown {
    const obj: any = {};
    if (message.userGroups !== undefined) {
      obj.userGroups = UserGroupFilters.toJSON(message.userGroups);
    }
    if (message.accountsFilter !== undefined) {
      obj.accountsFilter = AccountsFilter.toJSON(message.accountsFilter);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FetchWidgetsRequest>, I>>(base?: I): FetchWidgetsRequest {
    return FetchWidgetsRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FetchWidgetsRequest>, I>>(object: I): FetchWidgetsRequest {
    const message = createBaseFetchWidgetsRequest();
    message.userGroups = (object.userGroups !== undefined && object.userGroups !== null)
      ? UserGroupFilters.fromPartial(object.userGroups)
      : undefined;
    message.accountsFilter = (object.accountsFilter !== undefined && object.accountsFilter !== null)
      ? AccountsFilter.fromPartial(object.accountsFilter)
      : undefined;
    return message;
  },
};

function createBaseFetchWidgetsResponse(): FetchWidgetsResponse {
  return { widgets: [] };
}

export const FetchWidgetsResponse: MessageFns<FetchWidgetsResponse> = {
  encode(message: FetchWidgetsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.widgets) {
      Widget.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FetchWidgetsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFetchWidgetsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.widgets.push(Widget.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FetchWidgetsResponse {
    return {
      widgets: globalThis.Array.isArray(object?.widgets) ? object.widgets.map((e: any) => Widget.fromJSON(e)) : [],
    };
  },

  toJSON(message: FetchWidgetsResponse): unknown {
    const obj: any = {};
    if (message.widgets?.length) {
      obj.widgets = message.widgets.map((e) => Widget.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FetchWidgetsResponse>, I>>(base?: I): FetchWidgetsResponse {
    return FetchWidgetsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FetchWidgetsResponse>, I>>(object: I): FetchWidgetsResponse {
    const message = createBaseFetchWidgetsResponse();
    message.widgets = object.widgets?.map((e) => Widget.fromPartial(e)) || [];
    return message;
  },
};

function createBaseWidget(): Widget {
  return {
    totalNetWorthWidget: undefined,
    userNetWorthWidget: undefined,
    groupNetWorthWidget: undefined,
    groupSavingsWidget: undefined,
    userSavingsWidget: undefined,
    investmentsWorthWidget: undefined,
    cashflowWidget: undefined,
  };
}

export const Widget: MessageFns<Widget> = {
  encode(message: Widget, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.totalNetWorthWidget !== undefined) {
      TotalNetWorthWidget.encode(message.totalNetWorthWidget, writer.uint32(10).fork()).join();
    }
    if (message.userNetWorthWidget !== undefined) {
      UserNetWorthWidget.encode(message.userNetWorthWidget, writer.uint32(18).fork()).join();
    }
    if (message.groupNetWorthWidget !== undefined) {
      GroupNetWorthWidget.encode(message.groupNetWorthWidget, writer.uint32(26).fork()).join();
    }
    if (message.groupSavingsWidget !== undefined) {
      GroupSavingsWidget.encode(message.groupSavingsWidget, writer.uint32(34).fork()).join();
    }
    if (message.userSavingsWidget !== undefined) {
      UserSavingsWidget.encode(message.userSavingsWidget, writer.uint32(42).fork()).join();
    }
    if (message.investmentsWorthWidget !== undefined) {
      InvestmentsWorthWidget.encode(message.investmentsWorthWidget, writer.uint32(50).fork()).join();
    }
    if (message.cashflowWidget !== undefined) {
      CashflowWidget.encode(message.cashflowWidget, writer.uint32(58).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Widget {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWidget();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.totalNetWorthWidget = TotalNetWorthWidget.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.userNetWorthWidget = UserNetWorthWidget.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.groupNetWorthWidget = GroupNetWorthWidget.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.groupSavingsWidget = GroupSavingsWidget.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.userSavingsWidget = UserSavingsWidget.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.investmentsWorthWidget = InvestmentsWorthWidget.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.cashflowWidget = CashflowWidget.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Widget {
    return {
      totalNetWorthWidget: isSet(object.totalNetWorthWidget)
        ? TotalNetWorthWidget.fromJSON(object.totalNetWorthWidget)
        : undefined,
      userNetWorthWidget: isSet(object.userNetWorthWidget)
        ? UserNetWorthWidget.fromJSON(object.userNetWorthWidget)
        : undefined,
      groupNetWorthWidget: isSet(object.groupNetWorthWidget)
        ? GroupNetWorthWidget.fromJSON(object.groupNetWorthWidget)
        : undefined,
      groupSavingsWidget: isSet(object.groupSavingsWidget)
        ? GroupSavingsWidget.fromJSON(object.groupSavingsWidget)
        : undefined,
      userSavingsWidget: isSet(object.userSavingsWidget)
        ? UserSavingsWidget.fromJSON(object.userSavingsWidget)
        : undefined,
      investmentsWorthWidget: isSet(object.investmentsWorthWidget)
        ? InvestmentsWorthWidget.fromJSON(object.investmentsWorthWidget)
        : undefined,
      cashflowWidget: isSet(object.cashflowWidget) ? CashflowWidget.fromJSON(object.cashflowWidget) : undefined,
    };
  },

  toJSON(message: Widget): unknown {
    const obj: any = {};
    if (message.totalNetWorthWidget !== undefined) {
      obj.totalNetWorthWidget = TotalNetWorthWidget.toJSON(message.totalNetWorthWidget);
    }
    if (message.userNetWorthWidget !== undefined) {
      obj.userNetWorthWidget = UserNetWorthWidget.toJSON(message.userNetWorthWidget);
    }
    if (message.groupNetWorthWidget !== undefined) {
      obj.groupNetWorthWidget = GroupNetWorthWidget.toJSON(message.groupNetWorthWidget);
    }
    if (message.groupSavingsWidget !== undefined) {
      obj.groupSavingsWidget = GroupSavingsWidget.toJSON(message.groupSavingsWidget);
    }
    if (message.userSavingsWidget !== undefined) {
      obj.userSavingsWidget = UserSavingsWidget.toJSON(message.userSavingsWidget);
    }
    if (message.investmentsWorthWidget !== undefined) {
      obj.investmentsWorthWidget = InvestmentsWorthWidget.toJSON(message.investmentsWorthWidget);
    }
    if (message.cashflowWidget !== undefined) {
      obj.cashflowWidget = CashflowWidget.toJSON(message.cashflowWidget);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Widget>, I>>(base?: I): Widget {
    return Widget.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Widget>, I>>(object: I): Widget {
    const message = createBaseWidget();
    message.totalNetWorthWidget = (object.totalNetWorthWidget !== undefined && object.totalNetWorthWidget !== null)
      ? TotalNetWorthWidget.fromPartial(object.totalNetWorthWidget)
      : undefined;
    message.userNetWorthWidget = (object.userNetWorthWidget !== undefined && object.userNetWorthWidget !== null)
      ? UserNetWorthWidget.fromPartial(object.userNetWorthWidget)
      : undefined;
    message.groupNetWorthWidget = (object.groupNetWorthWidget !== undefined && object.groupNetWorthWidget !== null)
      ? GroupNetWorthWidget.fromPartial(object.groupNetWorthWidget)
      : undefined;
    message.groupSavingsWidget = (object.groupSavingsWidget !== undefined && object.groupSavingsWidget !== null)
      ? GroupSavingsWidget.fromPartial(object.groupSavingsWidget)
      : undefined;
    message.userSavingsWidget = (object.userSavingsWidget !== undefined && object.userSavingsWidget !== null)
      ? UserSavingsWidget.fromPartial(object.userSavingsWidget)
      : undefined;
    message.investmentsWorthWidget =
      (object.investmentsWorthWidget !== undefined && object.investmentsWorthWidget !== null)
        ? InvestmentsWorthWidget.fromPartial(object.investmentsWorthWidget)
        : undefined;
    message.cashflowWidget = (object.cashflowWidget !== undefined && object.cashflowWidget !== null)
      ? CashflowWidget.fromPartial(object.cashflowWidget)
      : undefined;
    return message;
  },
};

function createBaseTotalNetWorthWidget(): TotalNetWorthWidget {
  return { totalNetWorth: 0, categoryWorths: [] };
}

export const TotalNetWorthWidget: MessageFns<TotalNetWorthWidget> = {
  encode(message: TotalNetWorthWidget, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.totalNetWorth !== 0) {
      writer.uint32(9).double(message.totalNetWorth);
    }
    for (const v of message.categoryWorths) {
      TotalNetWorthWidget_CategoryWorth.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TotalNetWorthWidget {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTotalNetWorthWidget();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.totalNetWorth = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.categoryWorths.push(TotalNetWorthWidget_CategoryWorth.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TotalNetWorthWidget {
    return {
      totalNetWorth: isSet(object.totalNetWorth) ? globalThis.Number(object.totalNetWorth) : 0,
      categoryWorths: globalThis.Array.isArray(object?.categoryWorths)
        ? object.categoryWorths.map((e: any) => TotalNetWorthWidget_CategoryWorth.fromJSON(e))
        : [],
    };
  },

  toJSON(message: TotalNetWorthWidget): unknown {
    const obj: any = {};
    if (message.totalNetWorth !== 0) {
      obj.totalNetWorth = message.totalNetWorth;
    }
    if (message.categoryWorths?.length) {
      obj.categoryWorths = message.categoryWorths.map((e) => TotalNetWorthWidget_CategoryWorth.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TotalNetWorthWidget>, I>>(base?: I): TotalNetWorthWidget {
    return TotalNetWorthWidget.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TotalNetWorthWidget>, I>>(object: I): TotalNetWorthWidget {
    const message = createBaseTotalNetWorthWidget();
    message.totalNetWorth = object.totalNetWorth ?? 0;
    message.categoryWorths = object.categoryWorths?.map((e) => TotalNetWorthWidget_CategoryWorth.fromPartial(e)) || [];
    return message;
  },
};

function createBaseTotalNetWorthWidget_CategoryWorth(): TotalNetWorthWidget_CategoryWorth {
  return { worth: 0, worthCategory: 0 };
}

export const TotalNetWorthWidget_CategoryWorth: MessageFns<TotalNetWorthWidget_CategoryWorth> = {
  encode(message: TotalNetWorthWidget_CategoryWorth, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.worth !== 0) {
      writer.uint32(9).double(message.worth);
    }
    if (message.worthCategory !== 0) {
      writer.uint32(16).int32(message.worthCategory);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TotalNetWorthWidget_CategoryWorth {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTotalNetWorthWidget_CategoryWorth();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.worth = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.worthCategory = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TotalNetWorthWidget_CategoryWorth {
    return {
      worth: isSet(object.worth) ? globalThis.Number(object.worth) : 0,
      worthCategory: isSet(object.worthCategory) ? worthCategoryFromJSON(object.worthCategory) : 0,
    };
  },

  toJSON(message: TotalNetWorthWidget_CategoryWorth): unknown {
    const obj: any = {};
    if (message.worth !== 0) {
      obj.worth = message.worth;
    }
    if (message.worthCategory !== 0) {
      obj.worthCategory = worthCategoryToJSON(message.worthCategory);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TotalNetWorthWidget_CategoryWorth>, I>>(
    base?: I,
  ): TotalNetWorthWidget_CategoryWorth {
    return TotalNetWorthWidget_CategoryWorth.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TotalNetWorthWidget_CategoryWorth>, I>>(
    object: I,
  ): TotalNetWorthWidget_CategoryWorth {
    const message = createBaseTotalNetWorthWidget_CategoryWorth();
    message.worth = object.worth ?? 0;
    message.worthCategory = object.worthCategory ?? 0;
    return message;
  },
};

function createBaseGroupSavingsWidget(): GroupSavingsWidget {
  return { totalBalance: 0, savings: [] };
}

export const GroupSavingsWidget: MessageFns<GroupSavingsWidget> = {
  encode(message: GroupSavingsWidget, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.totalBalance !== 0) {
      writer.uint32(9).double(message.totalBalance);
    }
    for (const v of message.savings) {
      GroupSavingsWidget_GroupSavings.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GroupSavingsWidget {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGroupSavingsWidget();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.totalBalance = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.savings.push(GroupSavingsWidget_GroupSavings.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GroupSavingsWidget {
    return {
      totalBalance: isSet(object.totalBalance) ? globalThis.Number(object.totalBalance) : 0,
      savings: globalThis.Array.isArray(object?.savings)
        ? object.savings.map((e: any) => GroupSavingsWidget_GroupSavings.fromJSON(e))
        : [],
    };
  },

  toJSON(message: GroupSavingsWidget): unknown {
    const obj: any = {};
    if (message.totalBalance !== 0) {
      obj.totalBalance = message.totalBalance;
    }
    if (message.savings?.length) {
      obj.savings = message.savings.map((e) => GroupSavingsWidget_GroupSavings.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GroupSavingsWidget>, I>>(base?: I): GroupSavingsWidget {
    return GroupSavingsWidget.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GroupSavingsWidget>, I>>(object: I): GroupSavingsWidget {
    const message = createBaseGroupSavingsWidget();
    message.totalBalance = object.totalBalance ?? 0;
    message.savings = object.savings?.map((e) => GroupSavingsWidget_GroupSavings.fromPartial(e)) || [];
    return message;
  },
};

function createBaseGroupSavingsWidget_GroupSavings(): GroupSavingsWidget_GroupSavings {
  return { balance: 0, percentage: 0, groupId: "" };
}

export const GroupSavingsWidget_GroupSavings: MessageFns<GroupSavingsWidget_GroupSavings> = {
  encode(message: GroupSavingsWidget_GroupSavings, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.balance !== 0) {
      writer.uint32(9).double(message.balance);
    }
    if (message.percentage !== 0) {
      writer.uint32(17).double(message.percentage);
    }
    if (message.groupId !== "") {
      writer.uint32(26).string(message.groupId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GroupSavingsWidget_GroupSavings {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGroupSavingsWidget_GroupSavings();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.balance = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.percentage = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.groupId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GroupSavingsWidget_GroupSavings {
    return {
      balance: isSet(object.balance) ? globalThis.Number(object.balance) : 0,
      percentage: isSet(object.percentage) ? globalThis.Number(object.percentage) : 0,
      groupId: isSet(object.groupId) ? globalThis.String(object.groupId) : "",
    };
  },

  toJSON(message: GroupSavingsWidget_GroupSavings): unknown {
    const obj: any = {};
    if (message.balance !== 0) {
      obj.balance = message.balance;
    }
    if (message.percentage !== 0) {
      obj.percentage = message.percentage;
    }
    if (message.groupId !== "") {
      obj.groupId = message.groupId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GroupSavingsWidget_GroupSavings>, I>>(base?: I): GroupSavingsWidget_GroupSavings {
    return GroupSavingsWidget_GroupSavings.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GroupSavingsWidget_GroupSavings>, I>>(
    object: I,
  ): GroupSavingsWidget_GroupSavings {
    const message = createBaseGroupSavingsWidget_GroupSavings();
    message.balance = object.balance ?? 0;
    message.percentage = object.percentage ?? 0;
    message.groupId = object.groupId ?? "";
    return message;
  },
};

function createBaseUserSavingsWidget(): UserSavingsWidget {
  return { totalBalance: 0, savings: [] };
}

export const UserSavingsWidget: MessageFns<UserSavingsWidget> = {
  encode(message: UserSavingsWidget, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.totalBalance !== 0) {
      writer.uint32(9).double(message.totalBalance);
    }
    for (const v of message.savings) {
      UserSavingsWidget_UserSavings.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserSavingsWidget {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserSavingsWidget();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.totalBalance = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.savings.push(UserSavingsWidget_UserSavings.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserSavingsWidget {
    return {
      totalBalance: isSet(object.totalBalance) ? globalThis.Number(object.totalBalance) : 0,
      savings: globalThis.Array.isArray(object?.savings)
        ? object.savings.map((e: any) => UserSavingsWidget_UserSavings.fromJSON(e))
        : [],
    };
  },

  toJSON(message: UserSavingsWidget): unknown {
    const obj: any = {};
    if (message.totalBalance !== 0) {
      obj.totalBalance = message.totalBalance;
    }
    if (message.savings?.length) {
      obj.savings = message.savings.map((e) => UserSavingsWidget_UserSavings.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserSavingsWidget>, I>>(base?: I): UserSavingsWidget {
    return UserSavingsWidget.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserSavingsWidget>, I>>(object: I): UserSavingsWidget {
    const message = createBaseUserSavingsWidget();
    message.totalBalance = object.totalBalance ?? 0;
    message.savings = object.savings?.map((e) => UserSavingsWidget_UserSavings.fromPartial(e)) || [];
    return message;
  },
};

function createBaseUserSavingsWidget_UserSavings(): UserSavingsWidget_UserSavings {
  return { balance: 0, percentage: 0, userId: "" };
}

export const UserSavingsWidget_UserSavings: MessageFns<UserSavingsWidget_UserSavings> = {
  encode(message: UserSavingsWidget_UserSavings, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.balance !== 0) {
      writer.uint32(9).double(message.balance);
    }
    if (message.percentage !== 0) {
      writer.uint32(17).double(message.percentage);
    }
    if (message.userId !== "") {
      writer.uint32(26).string(message.userId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserSavingsWidget_UserSavings {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserSavingsWidget_UserSavings();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.balance = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.percentage = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserSavingsWidget_UserSavings {
    return {
      balance: isSet(object.balance) ? globalThis.Number(object.balance) : 0,
      percentage: isSet(object.percentage) ? globalThis.Number(object.percentage) : 0,
      userId: isSet(object.userId) ? globalThis.String(object.userId) : "",
    };
  },

  toJSON(message: UserSavingsWidget_UserSavings): unknown {
    const obj: any = {};
    if (message.balance !== 0) {
      obj.balance = message.balance;
    }
    if (message.percentage !== 0) {
      obj.percentage = message.percentage;
    }
    if (message.userId !== "") {
      obj.userId = message.userId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserSavingsWidget_UserSavings>, I>>(base?: I): UserSavingsWidget_UserSavings {
    return UserSavingsWidget_UserSavings.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserSavingsWidget_UserSavings>, I>>(
    object: I,
  ): UserSavingsWidget_UserSavings {
    const message = createBaseUserSavingsWidget_UserSavings();
    message.balance = object.balance ?? 0;
    message.percentage = object.percentage ?? 0;
    message.userId = object.userId ?? "";
    return message;
  },
};

function createBaseInvestmentsWorthWidget(): InvestmentsWorthWidget {
  return { totalValue: 0, instrumentValues: [] };
}

export const InvestmentsWorthWidget: MessageFns<InvestmentsWorthWidget> = {
  encode(message: InvestmentsWorthWidget, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.totalValue !== 0) {
      writer.uint32(9).double(message.totalValue);
    }
    for (const v of message.instrumentValues) {
      InvestmentsWorthWidget_InstrumentValue.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InvestmentsWorthWidget {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInvestmentsWorthWidget();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.totalValue = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.instrumentValues.push(InvestmentsWorthWidget_InstrumentValue.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InvestmentsWorthWidget {
    return {
      totalValue: isSet(object.totalValue) ? globalThis.Number(object.totalValue) : 0,
      instrumentValues: globalThis.Array.isArray(object?.instrumentValues)
        ? object.instrumentValues.map((e: any) => InvestmentsWorthWidget_InstrumentValue.fromJSON(e))
        : [],
    };
  },

  toJSON(message: InvestmentsWorthWidget): unknown {
    const obj: any = {};
    if (message.totalValue !== 0) {
      obj.totalValue = message.totalValue;
    }
    if (message.instrumentValues?.length) {
      obj.instrumentValues = message.instrumentValues.map((e) => InvestmentsWorthWidget_InstrumentValue.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InvestmentsWorthWidget>, I>>(base?: I): InvestmentsWorthWidget {
    return InvestmentsWorthWidget.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InvestmentsWorthWidget>, I>>(object: I): InvestmentsWorthWidget {
    const message = createBaseInvestmentsWorthWidget();
    message.totalValue = object.totalValue ?? 0;
    message.instrumentValues =
      object.instrumentValues?.map((e) => InvestmentsWorthWidget_InstrumentValue.fromPartial(e)) || [];
    return message;
  },
};

function createBaseInvestmentsWorthWidget_InstrumentValue(): InvestmentsWorthWidget_InstrumentValue {
  return { value: 0, instrument: 0 };
}

export const InvestmentsWorthWidget_InstrumentValue: MessageFns<InvestmentsWorthWidget_InstrumentValue> = {
  encode(message: InvestmentsWorthWidget_InstrumentValue, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.value !== 0) {
      writer.uint32(9).double(message.value);
    }
    if (message.instrument !== 0) {
      writer.uint32(16).int32(message.instrument);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InvestmentsWorthWidget_InstrumentValue {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInvestmentsWorthWidget_InstrumentValue();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.value = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.instrument = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InvestmentsWorthWidget_InstrumentValue {
    return {
      value: isSet(object.value) ? globalThis.Number(object.value) : 0,
      instrument: isSet(object.instrument) ? fiTypeFromJSON(object.instrument) : 0,
    };
  },

  toJSON(message: InvestmentsWorthWidget_InstrumentValue): unknown {
    const obj: any = {};
    if (message.value !== 0) {
      obj.value = message.value;
    }
    if (message.instrument !== 0) {
      obj.instrument = fiTypeToJSON(message.instrument);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InvestmentsWorthWidget_InstrumentValue>, I>>(
    base?: I,
  ): InvestmentsWorthWidget_InstrumentValue {
    return InvestmentsWorthWidget_InstrumentValue.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InvestmentsWorthWidget_InstrumentValue>, I>>(
    object: I,
  ): InvestmentsWorthWidget_InstrumentValue {
    const message = createBaseInvestmentsWorthWidget_InstrumentValue();
    message.value = object.value ?? 0;
    message.instrument = object.instrument ?? 0;
    return message;
  },
};

function createBaseUserNetWorthWidget(): UserNetWorthWidget {
  return { netWorths: [] };
}

export const UserNetWorthWidget: MessageFns<UserNetWorthWidget> = {
  encode(message: UserNetWorthWidget, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.netWorths) {
      UserNetWorthWidget_UserNetWorth.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserNetWorthWidget {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserNetWorthWidget();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.netWorths.push(UserNetWorthWidget_UserNetWorth.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserNetWorthWidget {
    return {
      netWorths: globalThis.Array.isArray(object?.netWorths)
        ? object.netWorths.map((e: any) => UserNetWorthWidget_UserNetWorth.fromJSON(e))
        : [],
    };
  },

  toJSON(message: UserNetWorthWidget): unknown {
    const obj: any = {};
    if (message.netWorths?.length) {
      obj.netWorths = message.netWorths.map((e) => UserNetWorthWidget_UserNetWorth.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserNetWorthWidget>, I>>(base?: I): UserNetWorthWidget {
    return UserNetWorthWidget.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserNetWorthWidget>, I>>(object: I): UserNetWorthWidget {
    const message = createBaseUserNetWorthWidget();
    message.netWorths = object.netWorths?.map((e) => UserNetWorthWidget_UserNetWorth.fromPartial(e)) || [];
    return message;
  },
};

function createBaseUserNetWorthWidget_UserNetWorth(): UserNetWorthWidget_UserNetWorth {
  return { netWorth: 0, userId: "" };
}

export const UserNetWorthWidget_UserNetWorth: MessageFns<UserNetWorthWidget_UserNetWorth> = {
  encode(message: UserNetWorthWidget_UserNetWorth, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.netWorth !== 0) {
      writer.uint32(9).double(message.netWorth);
    }
    if (message.userId !== "") {
      writer.uint32(18).string(message.userId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserNetWorthWidget_UserNetWorth {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserNetWorthWidget_UserNetWorth();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.netWorth = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserNetWorthWidget_UserNetWorth {
    return {
      netWorth: isSet(object.netWorth) ? globalThis.Number(object.netWorth) : 0,
      userId: isSet(object.userId) ? globalThis.String(object.userId) : "",
    };
  },

  toJSON(message: UserNetWorthWidget_UserNetWorth): unknown {
    const obj: any = {};
    if (message.netWorth !== 0) {
      obj.netWorth = message.netWorth;
    }
    if (message.userId !== "") {
      obj.userId = message.userId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserNetWorthWidget_UserNetWorth>, I>>(base?: I): UserNetWorthWidget_UserNetWorth {
    return UserNetWorthWidget_UserNetWorth.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserNetWorthWidget_UserNetWorth>, I>>(
    object: I,
  ): UserNetWorthWidget_UserNetWorth {
    const message = createBaseUserNetWorthWidget_UserNetWorth();
    message.netWorth = object.netWorth ?? 0;
    message.userId = object.userId ?? "";
    return message;
  },
};

function createBaseGroupNetWorthWidget(): GroupNetWorthWidget {
  return { netWorths: [] };
}

export const GroupNetWorthWidget: MessageFns<GroupNetWorthWidget> = {
  encode(message: GroupNetWorthWidget, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.netWorths) {
      GroupNetWorthWidget_GroupNetWorth.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GroupNetWorthWidget {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGroupNetWorthWidget();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.netWorths.push(GroupNetWorthWidget_GroupNetWorth.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GroupNetWorthWidget {
    return {
      netWorths: globalThis.Array.isArray(object?.netWorths)
        ? object.netWorths.map((e: any) => GroupNetWorthWidget_GroupNetWorth.fromJSON(e))
        : [],
    };
  },

  toJSON(message: GroupNetWorthWidget): unknown {
    const obj: any = {};
    if (message.netWorths?.length) {
      obj.netWorths = message.netWorths.map((e) => GroupNetWorthWidget_GroupNetWorth.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GroupNetWorthWidget>, I>>(base?: I): GroupNetWorthWidget {
    return GroupNetWorthWidget.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GroupNetWorthWidget>, I>>(object: I): GroupNetWorthWidget {
    const message = createBaseGroupNetWorthWidget();
    message.netWorths = object.netWorths?.map((e) => GroupNetWorthWidget_GroupNetWorth.fromPartial(e)) || [];
    return message;
  },
};

function createBaseGroupNetWorthWidget_GroupNetWorth(): GroupNetWorthWidget_GroupNetWorth {
  return { netWorth: 0, groupId: "" };
}

export const GroupNetWorthWidget_GroupNetWorth: MessageFns<GroupNetWorthWidget_GroupNetWorth> = {
  encode(message: GroupNetWorthWidget_GroupNetWorth, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.netWorth !== 0) {
      writer.uint32(9).double(message.netWorth);
    }
    if (message.groupId !== "") {
      writer.uint32(18).string(message.groupId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GroupNetWorthWidget_GroupNetWorth {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGroupNetWorthWidget_GroupNetWorth();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.netWorth = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.groupId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GroupNetWorthWidget_GroupNetWorth {
    return {
      netWorth: isSet(object.netWorth) ? globalThis.Number(object.netWorth) : 0,
      groupId: isSet(object.groupId) ? globalThis.String(object.groupId) : "",
    };
  },

  toJSON(message: GroupNetWorthWidget_GroupNetWorth): unknown {
    const obj: any = {};
    if (message.netWorth !== 0) {
      obj.netWorth = message.netWorth;
    }
    if (message.groupId !== "") {
      obj.groupId = message.groupId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GroupNetWorthWidget_GroupNetWorth>, I>>(
    base?: I,
  ): GroupNetWorthWidget_GroupNetWorth {
    return GroupNetWorthWidget_GroupNetWorth.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GroupNetWorthWidget_GroupNetWorth>, I>>(
    object: I,
  ): GroupNetWorthWidget_GroupNetWorth {
    const message = createBaseGroupNetWorthWidget_GroupNetWorth();
    message.netWorth = object.netWorth ?? 0;
    message.groupId = object.groupId ?? "";
    return message;
  },
};

function createBaseCashflowWidget(): CashflowWidget {
  return { overallCashflows: [], categoryCashflows: [] };
}

export const CashflowWidget: MessageFns<CashflowWidget> = {
  encode(message: CashflowWidget, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.overallCashflows) {
      OverallCashflow.encode(v!, writer.uint32(10).fork()).join();
    }
    for (const v of message.categoryCashflows) {
      CategoryCashflow.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CashflowWidget {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCashflowWidget();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.overallCashflows.push(OverallCashflow.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.categoryCashflows.push(CategoryCashflow.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CashflowWidget {
    return {
      overallCashflows: globalThis.Array.isArray(object?.overallCashflows)
        ? object.overallCashflows.map((e: any) => OverallCashflow.fromJSON(e))
        : [],
      categoryCashflows: globalThis.Array.isArray(object?.categoryCashflows)
        ? object.categoryCashflows.map((e: any) => CategoryCashflow.fromJSON(e))
        : [],
    };
  },

  toJSON(message: CashflowWidget): unknown {
    const obj: any = {};
    if (message.overallCashflows?.length) {
      obj.overallCashflows = message.overallCashflows.map((e) => OverallCashflow.toJSON(e));
    }
    if (message.categoryCashflows?.length) {
      obj.categoryCashflows = message.categoryCashflows.map((e) => CategoryCashflow.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CashflowWidget>, I>>(base?: I): CashflowWidget {
    return CashflowWidget.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CashflowWidget>, I>>(object: I): CashflowWidget {
    const message = createBaseCashflowWidget();
    message.overallCashflows = object.overallCashflows?.map((e) => OverallCashflow.fromPartial(e)) || [];
    message.categoryCashflows = object.categoryCashflows?.map((e) => CategoryCashflow.fromPartial(e)) || [];
    return message;
  },
};

/** Service for Wealth Widgets */
export type WealthService = typeof WealthService;
export const WealthService = {
  fetchWidgets: {
    path: "/backend_services.visualization.Wealth/FetchWidgets",
    requestStream: false,
    responseStream: true,
    requestSerialize: (value: FetchWidgetsRequest) => Buffer.from(FetchWidgetsRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => FetchWidgetsRequest.decode(value),
    responseSerialize: (value: FetchWidgetsResponse) => Buffer.from(FetchWidgetsResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => FetchWidgetsResponse.decode(value),
  },
} as const;

export interface WealthServer extends UntypedServiceImplementation {
  fetchWidgets: handleServerStreamingCall<FetchWidgetsRequest, FetchWidgetsResponse>;
}

export interface WealthClient extends Client {
  fetchWidgets(
    request: FetchWidgetsRequest,
    options?: Partial<CallOptions>,
  ): ClientReadableStream<FetchWidgetsResponse>;
  fetchWidgets(
    request: FetchWidgetsRequest,
    metadata?: Metadata,
    options?: Partial<CallOptions>,
  ): ClientReadableStream<FetchWidgetsResponse>;
}

export const WealthClient = makeGenericClientConstructor(
  WealthService,
  "backend_services.visualization.Wealth",
) as unknown as {
  new (address: string, credentials: ChannelCredentials, options?: Partial<ClientOptions>): WealthClient;
  service: typeof WealthService;
  serviceName: string;
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
