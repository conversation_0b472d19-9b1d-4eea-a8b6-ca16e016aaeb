import React from "react";
import { But<PERSON> } from "./ui/button";

export interface FilterButtonProps {
  onClearClick: () => void;
  onApplyClick: () => void;
  applyDisabled?: boolean;
}

function FilterButtons({
  onClearClick,
  onApplyClick,
  applyDisabled,
}: FilterButtonProps) {
  return (
    <div className="flex justify-end gap-2">
      <Button size="sm" variant="secondary" onClick={onClearClick}>
        Clear
      </Button>
      <Button size="sm" onClick={onApplyClick} disabled={applyDisabled}>
        Apply
      </Button>
    </div>
  );
}

export { FilterButtons };
