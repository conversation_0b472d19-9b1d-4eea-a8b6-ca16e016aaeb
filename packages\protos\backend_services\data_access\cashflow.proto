syntax = "proto3";
package backend_services.data_access;

import "database/fi_deposit.proto";
import "database/cards_filters.proto";

service Cashflow {
    rpc GetTimeIntervalsCashflow(GetTimeIntervalsCashflowRequest) returns (GetTimeIntervalsCashflowResponse) {};
    rpc GetPeriodicCashflow(GetPeriodicCashflowRequest) returns (GetPeriodicCashflowResponse) {};
	rpc GetCategoryCashflow(GetCategoryCashflowRequest) returns (GetCategoryCashflowResponse) {};
    rpc GetSubcategoryCashflow(GetSubcategoryCashflowRequest) returns (GetSubcategoryCashflowResponse) {};
}

message GetTimeIntervalsCashflowRequest {
    database.AccountsFilter account_filters = 1;
    database.TxnFilters txn_filters = 2;
    repeated int64 time_interval_boundaries = 3;
    repeated string investment_category_ids = 4;
}
message GetTimeIntervalsCashflowResponse {
    message TimeIntervalCashflow {
        int64 interval_start_time = 1;
        double investment_amount = 2;
        double outgoing_amount = 3;
        double incoming_amount = 4;
    }
    repeated TimeIntervalCashflow cashflows = 1;
}

message GetPeriodicCashflowRequest {
    database.AccountsFilter account_filters = 1;
    database.TxnFilters txn_filters = 2;
    repeated string investment_category_ids = 3;
}
message GetPeriodicCashflowResponse {
    message PeriodCashflow {
        int64 period_start_time = 1;
        double investment_amount = 2;
        double outgoing_amount = 3;
        double incoming_amount = 4;
    }
    repeated PeriodCashflow cashflows = 1;
}


message GetCategoryCashflowRequest {
    database.AccountsFilter account_filters = 1;
    database.TxnFilters txn_filters = 2;
}
message GetCategoryCashflowResponse {
    message Cashflow {
        string txn_type = 1;
        string category_collection = 2;
        string category_id = 3;
        double amount = 4;
    }
    repeated Cashflow cashflows = 1;
}


message GetSubcategoryCashflowRequest {
    database.AccountsFilter account_filters = 1;
    database.TxnFilters txn_filters = 2;
}
message GetSubcategoryCashflowResponse {
    message SubcategoryCashflow {
        string subcategory_id = 3;
        double amount = 4;
    }
    repeated SubcategoryCashflow cashflows = 1;
}
