// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.2
//   protoc               v6.31.1
// source: backend_services/visualization/deposit_transaction_tags.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import {
  type CallOptions,
  ChannelCredentials,
  Client,
  type ClientOptions,
  type ClientUnaryCall,
  type handleUnaryCall,
  makeGenericClientConstructor,
  Metadata,
  type ServiceError,
  type UntypedServiceImplementation,
} from "@grpc/grpc-js";

export const protobufPackage = "backend_services.visualization";

export interface CreateCategoryRequest {
  categoryName: string;
}

export interface CreateCategoryResponse {
  categoryId: string;
}

export interface RenameCategoryRequest {
  categoryId: string;
  categoryName: string;
}

export interface RenameCategoryResponse {
}

export interface DeleteCategoryRequest {
  categoryId: string;
}

export interface DeleteCategoryResponse {
}

export interface CreateSubcategoryRequest {
  categoryId: string;
  subcategoryName: string;
}

export interface CreateSubcategoryResponse {
  subcategoryId: string;
}

export interface RenameSubcategoryRequest {
  subcategoryId: string;
  subcategoryName: string;
}

export interface RenameSubcategoryResponse {
}

export interface DeleteSubcategoryRequest {
  subcategoryId: string;
}

export interface DeleteSubcategoryResponse {
}

export interface RemoveSubcategoryFromCategoryRequest {
  categoryId: string;
  subcategoryId: string;
}

export interface RemoveSubcategoryFromCategoryResponse {
}

export interface CreateMerchantRequest {
  merchantName: string;
}

export interface CreateMerchantResponse {
  merchantId: string;
}

export interface GetMerchantsRequest {
}

export interface GetMerchantsResponse {
  merchants: GetMerchantsResponse_Merchant[];
}

export interface GetMerchantsResponse_Merchant {
  id: string;
  name: string;
}

export interface DeleteMerchantRequest {
  merchantId: string;
}

export interface DeleteMerchantResponse {
}

export interface SearchCategoryRequest {
  keyword: string;
  txnType: string;
}

export interface SearchCategoryResponse {
  tags: SearchCategoryResponse_Tag[];
}

export interface SearchCategoryResponse_Tag {
  categoryCollection: string;
  categoryId: string;
  subcategoryId: string;
}

export interface SearchMerchantRequest {
  keyword: string;
}

export interface SearchMerchantResponse {
  merchants: SearchMerchantResponse_Merchant[];
}

export interface SearchMerchantResponse_Merchant {
  id: string;
  name: string;
}

function createBaseCreateCategoryRequest(): CreateCategoryRequest {
  return { categoryName: "" };
}

export const CreateCategoryRequest: MessageFns<CreateCategoryRequest> = {
  encode(message: CreateCategoryRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.categoryName !== "") {
      writer.uint32(18).string(message.categoryName);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateCategoryRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateCategoryRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.categoryName = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreateCategoryRequest {
    return { categoryName: isSet(object.categoryName) ? globalThis.String(object.categoryName) : "" };
  },

  toJSON(message: CreateCategoryRequest): unknown {
    const obj: any = {};
    if (message.categoryName !== "") {
      obj.categoryName = message.categoryName;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CreateCategoryRequest>, I>>(base?: I): CreateCategoryRequest {
    return CreateCategoryRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateCategoryRequest>, I>>(object: I): CreateCategoryRequest {
    const message = createBaseCreateCategoryRequest();
    message.categoryName = object.categoryName ?? "";
    return message;
  },
};

function createBaseCreateCategoryResponse(): CreateCategoryResponse {
  return { categoryId: "" };
}

export const CreateCategoryResponse: MessageFns<CreateCategoryResponse> = {
  encode(message: CreateCategoryResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.categoryId !== "") {
      writer.uint32(10).string(message.categoryId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateCategoryResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateCategoryResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.categoryId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreateCategoryResponse {
    return { categoryId: isSet(object.categoryId) ? globalThis.String(object.categoryId) : "" };
  },

  toJSON(message: CreateCategoryResponse): unknown {
    const obj: any = {};
    if (message.categoryId !== "") {
      obj.categoryId = message.categoryId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CreateCategoryResponse>, I>>(base?: I): CreateCategoryResponse {
    return CreateCategoryResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateCategoryResponse>, I>>(object: I): CreateCategoryResponse {
    const message = createBaseCreateCategoryResponse();
    message.categoryId = object.categoryId ?? "";
    return message;
  },
};

function createBaseRenameCategoryRequest(): RenameCategoryRequest {
  return { categoryId: "", categoryName: "" };
}

export const RenameCategoryRequest: MessageFns<RenameCategoryRequest> = {
  encode(message: RenameCategoryRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.categoryId !== "") {
      writer.uint32(10).string(message.categoryId);
    }
    if (message.categoryName !== "") {
      writer.uint32(18).string(message.categoryName);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RenameCategoryRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRenameCategoryRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.categoryId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.categoryName = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RenameCategoryRequest {
    return {
      categoryId: isSet(object.categoryId) ? globalThis.String(object.categoryId) : "",
      categoryName: isSet(object.categoryName) ? globalThis.String(object.categoryName) : "",
    };
  },

  toJSON(message: RenameCategoryRequest): unknown {
    const obj: any = {};
    if (message.categoryId !== "") {
      obj.categoryId = message.categoryId;
    }
    if (message.categoryName !== "") {
      obj.categoryName = message.categoryName;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RenameCategoryRequest>, I>>(base?: I): RenameCategoryRequest {
    return RenameCategoryRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RenameCategoryRequest>, I>>(object: I): RenameCategoryRequest {
    const message = createBaseRenameCategoryRequest();
    message.categoryId = object.categoryId ?? "";
    message.categoryName = object.categoryName ?? "";
    return message;
  },
};

function createBaseRenameCategoryResponse(): RenameCategoryResponse {
  return {};
}

export const RenameCategoryResponse: MessageFns<RenameCategoryResponse> = {
  encode(_: RenameCategoryResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RenameCategoryResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRenameCategoryResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): RenameCategoryResponse {
    return {};
  },

  toJSON(_: RenameCategoryResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<RenameCategoryResponse>, I>>(base?: I): RenameCategoryResponse {
    return RenameCategoryResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RenameCategoryResponse>, I>>(_: I): RenameCategoryResponse {
    const message = createBaseRenameCategoryResponse();
    return message;
  },
};

function createBaseDeleteCategoryRequest(): DeleteCategoryRequest {
  return { categoryId: "" };
}

export const DeleteCategoryRequest: MessageFns<DeleteCategoryRequest> = {
  encode(message: DeleteCategoryRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.categoryId !== "") {
      writer.uint32(10).string(message.categoryId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteCategoryRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteCategoryRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.categoryId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DeleteCategoryRequest {
    return { categoryId: isSet(object.categoryId) ? globalThis.String(object.categoryId) : "" };
  },

  toJSON(message: DeleteCategoryRequest): unknown {
    const obj: any = {};
    if (message.categoryId !== "") {
      obj.categoryId = message.categoryId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DeleteCategoryRequest>, I>>(base?: I): DeleteCategoryRequest {
    return DeleteCategoryRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteCategoryRequest>, I>>(object: I): DeleteCategoryRequest {
    const message = createBaseDeleteCategoryRequest();
    message.categoryId = object.categoryId ?? "";
    return message;
  },
};

function createBaseDeleteCategoryResponse(): DeleteCategoryResponse {
  return {};
}

export const DeleteCategoryResponse: MessageFns<DeleteCategoryResponse> = {
  encode(_: DeleteCategoryResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteCategoryResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteCategoryResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): DeleteCategoryResponse {
    return {};
  },

  toJSON(_: DeleteCategoryResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<DeleteCategoryResponse>, I>>(base?: I): DeleteCategoryResponse {
    return DeleteCategoryResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteCategoryResponse>, I>>(_: I): DeleteCategoryResponse {
    const message = createBaseDeleteCategoryResponse();
    return message;
  },
};

function createBaseCreateSubcategoryRequest(): CreateSubcategoryRequest {
  return { categoryId: "", subcategoryName: "" };
}

export const CreateSubcategoryRequest: MessageFns<CreateSubcategoryRequest> = {
  encode(message: CreateSubcategoryRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.categoryId !== "") {
      writer.uint32(10).string(message.categoryId);
    }
    if (message.subcategoryName !== "") {
      writer.uint32(18).string(message.subcategoryName);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateSubcategoryRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateSubcategoryRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.categoryId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.subcategoryName = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreateSubcategoryRequest {
    return {
      categoryId: isSet(object.categoryId) ? globalThis.String(object.categoryId) : "",
      subcategoryName: isSet(object.subcategoryName) ? globalThis.String(object.subcategoryName) : "",
    };
  },

  toJSON(message: CreateSubcategoryRequest): unknown {
    const obj: any = {};
    if (message.categoryId !== "") {
      obj.categoryId = message.categoryId;
    }
    if (message.subcategoryName !== "") {
      obj.subcategoryName = message.subcategoryName;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CreateSubcategoryRequest>, I>>(base?: I): CreateSubcategoryRequest {
    return CreateSubcategoryRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateSubcategoryRequest>, I>>(object: I): CreateSubcategoryRequest {
    const message = createBaseCreateSubcategoryRequest();
    message.categoryId = object.categoryId ?? "";
    message.subcategoryName = object.subcategoryName ?? "";
    return message;
  },
};

function createBaseCreateSubcategoryResponse(): CreateSubcategoryResponse {
  return { subcategoryId: "" };
}

export const CreateSubcategoryResponse: MessageFns<CreateSubcategoryResponse> = {
  encode(message: CreateSubcategoryResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.subcategoryId !== "") {
      writer.uint32(10).string(message.subcategoryId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateSubcategoryResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateSubcategoryResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.subcategoryId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreateSubcategoryResponse {
    return { subcategoryId: isSet(object.subcategoryId) ? globalThis.String(object.subcategoryId) : "" };
  },

  toJSON(message: CreateSubcategoryResponse): unknown {
    const obj: any = {};
    if (message.subcategoryId !== "") {
      obj.subcategoryId = message.subcategoryId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CreateSubcategoryResponse>, I>>(base?: I): CreateSubcategoryResponse {
    return CreateSubcategoryResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateSubcategoryResponse>, I>>(object: I): CreateSubcategoryResponse {
    const message = createBaseCreateSubcategoryResponse();
    message.subcategoryId = object.subcategoryId ?? "";
    return message;
  },
};

function createBaseRenameSubcategoryRequest(): RenameSubcategoryRequest {
  return { subcategoryId: "", subcategoryName: "" };
}

export const RenameSubcategoryRequest: MessageFns<RenameSubcategoryRequest> = {
  encode(message: RenameSubcategoryRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.subcategoryId !== "") {
      writer.uint32(10).string(message.subcategoryId);
    }
    if (message.subcategoryName !== "") {
      writer.uint32(18).string(message.subcategoryName);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RenameSubcategoryRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRenameSubcategoryRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.subcategoryId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.subcategoryName = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RenameSubcategoryRequest {
    return {
      subcategoryId: isSet(object.subcategoryId) ? globalThis.String(object.subcategoryId) : "",
      subcategoryName: isSet(object.subcategoryName) ? globalThis.String(object.subcategoryName) : "",
    };
  },

  toJSON(message: RenameSubcategoryRequest): unknown {
    const obj: any = {};
    if (message.subcategoryId !== "") {
      obj.subcategoryId = message.subcategoryId;
    }
    if (message.subcategoryName !== "") {
      obj.subcategoryName = message.subcategoryName;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RenameSubcategoryRequest>, I>>(base?: I): RenameSubcategoryRequest {
    return RenameSubcategoryRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RenameSubcategoryRequest>, I>>(object: I): RenameSubcategoryRequest {
    const message = createBaseRenameSubcategoryRequest();
    message.subcategoryId = object.subcategoryId ?? "";
    message.subcategoryName = object.subcategoryName ?? "";
    return message;
  },
};

function createBaseRenameSubcategoryResponse(): RenameSubcategoryResponse {
  return {};
}

export const RenameSubcategoryResponse: MessageFns<RenameSubcategoryResponse> = {
  encode(_: RenameSubcategoryResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RenameSubcategoryResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRenameSubcategoryResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): RenameSubcategoryResponse {
    return {};
  },

  toJSON(_: RenameSubcategoryResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<RenameSubcategoryResponse>, I>>(base?: I): RenameSubcategoryResponse {
    return RenameSubcategoryResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RenameSubcategoryResponse>, I>>(_: I): RenameSubcategoryResponse {
    const message = createBaseRenameSubcategoryResponse();
    return message;
  },
};

function createBaseDeleteSubcategoryRequest(): DeleteSubcategoryRequest {
  return { subcategoryId: "" };
}

export const DeleteSubcategoryRequest: MessageFns<DeleteSubcategoryRequest> = {
  encode(message: DeleteSubcategoryRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.subcategoryId !== "") {
      writer.uint32(10).string(message.subcategoryId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteSubcategoryRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteSubcategoryRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.subcategoryId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DeleteSubcategoryRequest {
    return { subcategoryId: isSet(object.subcategoryId) ? globalThis.String(object.subcategoryId) : "" };
  },

  toJSON(message: DeleteSubcategoryRequest): unknown {
    const obj: any = {};
    if (message.subcategoryId !== "") {
      obj.subcategoryId = message.subcategoryId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DeleteSubcategoryRequest>, I>>(base?: I): DeleteSubcategoryRequest {
    return DeleteSubcategoryRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteSubcategoryRequest>, I>>(object: I): DeleteSubcategoryRequest {
    const message = createBaseDeleteSubcategoryRequest();
    message.subcategoryId = object.subcategoryId ?? "";
    return message;
  },
};

function createBaseDeleteSubcategoryResponse(): DeleteSubcategoryResponse {
  return {};
}

export const DeleteSubcategoryResponse: MessageFns<DeleteSubcategoryResponse> = {
  encode(_: DeleteSubcategoryResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteSubcategoryResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteSubcategoryResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): DeleteSubcategoryResponse {
    return {};
  },

  toJSON(_: DeleteSubcategoryResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<DeleteSubcategoryResponse>, I>>(base?: I): DeleteSubcategoryResponse {
    return DeleteSubcategoryResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteSubcategoryResponse>, I>>(_: I): DeleteSubcategoryResponse {
    const message = createBaseDeleteSubcategoryResponse();
    return message;
  },
};

function createBaseRemoveSubcategoryFromCategoryRequest(): RemoveSubcategoryFromCategoryRequest {
  return { categoryId: "", subcategoryId: "" };
}

export const RemoveSubcategoryFromCategoryRequest: MessageFns<RemoveSubcategoryFromCategoryRequest> = {
  encode(message: RemoveSubcategoryFromCategoryRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.categoryId !== "") {
      writer.uint32(10).string(message.categoryId);
    }
    if (message.subcategoryId !== "") {
      writer.uint32(18).string(message.subcategoryId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RemoveSubcategoryFromCategoryRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRemoveSubcategoryFromCategoryRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.categoryId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.subcategoryId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RemoveSubcategoryFromCategoryRequest {
    return {
      categoryId: isSet(object.categoryId) ? globalThis.String(object.categoryId) : "",
      subcategoryId: isSet(object.subcategoryId) ? globalThis.String(object.subcategoryId) : "",
    };
  },

  toJSON(message: RemoveSubcategoryFromCategoryRequest): unknown {
    const obj: any = {};
    if (message.categoryId !== "") {
      obj.categoryId = message.categoryId;
    }
    if (message.subcategoryId !== "") {
      obj.subcategoryId = message.subcategoryId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RemoveSubcategoryFromCategoryRequest>, I>>(
    base?: I,
  ): RemoveSubcategoryFromCategoryRequest {
    return RemoveSubcategoryFromCategoryRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RemoveSubcategoryFromCategoryRequest>, I>>(
    object: I,
  ): RemoveSubcategoryFromCategoryRequest {
    const message = createBaseRemoveSubcategoryFromCategoryRequest();
    message.categoryId = object.categoryId ?? "";
    message.subcategoryId = object.subcategoryId ?? "";
    return message;
  },
};

function createBaseRemoveSubcategoryFromCategoryResponse(): RemoveSubcategoryFromCategoryResponse {
  return {};
}

export const RemoveSubcategoryFromCategoryResponse: MessageFns<RemoveSubcategoryFromCategoryResponse> = {
  encode(_: RemoveSubcategoryFromCategoryResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RemoveSubcategoryFromCategoryResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRemoveSubcategoryFromCategoryResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): RemoveSubcategoryFromCategoryResponse {
    return {};
  },

  toJSON(_: RemoveSubcategoryFromCategoryResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<RemoveSubcategoryFromCategoryResponse>, I>>(
    base?: I,
  ): RemoveSubcategoryFromCategoryResponse {
    return RemoveSubcategoryFromCategoryResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RemoveSubcategoryFromCategoryResponse>, I>>(
    _: I,
  ): RemoveSubcategoryFromCategoryResponse {
    const message = createBaseRemoveSubcategoryFromCategoryResponse();
    return message;
  },
};

function createBaseCreateMerchantRequest(): CreateMerchantRequest {
  return { merchantName: "" };
}

export const CreateMerchantRequest: MessageFns<CreateMerchantRequest> = {
  encode(message: CreateMerchantRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.merchantName !== "") {
      writer.uint32(10).string(message.merchantName);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateMerchantRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateMerchantRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.merchantName = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreateMerchantRequest {
    return { merchantName: isSet(object.merchantName) ? globalThis.String(object.merchantName) : "" };
  },

  toJSON(message: CreateMerchantRequest): unknown {
    const obj: any = {};
    if (message.merchantName !== "") {
      obj.merchantName = message.merchantName;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CreateMerchantRequest>, I>>(base?: I): CreateMerchantRequest {
    return CreateMerchantRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateMerchantRequest>, I>>(object: I): CreateMerchantRequest {
    const message = createBaseCreateMerchantRequest();
    message.merchantName = object.merchantName ?? "";
    return message;
  },
};

function createBaseCreateMerchantResponse(): CreateMerchantResponse {
  return { merchantId: "" };
}

export const CreateMerchantResponse: MessageFns<CreateMerchantResponse> = {
  encode(message: CreateMerchantResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.merchantId !== "") {
      writer.uint32(10).string(message.merchantId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateMerchantResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateMerchantResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.merchantId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreateMerchantResponse {
    return { merchantId: isSet(object.merchantId) ? globalThis.String(object.merchantId) : "" };
  },

  toJSON(message: CreateMerchantResponse): unknown {
    const obj: any = {};
    if (message.merchantId !== "") {
      obj.merchantId = message.merchantId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CreateMerchantResponse>, I>>(base?: I): CreateMerchantResponse {
    return CreateMerchantResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateMerchantResponse>, I>>(object: I): CreateMerchantResponse {
    const message = createBaseCreateMerchantResponse();
    message.merchantId = object.merchantId ?? "";
    return message;
  },
};

function createBaseGetMerchantsRequest(): GetMerchantsRequest {
  return {};
}

export const GetMerchantsRequest: MessageFns<GetMerchantsRequest> = {
  encode(_: GetMerchantsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetMerchantsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetMerchantsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): GetMerchantsRequest {
    return {};
  },

  toJSON(_: GetMerchantsRequest): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<GetMerchantsRequest>, I>>(base?: I): GetMerchantsRequest {
    return GetMerchantsRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetMerchantsRequest>, I>>(_: I): GetMerchantsRequest {
    const message = createBaseGetMerchantsRequest();
    return message;
  },
};

function createBaseGetMerchantsResponse(): GetMerchantsResponse {
  return { merchants: [] };
}

export const GetMerchantsResponse: MessageFns<GetMerchantsResponse> = {
  encode(message: GetMerchantsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.merchants) {
      GetMerchantsResponse_Merchant.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetMerchantsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetMerchantsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.merchants.push(GetMerchantsResponse_Merchant.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetMerchantsResponse {
    return {
      merchants: globalThis.Array.isArray(object?.merchants)
        ? object.merchants.map((e: any) => GetMerchantsResponse_Merchant.fromJSON(e))
        : [],
    };
  },

  toJSON(message: GetMerchantsResponse): unknown {
    const obj: any = {};
    if (message.merchants?.length) {
      obj.merchants = message.merchants.map((e) => GetMerchantsResponse_Merchant.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetMerchantsResponse>, I>>(base?: I): GetMerchantsResponse {
    return GetMerchantsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetMerchantsResponse>, I>>(object: I): GetMerchantsResponse {
    const message = createBaseGetMerchantsResponse();
    message.merchants = object.merchants?.map((e) => GetMerchantsResponse_Merchant.fromPartial(e)) || [];
    return message;
  },
};

function createBaseGetMerchantsResponse_Merchant(): GetMerchantsResponse_Merchant {
  return { id: "", name: "" };
}

export const GetMerchantsResponse_Merchant: MessageFns<GetMerchantsResponse_Merchant> = {
  encode(message: GetMerchantsResponse_Merchant, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetMerchantsResponse_Merchant {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetMerchantsResponse_Merchant();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetMerchantsResponse_Merchant {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
    };
  },

  toJSON(message: GetMerchantsResponse_Merchant): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetMerchantsResponse_Merchant>, I>>(base?: I): GetMerchantsResponse_Merchant {
    return GetMerchantsResponse_Merchant.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetMerchantsResponse_Merchant>, I>>(
    object: I,
  ): GetMerchantsResponse_Merchant {
    const message = createBaseGetMerchantsResponse_Merchant();
    message.id = object.id ?? "";
    message.name = object.name ?? "";
    return message;
  },
};

function createBaseDeleteMerchantRequest(): DeleteMerchantRequest {
  return { merchantId: "" };
}

export const DeleteMerchantRequest: MessageFns<DeleteMerchantRequest> = {
  encode(message: DeleteMerchantRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.merchantId !== "") {
      writer.uint32(10).string(message.merchantId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteMerchantRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteMerchantRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.merchantId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DeleteMerchantRequest {
    return { merchantId: isSet(object.merchantId) ? globalThis.String(object.merchantId) : "" };
  },

  toJSON(message: DeleteMerchantRequest): unknown {
    const obj: any = {};
    if (message.merchantId !== "") {
      obj.merchantId = message.merchantId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DeleteMerchantRequest>, I>>(base?: I): DeleteMerchantRequest {
    return DeleteMerchantRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteMerchantRequest>, I>>(object: I): DeleteMerchantRequest {
    const message = createBaseDeleteMerchantRequest();
    message.merchantId = object.merchantId ?? "";
    return message;
  },
};

function createBaseDeleteMerchantResponse(): DeleteMerchantResponse {
  return {};
}

export const DeleteMerchantResponse: MessageFns<DeleteMerchantResponse> = {
  encode(_: DeleteMerchantResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteMerchantResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteMerchantResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): DeleteMerchantResponse {
    return {};
  },

  toJSON(_: DeleteMerchantResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<DeleteMerchantResponse>, I>>(base?: I): DeleteMerchantResponse {
    return DeleteMerchantResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteMerchantResponse>, I>>(_: I): DeleteMerchantResponse {
    const message = createBaseDeleteMerchantResponse();
    return message;
  },
};

function createBaseSearchCategoryRequest(): SearchCategoryRequest {
  return { keyword: "", txnType: "" };
}

export const SearchCategoryRequest: MessageFns<SearchCategoryRequest> = {
  encode(message: SearchCategoryRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.keyword !== "") {
      writer.uint32(10).string(message.keyword);
    }
    if (message.txnType !== "") {
      writer.uint32(18).string(message.txnType);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SearchCategoryRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSearchCategoryRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.keyword = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.txnType = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SearchCategoryRequest {
    return {
      keyword: isSet(object.keyword) ? globalThis.String(object.keyword) : "",
      txnType: isSet(object.txnType) ? globalThis.String(object.txnType) : "",
    };
  },

  toJSON(message: SearchCategoryRequest): unknown {
    const obj: any = {};
    if (message.keyword !== "") {
      obj.keyword = message.keyword;
    }
    if (message.txnType !== "") {
      obj.txnType = message.txnType;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SearchCategoryRequest>, I>>(base?: I): SearchCategoryRequest {
    return SearchCategoryRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchCategoryRequest>, I>>(object: I): SearchCategoryRequest {
    const message = createBaseSearchCategoryRequest();
    message.keyword = object.keyword ?? "";
    message.txnType = object.txnType ?? "";
    return message;
  },
};

function createBaseSearchCategoryResponse(): SearchCategoryResponse {
  return { tags: [] };
}

export const SearchCategoryResponse: MessageFns<SearchCategoryResponse> = {
  encode(message: SearchCategoryResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.tags) {
      SearchCategoryResponse_Tag.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SearchCategoryResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSearchCategoryResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.tags.push(SearchCategoryResponse_Tag.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SearchCategoryResponse {
    return {
      tags: globalThis.Array.isArray(object?.tags)
        ? object.tags.map((e: any) => SearchCategoryResponse_Tag.fromJSON(e))
        : [],
    };
  },

  toJSON(message: SearchCategoryResponse): unknown {
    const obj: any = {};
    if (message.tags?.length) {
      obj.tags = message.tags.map((e) => SearchCategoryResponse_Tag.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SearchCategoryResponse>, I>>(base?: I): SearchCategoryResponse {
    return SearchCategoryResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchCategoryResponse>, I>>(object: I): SearchCategoryResponse {
    const message = createBaseSearchCategoryResponse();
    message.tags = object.tags?.map((e) => SearchCategoryResponse_Tag.fromPartial(e)) || [];
    return message;
  },
};

function createBaseSearchCategoryResponse_Tag(): SearchCategoryResponse_Tag {
  return { categoryCollection: "", categoryId: "", subcategoryId: "" };
}

export const SearchCategoryResponse_Tag: MessageFns<SearchCategoryResponse_Tag> = {
  encode(message: SearchCategoryResponse_Tag, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.categoryCollection !== "") {
      writer.uint32(10).string(message.categoryCollection);
    }
    if (message.categoryId !== "") {
      writer.uint32(18).string(message.categoryId);
    }
    if (message.subcategoryId !== "") {
      writer.uint32(26).string(message.subcategoryId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SearchCategoryResponse_Tag {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSearchCategoryResponse_Tag();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.categoryCollection = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.categoryId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.subcategoryId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SearchCategoryResponse_Tag {
    return {
      categoryCollection: isSet(object.categoryCollection) ? globalThis.String(object.categoryCollection) : "",
      categoryId: isSet(object.categoryId) ? globalThis.String(object.categoryId) : "",
      subcategoryId: isSet(object.subcategoryId) ? globalThis.String(object.subcategoryId) : "",
    };
  },

  toJSON(message: SearchCategoryResponse_Tag): unknown {
    const obj: any = {};
    if (message.categoryCollection !== "") {
      obj.categoryCollection = message.categoryCollection;
    }
    if (message.categoryId !== "") {
      obj.categoryId = message.categoryId;
    }
    if (message.subcategoryId !== "") {
      obj.subcategoryId = message.subcategoryId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SearchCategoryResponse_Tag>, I>>(base?: I): SearchCategoryResponse_Tag {
    return SearchCategoryResponse_Tag.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchCategoryResponse_Tag>, I>>(object: I): SearchCategoryResponse_Tag {
    const message = createBaseSearchCategoryResponse_Tag();
    message.categoryCollection = object.categoryCollection ?? "";
    message.categoryId = object.categoryId ?? "";
    message.subcategoryId = object.subcategoryId ?? "";
    return message;
  },
};

function createBaseSearchMerchantRequest(): SearchMerchantRequest {
  return { keyword: "" };
}

export const SearchMerchantRequest: MessageFns<SearchMerchantRequest> = {
  encode(message: SearchMerchantRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.keyword !== "") {
      writer.uint32(10).string(message.keyword);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SearchMerchantRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSearchMerchantRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.keyword = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SearchMerchantRequest {
    return { keyword: isSet(object.keyword) ? globalThis.String(object.keyword) : "" };
  },

  toJSON(message: SearchMerchantRequest): unknown {
    const obj: any = {};
    if (message.keyword !== "") {
      obj.keyword = message.keyword;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SearchMerchantRequest>, I>>(base?: I): SearchMerchantRequest {
    return SearchMerchantRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchMerchantRequest>, I>>(object: I): SearchMerchantRequest {
    const message = createBaseSearchMerchantRequest();
    message.keyword = object.keyword ?? "";
    return message;
  },
};

function createBaseSearchMerchantResponse(): SearchMerchantResponse {
  return { merchants: [] };
}

export const SearchMerchantResponse: MessageFns<SearchMerchantResponse> = {
  encode(message: SearchMerchantResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.merchants) {
      SearchMerchantResponse_Merchant.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SearchMerchantResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSearchMerchantResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.merchants.push(SearchMerchantResponse_Merchant.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SearchMerchantResponse {
    return {
      merchants: globalThis.Array.isArray(object?.merchants)
        ? object.merchants.map((e: any) => SearchMerchantResponse_Merchant.fromJSON(e))
        : [],
    };
  },

  toJSON(message: SearchMerchantResponse): unknown {
    const obj: any = {};
    if (message.merchants?.length) {
      obj.merchants = message.merchants.map((e) => SearchMerchantResponse_Merchant.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SearchMerchantResponse>, I>>(base?: I): SearchMerchantResponse {
    return SearchMerchantResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchMerchantResponse>, I>>(object: I): SearchMerchantResponse {
    const message = createBaseSearchMerchantResponse();
    message.merchants = object.merchants?.map((e) => SearchMerchantResponse_Merchant.fromPartial(e)) || [];
    return message;
  },
};

function createBaseSearchMerchantResponse_Merchant(): SearchMerchantResponse_Merchant {
  return { id: "", name: "" };
}

export const SearchMerchantResponse_Merchant: MessageFns<SearchMerchantResponse_Merchant> = {
  encode(message: SearchMerchantResponse_Merchant, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SearchMerchantResponse_Merchant {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSearchMerchantResponse_Merchant();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SearchMerchantResponse_Merchant {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
    };
  },

  toJSON(message: SearchMerchantResponse_Merchant): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SearchMerchantResponse_Merchant>, I>>(base?: I): SearchMerchantResponse_Merchant {
    return SearchMerchantResponse_Merchant.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchMerchantResponse_Merchant>, I>>(
    object: I,
  ): SearchMerchantResponse_Merchant {
    const message = createBaseSearchMerchantResponse_Merchant();
    message.id = object.id ?? "";
    message.name = object.name ?? "";
    return message;
  },
};

/** Service for Tags */
export type DepositTransactionTagsService = typeof DepositTransactionTagsService;
export const DepositTransactionTagsService = {
  createCategory: {
    path: "/backend_services.visualization.DepositTransactionTags/CreateCategory",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: CreateCategoryRequest) => Buffer.from(CreateCategoryRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => CreateCategoryRequest.decode(value),
    responseSerialize: (value: CreateCategoryResponse) => Buffer.from(CreateCategoryResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => CreateCategoryResponse.decode(value),
  },
  renameCategory: {
    path: "/backend_services.visualization.DepositTransactionTags/RenameCategory",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: RenameCategoryRequest) => Buffer.from(RenameCategoryRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => RenameCategoryRequest.decode(value),
    responseSerialize: (value: RenameCategoryResponse) => Buffer.from(RenameCategoryResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => RenameCategoryResponse.decode(value),
  },
  deleteCategory: {
    path: "/backend_services.visualization.DepositTransactionTags/DeleteCategory",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: DeleteCategoryRequest) => Buffer.from(DeleteCategoryRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => DeleteCategoryRequest.decode(value),
    responseSerialize: (value: DeleteCategoryResponse) => Buffer.from(DeleteCategoryResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => DeleteCategoryResponse.decode(value),
  },
  createSubcategory: {
    path: "/backend_services.visualization.DepositTransactionTags/CreateSubcategory",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: CreateSubcategoryRequest) => Buffer.from(CreateSubcategoryRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => CreateSubcategoryRequest.decode(value),
    responseSerialize: (value: CreateSubcategoryResponse) =>
      Buffer.from(CreateSubcategoryResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => CreateSubcategoryResponse.decode(value),
  },
  renameSubcategory: {
    path: "/backend_services.visualization.DepositTransactionTags/RenameSubcategory",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: RenameSubcategoryRequest) => Buffer.from(RenameSubcategoryRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => RenameSubcategoryRequest.decode(value),
    responseSerialize: (value: RenameSubcategoryResponse) =>
      Buffer.from(RenameSubcategoryResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => RenameSubcategoryResponse.decode(value),
  },
  deleteSubcategory: {
    path: "/backend_services.visualization.DepositTransactionTags/DeleteSubcategory",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: DeleteSubcategoryRequest) => Buffer.from(DeleteSubcategoryRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => DeleteSubcategoryRequest.decode(value),
    responseSerialize: (value: DeleteSubcategoryResponse) =>
      Buffer.from(DeleteSubcategoryResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => DeleteSubcategoryResponse.decode(value),
  },
  removeSubcategoryFromCategory: {
    path: "/backend_services.visualization.DepositTransactionTags/RemoveSubcategoryFromCategory",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: RemoveSubcategoryFromCategoryRequest) =>
      Buffer.from(RemoveSubcategoryFromCategoryRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => RemoveSubcategoryFromCategoryRequest.decode(value),
    responseSerialize: (value: RemoveSubcategoryFromCategoryResponse) =>
      Buffer.from(RemoveSubcategoryFromCategoryResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => RemoveSubcategoryFromCategoryResponse.decode(value),
  },
  createMerchant: {
    path: "/backend_services.visualization.DepositTransactionTags/CreateMerchant",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: CreateMerchantRequest) => Buffer.from(CreateMerchantRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => CreateMerchantRequest.decode(value),
    responseSerialize: (value: CreateMerchantResponse) => Buffer.from(CreateMerchantResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => CreateMerchantResponse.decode(value),
  },
  getMerchants: {
    path: "/backend_services.visualization.DepositTransactionTags/GetMerchants",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: GetMerchantsRequest) => Buffer.from(GetMerchantsRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => GetMerchantsRequest.decode(value),
    responseSerialize: (value: GetMerchantsResponse) => Buffer.from(GetMerchantsResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => GetMerchantsResponse.decode(value),
  },
  deleteMerchant: {
    path: "/backend_services.visualization.DepositTransactionTags/DeleteMerchant",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: DeleteMerchantRequest) => Buffer.from(DeleteMerchantRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => DeleteMerchantRequest.decode(value),
    responseSerialize: (value: DeleteMerchantResponse) => Buffer.from(DeleteMerchantResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => DeleteMerchantResponse.decode(value),
  },
  searchCategory: {
    path: "/backend_services.visualization.DepositTransactionTags/SearchCategory",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: SearchCategoryRequest) => Buffer.from(SearchCategoryRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => SearchCategoryRequest.decode(value),
    responseSerialize: (value: SearchCategoryResponse) => Buffer.from(SearchCategoryResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => SearchCategoryResponse.decode(value),
  },
  searchMerchant: {
    path: "/backend_services.visualization.DepositTransactionTags/SearchMerchant",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: SearchMerchantRequest) => Buffer.from(SearchMerchantRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => SearchMerchantRequest.decode(value),
    responseSerialize: (value: SearchMerchantResponse) => Buffer.from(SearchMerchantResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => SearchMerchantResponse.decode(value),
  },
} as const;

export interface DepositTransactionTagsServer extends UntypedServiceImplementation {
  createCategory: handleUnaryCall<CreateCategoryRequest, CreateCategoryResponse>;
  renameCategory: handleUnaryCall<RenameCategoryRequest, RenameCategoryResponse>;
  deleteCategory: handleUnaryCall<DeleteCategoryRequest, DeleteCategoryResponse>;
  createSubcategory: handleUnaryCall<CreateSubcategoryRequest, CreateSubcategoryResponse>;
  renameSubcategory: handleUnaryCall<RenameSubcategoryRequest, RenameSubcategoryResponse>;
  deleteSubcategory: handleUnaryCall<DeleteSubcategoryRequest, DeleteSubcategoryResponse>;
  removeSubcategoryFromCategory: handleUnaryCall<
    RemoveSubcategoryFromCategoryRequest,
    RemoveSubcategoryFromCategoryResponse
  >;
  createMerchant: handleUnaryCall<CreateMerchantRequest, CreateMerchantResponse>;
  getMerchants: handleUnaryCall<GetMerchantsRequest, GetMerchantsResponse>;
  deleteMerchant: handleUnaryCall<DeleteMerchantRequest, DeleteMerchantResponse>;
  searchCategory: handleUnaryCall<SearchCategoryRequest, SearchCategoryResponse>;
  searchMerchant: handleUnaryCall<SearchMerchantRequest, SearchMerchantResponse>;
}

export interface DepositTransactionTagsClient extends Client {
  createCategory(
    request: CreateCategoryRequest,
    callback: (error: ServiceError | null, response: CreateCategoryResponse) => void,
  ): ClientUnaryCall;
  createCategory(
    request: CreateCategoryRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: CreateCategoryResponse) => void,
  ): ClientUnaryCall;
  createCategory(
    request: CreateCategoryRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: CreateCategoryResponse) => void,
  ): ClientUnaryCall;
  renameCategory(
    request: RenameCategoryRequest,
    callback: (error: ServiceError | null, response: RenameCategoryResponse) => void,
  ): ClientUnaryCall;
  renameCategory(
    request: RenameCategoryRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: RenameCategoryResponse) => void,
  ): ClientUnaryCall;
  renameCategory(
    request: RenameCategoryRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: RenameCategoryResponse) => void,
  ): ClientUnaryCall;
  deleteCategory(
    request: DeleteCategoryRequest,
    callback: (error: ServiceError | null, response: DeleteCategoryResponse) => void,
  ): ClientUnaryCall;
  deleteCategory(
    request: DeleteCategoryRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: DeleteCategoryResponse) => void,
  ): ClientUnaryCall;
  deleteCategory(
    request: DeleteCategoryRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: DeleteCategoryResponse) => void,
  ): ClientUnaryCall;
  createSubcategory(
    request: CreateSubcategoryRequest,
    callback: (error: ServiceError | null, response: CreateSubcategoryResponse) => void,
  ): ClientUnaryCall;
  createSubcategory(
    request: CreateSubcategoryRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: CreateSubcategoryResponse) => void,
  ): ClientUnaryCall;
  createSubcategory(
    request: CreateSubcategoryRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: CreateSubcategoryResponse) => void,
  ): ClientUnaryCall;
  renameSubcategory(
    request: RenameSubcategoryRequest,
    callback: (error: ServiceError | null, response: RenameSubcategoryResponse) => void,
  ): ClientUnaryCall;
  renameSubcategory(
    request: RenameSubcategoryRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: RenameSubcategoryResponse) => void,
  ): ClientUnaryCall;
  renameSubcategory(
    request: RenameSubcategoryRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: RenameSubcategoryResponse) => void,
  ): ClientUnaryCall;
  deleteSubcategory(
    request: DeleteSubcategoryRequest,
    callback: (error: ServiceError | null, response: DeleteSubcategoryResponse) => void,
  ): ClientUnaryCall;
  deleteSubcategory(
    request: DeleteSubcategoryRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: DeleteSubcategoryResponse) => void,
  ): ClientUnaryCall;
  deleteSubcategory(
    request: DeleteSubcategoryRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: DeleteSubcategoryResponse) => void,
  ): ClientUnaryCall;
  removeSubcategoryFromCategory(
    request: RemoveSubcategoryFromCategoryRequest,
    callback: (error: ServiceError | null, response: RemoveSubcategoryFromCategoryResponse) => void,
  ): ClientUnaryCall;
  removeSubcategoryFromCategory(
    request: RemoveSubcategoryFromCategoryRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: RemoveSubcategoryFromCategoryResponse) => void,
  ): ClientUnaryCall;
  removeSubcategoryFromCategory(
    request: RemoveSubcategoryFromCategoryRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: RemoveSubcategoryFromCategoryResponse) => void,
  ): ClientUnaryCall;
  createMerchant(
    request: CreateMerchantRequest,
    callback: (error: ServiceError | null, response: CreateMerchantResponse) => void,
  ): ClientUnaryCall;
  createMerchant(
    request: CreateMerchantRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: CreateMerchantResponse) => void,
  ): ClientUnaryCall;
  createMerchant(
    request: CreateMerchantRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: CreateMerchantResponse) => void,
  ): ClientUnaryCall;
  getMerchants(
    request: GetMerchantsRequest,
    callback: (error: ServiceError | null, response: GetMerchantsResponse) => void,
  ): ClientUnaryCall;
  getMerchants(
    request: GetMerchantsRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: GetMerchantsResponse) => void,
  ): ClientUnaryCall;
  getMerchants(
    request: GetMerchantsRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: GetMerchantsResponse) => void,
  ): ClientUnaryCall;
  deleteMerchant(
    request: DeleteMerchantRequest,
    callback: (error: ServiceError | null, response: DeleteMerchantResponse) => void,
  ): ClientUnaryCall;
  deleteMerchant(
    request: DeleteMerchantRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: DeleteMerchantResponse) => void,
  ): ClientUnaryCall;
  deleteMerchant(
    request: DeleteMerchantRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: DeleteMerchantResponse) => void,
  ): ClientUnaryCall;
  searchCategory(
    request: SearchCategoryRequest,
    callback: (error: ServiceError | null, response: SearchCategoryResponse) => void,
  ): ClientUnaryCall;
  searchCategory(
    request: SearchCategoryRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: SearchCategoryResponse) => void,
  ): ClientUnaryCall;
  searchCategory(
    request: SearchCategoryRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: SearchCategoryResponse) => void,
  ): ClientUnaryCall;
  searchMerchant(
    request: SearchMerchantRequest,
    callback: (error: ServiceError | null, response: SearchMerchantResponse) => void,
  ): ClientUnaryCall;
  searchMerchant(
    request: SearchMerchantRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: SearchMerchantResponse) => void,
  ): ClientUnaryCall;
  searchMerchant(
    request: SearchMerchantRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: SearchMerchantResponse) => void,
  ): ClientUnaryCall;
}

export const DepositTransactionTagsClient = makeGenericClientConstructor(
  DepositTransactionTagsService,
  "backend_services.visualization.DepositTransactionTags",
) as unknown as {
  new (
    address: string,
    credentials: ChannelCredentials,
    options?: Partial<ClientOptions>,
  ): DepositTransactionTagsClient;
  service: typeof DepositTransactionTagsService;
  serviceName: string;
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
