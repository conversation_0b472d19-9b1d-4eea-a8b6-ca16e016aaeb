import { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import {
  CategorySelector,
  debitCategories,
  TransactionMultiSelectCategory,
} from "@repo/ui";
import { useState } from "react";

const meta: Meta<typeof CategorySelector> = {
  title: "Components/Category Selector",
  component: CategorySelector,
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <div style={{ width: "486px" }}>
        <Story />
      </div>
    ),
  ],
};

export default meta;

type Story = StoryObj<typeof CategorySelector>;

export const Multiple: Story = {
  render: () => {
    const [currentSelected, setCurrentSelected] = useState<
      TransactionMultiSelectCategory | TransactionMultiSelectCategory[] | null
    >([
      {
        category_id: "67c8025059c3c908d42c61b2",
        subcategory_id: [
          "67c8025059c3c908d42c6201",
          "67c8025059c3c908d42c6202",
          "67c8025059c3c908d42c6203",
        ],
      },
      {
        category_id: "67c8025059c3c908d42c61a8",
        subcategory_id: [
          "67c8025059c3c908d42c61bc",
          "67c8025059c3c908d42c61d6",
          "67c8025059c3c908d42c61d7",
          "67c8025059c3c908d42c61d8",
        ],
      },
    ]);

    return (
      <CategorySelector
        categories={debitCategories}
        selectedCategories={currentSelected}
        onCategorySelectionChange={setCurrentSelected}
        mode={"filter"}
        multiSelection={true}
        showEveryCategories={false}
      />
    );
  },
};

export const Single: Story = {
  render: () => {
    const [currentSelected, setCurrentSelected] = useState<
      TransactionMultiSelectCategory | TransactionMultiSelectCategory[] | null
    >({
      category_id: "67c8025059c3c908d42c61b2",
      subcategory_id: ["67c8025059c3c908d42c6201"],
    });

    return (
      <CategorySelector
        categories={debitCategories}
        selectedCategories={currentSelected}
        onCategorySelectionChange={setCurrentSelected}
        mode={"modal"}
        multiSelection={false}
        showEveryCategories={false}
      />
    );
  },
};
