
syntax = "proto3";
package backend_services.visualization;

import "database/cards_filters.proto";
import "database/fi_deposit.proto";
import "backend_services/visualization/shared.proto";

// Service for Cashflow
service Cashflow{
    rpc GetCashflow(GetCashflowRequest) returns (GetCashflowResponse) {};
    rpc GetSubcategoryCashflow(GetSubcategoryCashflowRequest) returns (GetSubcategoryCashflowResponse) {};
}

message GetCashflowChartRequest {
    database.UserGroupFilters user_groups = 1;
    database.AccountsFilter account_filters = 2;
    database.TxnFilterTimeRange time_range = 3;
}


message CashflowChart {
    int64 start_time = 1;
    int64 end_time = 2;
    TimeStep time_step = 3;
    message YAxis {
        FlowType flow_type = 1;
        repeated double amount = 2;
    }
    repeated YAxis y_axes = 4;
}


message GetCashflowRequest {
    database.UserGroupFilters user_groups = 1;
    database.AccountsFilter account_filters = 2;
    database.TxnFilterTimeRange time_range = 3;
}
message GetCashflowResponse {
    CashflowChart chart = 1;
    repeated OverallCashflow overall_cashflows = 2;
    repeated CategoryCashflow category_cashflows = 3;
}


message GetSubcategoryCashflowRequest {
    database.UserGroupFilters user_groups = 1;
    database.AccountsFilter account_filters = 2;
    database.TxnFilterTimeRange time_range = 3;

    string category_collection = 4;
    string category_id = 5;
    FlowType flow_type = 6;
}
message GetSubcategoryCashflowResponse {
    repeated SubcategoryCashflow cash_flow = 1;
}

message SubcategoryCashflow {
    double amount = 1;
    double percentage = 2;
    string subcategory_id = 4;
}
