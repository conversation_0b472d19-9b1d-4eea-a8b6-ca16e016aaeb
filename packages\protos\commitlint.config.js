module.exports = {
    extends: ['@commitlint/config-conventional'],
    rules: {
      // You can define additional rules or override existing ones here
      'type-enum': [
        2,
        'always',
        [
          'build',
          'chore',
          'ci',
          'docs',
          'feat',
          'fix',
          'perf',
          'refactor',
          'revert',
          'style',
          'test',
        ],
      ],
      'subject-case': [2, 'never', ['sentence-case', 'start-case', 'pascal-case', 'upper-case']],
      'subject-full-stop': [2, 'never', '.'],
      'header-max-length': [0, 'always', 300],
      'footer-max-length': [0, 'always', 300],
      'footer-max-line-length': [0, 'always', 300],
      'body-max-line-length': [0, 'always', 300],
      'header-trim': [0, 'always'],
    },
  };
  