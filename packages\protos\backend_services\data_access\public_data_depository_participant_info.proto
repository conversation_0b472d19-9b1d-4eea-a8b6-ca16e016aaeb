syntax = "proto3";

package backend_services.data_access.public_data;
import "database/public_data.proto";

service PublicDataDepositoryParticipantInfo {
    rpc AddDepositoryParticipants(AddDepositoryParticipantRequest) returns (AddDepositoryParticipantResponse){};
    rpc GetDepositoryParticipant(GetDepositoryParticipantRequest) returns (GetDepositoryParticipantResponse){};
}

message DepositoryParticipant {
  string name = 1;
  string dp_id = 2;
  string depository_name = 3;
  string email = 4;
  string contact_no = 5;
  string address = 6;
  string website = 7;
}

message AddDepositoryParticipantRequest{
    repeated DepositoryParticipant records = 1;
}

message AddDepositoryParticipantResponse{}

message GetDepositoryParticipantRequest {
  string dp_id = 1;
}
message GetDepositoryParticipantResponse {
  database.DepositoryParticipantInfo record = 1;
}
