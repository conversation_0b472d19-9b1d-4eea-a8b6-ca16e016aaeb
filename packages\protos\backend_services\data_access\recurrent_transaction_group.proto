syntax = "proto3";
package backend_services.data_access;
import "database/fi_deposit.proto";
import "database/custom_type.proto";

service RecurrentTransactionGroup {
    rpc GetRecurrentTransactionGroupsWithRecentTxnByAccountIds(GetRecurrentTransactionGroupsWithRecentTxnByAccountIdsRequest) returns (GetRecurrentTransactionGroupsWithRecentTxnByAccountIdsResponse) {};
    rpc DeleteRecurrentTransactionGroup(DeleteRecurrentTransactionGroupRequest) returns (DeleteRecurrentTransactionGroupResponse) {}; 
    rpc GetRecurrentTransactionGroupsByGroupIds(GetRecurrentTransactionGroupsByGroupIdsRequest) returns (GetRecurrentTransactionGroupsByGroupIdsResponse) {};
    rpc GetRecurrentTransactionGroupsByAccountIds(GetRecurrentTransactionGroupsByAccountIdsRequest) returns (GetRecurrentTransactionGroupsByAccountIdsResponse) {};
    rpc UpdateRecurrentTransactionGroup(UpdateRecurrentTransactionGroupRequest) returns (UpdateRecurrentTransactionGroupResponse) {};
}

message GetRecurrentTransactionGroupsWithRecentTxnByAccountIdsRequest {
    repeated database.ObjectId account_ids = 1;
}

message GetRecurrentTransactionGroupsWithRecentTxnByAccountIdsResponse {
    repeated Record records = 1;

    message Record {
        database.RecurrentTransactionGroup group = 1;
        database.DepositTransaction recent_txn = 2;
    }
}

message DeleteRecurrentTransactionGroupRequest {
    database.ObjectId group_id = 1; 
}
message DeleteRecurrentTransactionGroupResponse {}

message GetRecurrentTransactionGroupsByGroupIdsRequest {
    repeated database.ObjectId group_ids = 1;
}
message GetRecurrentTransactionGroupsByGroupIdsResponse {
    repeated database.RecurrentTransactionGroup records = 1;
}


message GetRecurrentTransactionGroupsByAccountIdsRequest {
    repeated database.ObjectId account_ids = 1;
    database.RecurrentTxnGroupFilters group_filters = 2;
}
message GetRecurrentTransactionGroupsByAccountIdsResponse {
    repeated database.RecurrentTransactionGroup records = 1;
}

message UpdateRecurrentTransactionGroupRequest {
    database.ObjectId group_id = 1;
    message Updates {
        database.RecurrentGroupFilterFavorite favorited = 1;
        database.RecurrentGroupFilterExcludeCashFlow exclude_cash_flow = 2;
    }
    Updates updates = 2;
}

message UpdateRecurrentTransactionGroupResponse {}
