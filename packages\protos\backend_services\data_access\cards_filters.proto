syntax = "proto3";
package backend_services.data_access;
import "database/cards_filters.proto";

service CardsFilters {
    rpc SetCardsFilters(SetCardsFiltersRequest) returns (SetCardsFiltersResponse) {};
    rpc GetCardsFilters(GetCardsFiltersRequest) returns (GetCardsFiltersResponse) {};
	rpc SetCardsPriorities(SetCardsPrioritiesRequest) returns (SetCardsPrioritiesResponse) {};
}

message SetCardsFiltersRequest {
	repeated database.CardFilters records = 1;
}

message SetCardsFiltersResponse {}


message GetCardsFiltersRequest{
	string phone_number = 1;
}

message GetCardsFiltersResponse {
	repeated database.CardFilters records = 1;
}

message SetCardsPrioritiesRequest {
	repeated database.CardFilters records = 1;
}
message SetCardsPrioritiesResponse {}