// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.2
//   protoc               v6.31.1
// source: database/cards_filters.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { ObjectId } from "./custom_type";

export const protobufPackage = "database";

export enum AccountsOf {
  ACCOUNTS_OF_UNSPECIFIED = 0,
  ACCOUNTS_OF_USER = 1,
  ACCOUNTS_OF_GROUP = 2,
  UNRECOGNIZED = -1,
}

export function accountsOfFromJSON(object: any): AccountsOf {
  switch (object) {
    case 0:
    case "ACCOUNTS_OF_UNSPECIFIED":
      return AccountsOf.ACCOUNTS_OF_UNSPECIFIED;
    case 1:
    case "ACCOUNTS_OF_USER":
      return AccountsOf.ACCOUNTS_OF_USER;
    case 2:
    case "ACCOUNTS_OF_GROUP":
      return AccountsOf.ACCOUNTS_OF_GROUP;
    case -1:
    case "UNRECOGNIZED":
    default:
      return AccountsOf.UNRECOGNIZED;
  }
}

export function accountsOfToJSON(object: AccountsOf): string {
  switch (object) {
    case AccountsOf.ACCOUNTS_OF_UNSPECIFIED:
      return "ACCOUNTS_OF_UNSPECIFIED";
    case AccountsOf.ACCOUNTS_OF_USER:
      return "ACCOUNTS_OF_USER";
    case AccountsOf.ACCOUNTS_OF_GROUP:
      return "ACCOUNTS_OF_GROUP";
    case AccountsOf.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface TimeSpan {
  /** @gotags: bson:"-" */
  allowedValues: string[];
  value: string;
}

export interface BankBalanceCardUserFilters {
  timeSpan: TimeSpan | undefined;
}

export interface EquitiesXIRRCardUserFilters {
  timeSpan: TimeSpan | undefined;
}

export interface MutualFundsXIRRCardUserFilters {
  timeSpan: TimeSpan | undefined;
}

export interface EquitiesReturnsCardUserFilters {
  timeSpan: TimeSpan | undefined;
}

export interface MutualFundsReturnsCardUserFilters {
  timeSpan: TimeSpan | undefined;
}

export interface BankAccountSummaryCardSystemFilters {
  accountId: ObjectId | undefined;
}

export interface EquitiesAccountSummaryCardSystemFilters {
  accountId: ObjectId | undefined;
}

export interface MutualFundsAccountSummaryCardSystemFilters {
  accountId: ObjectId | undefined;
}

export interface WrapOneOfSystemFilter {
  bankAccountSummaryCardSystemFilters?: BankAccountSummaryCardSystemFilters | undefined;
  equitiesAccountSummaryCardSystemFilters?: EquitiesAccountSummaryCardSystemFilters | undefined;
  mutualFundsAccountSummaryCardSystemFilters?: MutualFundsAccountSummaryCardSystemFilters | undefined;
}

export interface WrapOneOfUserFilter {
  bankBalanceCardFilters?: BankBalanceCardUserFilters | undefined;
  equitiesXirrCardFilters?: EquitiesXIRRCardUserFilters | undefined;
  mutualFundsXirrCardFilters?: MutualFundsXIRRCardUserFilters | undefined;
  equitiesReturnsCardFilters?: EquitiesReturnsCardUserFilters | undefined;
  mutualFundsReturnsCardFilters?: MutualFundsReturnsCardUserFilters | undefined;
}

export interface UserFilters {
  filter: WrapOneOfUserFilter | undefined;
  userGroupFilters: UserGroupFilters | undefined;
}

export interface SystemFilters {
  filter: WrapOneOfSystemFilter | undefined;
}

export interface UserGroupFilters {
  userGroupIds: string[];
  userIds: string[];
}

export interface AccountsFilter {
  accountIds: string[];
  accountsOf: AccountsOf;
}

/** flag:is-collection=true; */
export interface CardFilters {
  Id: ObjectId | undefined;
  phoneNumber: string;
  cardType: string;
  cardId: string;
  priority: number;
  systemFilters: SystemFilters | undefined;
  userFilters: UserFilters | undefined;
  createdAt: number;
  updatedAt: number;
}

/** flag:is-collection=true; */
export interface UserDefaults {
  Id: ObjectId | undefined;
  phoneNumber: string;
  userGroupFilters: UserGroupFilters | undefined;
  accountFilters: AccountsFilter | undefined;
  createdAt: number;
  updatedAt: number;
}

function createBaseTimeSpan(): TimeSpan {
  return { allowedValues: [], value: "" };
}

export const TimeSpan: MessageFns<TimeSpan> = {
  encode(message: TimeSpan, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.allowedValues) {
      writer.uint32(10).string(v!);
    }
    if (message.value !== "") {
      writer.uint32(18).string(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TimeSpan {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTimeSpan();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.allowedValues.push(reader.string());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TimeSpan {
    return {
      allowedValues: globalThis.Array.isArray(object?.allowedValues)
        ? object.allowedValues.map((e: any) => globalThis.String(e))
        : [],
      value: isSet(object.value) ? globalThis.String(object.value) : "",
    };
  },

  toJSON(message: TimeSpan): unknown {
    const obj: any = {};
    if (message.allowedValues?.length) {
      obj.allowedValues = message.allowedValues;
    }
    if (message.value !== "") {
      obj.value = message.value;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TimeSpan>, I>>(base?: I): TimeSpan {
    return TimeSpan.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TimeSpan>, I>>(object: I): TimeSpan {
    const message = createBaseTimeSpan();
    message.allowedValues = object.allowedValues?.map((e) => e) || [];
    message.value = object.value ?? "";
    return message;
  },
};

function createBaseBankBalanceCardUserFilters(): BankBalanceCardUserFilters {
  return { timeSpan: undefined };
}

export const BankBalanceCardUserFilters: MessageFns<BankBalanceCardUserFilters> = {
  encode(message: BankBalanceCardUserFilters, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.timeSpan !== undefined) {
      TimeSpan.encode(message.timeSpan, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankBalanceCardUserFilters {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankBalanceCardUserFilters();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.timeSpan = TimeSpan.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankBalanceCardUserFilters {
    return { timeSpan: isSet(object.timeSpan) ? TimeSpan.fromJSON(object.timeSpan) : undefined };
  },

  toJSON(message: BankBalanceCardUserFilters): unknown {
    const obj: any = {};
    if (message.timeSpan !== undefined) {
      obj.timeSpan = TimeSpan.toJSON(message.timeSpan);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankBalanceCardUserFilters>, I>>(base?: I): BankBalanceCardUserFilters {
    return BankBalanceCardUserFilters.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankBalanceCardUserFilters>, I>>(object: I): BankBalanceCardUserFilters {
    const message = createBaseBankBalanceCardUserFilters();
    message.timeSpan = (object.timeSpan !== undefined && object.timeSpan !== null)
      ? TimeSpan.fromPartial(object.timeSpan)
      : undefined;
    return message;
  },
};

function createBaseEquitiesXIRRCardUserFilters(): EquitiesXIRRCardUserFilters {
  return { timeSpan: undefined };
}

export const EquitiesXIRRCardUserFilters: MessageFns<EquitiesXIRRCardUserFilters> = {
  encode(message: EquitiesXIRRCardUserFilters, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.timeSpan !== undefined) {
      TimeSpan.encode(message.timeSpan, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EquitiesXIRRCardUserFilters {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEquitiesXIRRCardUserFilters();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.timeSpan = TimeSpan.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): EquitiesXIRRCardUserFilters {
    return { timeSpan: isSet(object.timeSpan) ? TimeSpan.fromJSON(object.timeSpan) : undefined };
  },

  toJSON(message: EquitiesXIRRCardUserFilters): unknown {
    const obj: any = {};
    if (message.timeSpan !== undefined) {
      obj.timeSpan = TimeSpan.toJSON(message.timeSpan);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<EquitiesXIRRCardUserFilters>, I>>(base?: I): EquitiesXIRRCardUserFilters {
    return EquitiesXIRRCardUserFilters.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EquitiesXIRRCardUserFilters>, I>>(object: I): EquitiesXIRRCardUserFilters {
    const message = createBaseEquitiesXIRRCardUserFilters();
    message.timeSpan = (object.timeSpan !== undefined && object.timeSpan !== null)
      ? TimeSpan.fromPartial(object.timeSpan)
      : undefined;
    return message;
  },
};

function createBaseMutualFundsXIRRCardUserFilters(): MutualFundsXIRRCardUserFilters {
  return { timeSpan: undefined };
}

export const MutualFundsXIRRCardUserFilters: MessageFns<MutualFundsXIRRCardUserFilters> = {
  encode(message: MutualFundsXIRRCardUserFilters, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.timeSpan !== undefined) {
      TimeSpan.encode(message.timeSpan, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MutualFundsXIRRCardUserFilters {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMutualFundsXIRRCardUserFilters();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.timeSpan = TimeSpan.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MutualFundsXIRRCardUserFilters {
    return { timeSpan: isSet(object.timeSpan) ? TimeSpan.fromJSON(object.timeSpan) : undefined };
  },

  toJSON(message: MutualFundsXIRRCardUserFilters): unknown {
    const obj: any = {};
    if (message.timeSpan !== undefined) {
      obj.timeSpan = TimeSpan.toJSON(message.timeSpan);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MutualFundsXIRRCardUserFilters>, I>>(base?: I): MutualFundsXIRRCardUserFilters {
    return MutualFundsXIRRCardUserFilters.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MutualFundsXIRRCardUserFilters>, I>>(
    object: I,
  ): MutualFundsXIRRCardUserFilters {
    const message = createBaseMutualFundsXIRRCardUserFilters();
    message.timeSpan = (object.timeSpan !== undefined && object.timeSpan !== null)
      ? TimeSpan.fromPartial(object.timeSpan)
      : undefined;
    return message;
  },
};

function createBaseEquitiesReturnsCardUserFilters(): EquitiesReturnsCardUserFilters {
  return { timeSpan: undefined };
}

export const EquitiesReturnsCardUserFilters: MessageFns<EquitiesReturnsCardUserFilters> = {
  encode(message: EquitiesReturnsCardUserFilters, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.timeSpan !== undefined) {
      TimeSpan.encode(message.timeSpan, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EquitiesReturnsCardUserFilters {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEquitiesReturnsCardUserFilters();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.timeSpan = TimeSpan.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): EquitiesReturnsCardUserFilters {
    return { timeSpan: isSet(object.timeSpan) ? TimeSpan.fromJSON(object.timeSpan) : undefined };
  },

  toJSON(message: EquitiesReturnsCardUserFilters): unknown {
    const obj: any = {};
    if (message.timeSpan !== undefined) {
      obj.timeSpan = TimeSpan.toJSON(message.timeSpan);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<EquitiesReturnsCardUserFilters>, I>>(base?: I): EquitiesReturnsCardUserFilters {
    return EquitiesReturnsCardUserFilters.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EquitiesReturnsCardUserFilters>, I>>(
    object: I,
  ): EquitiesReturnsCardUserFilters {
    const message = createBaseEquitiesReturnsCardUserFilters();
    message.timeSpan = (object.timeSpan !== undefined && object.timeSpan !== null)
      ? TimeSpan.fromPartial(object.timeSpan)
      : undefined;
    return message;
  },
};

function createBaseMutualFundsReturnsCardUserFilters(): MutualFundsReturnsCardUserFilters {
  return { timeSpan: undefined };
}

export const MutualFundsReturnsCardUserFilters: MessageFns<MutualFundsReturnsCardUserFilters> = {
  encode(message: MutualFundsReturnsCardUserFilters, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.timeSpan !== undefined) {
      TimeSpan.encode(message.timeSpan, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MutualFundsReturnsCardUserFilters {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMutualFundsReturnsCardUserFilters();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.timeSpan = TimeSpan.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MutualFundsReturnsCardUserFilters {
    return { timeSpan: isSet(object.timeSpan) ? TimeSpan.fromJSON(object.timeSpan) : undefined };
  },

  toJSON(message: MutualFundsReturnsCardUserFilters): unknown {
    const obj: any = {};
    if (message.timeSpan !== undefined) {
      obj.timeSpan = TimeSpan.toJSON(message.timeSpan);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MutualFundsReturnsCardUserFilters>, I>>(
    base?: I,
  ): MutualFundsReturnsCardUserFilters {
    return MutualFundsReturnsCardUserFilters.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MutualFundsReturnsCardUserFilters>, I>>(
    object: I,
  ): MutualFundsReturnsCardUserFilters {
    const message = createBaseMutualFundsReturnsCardUserFilters();
    message.timeSpan = (object.timeSpan !== undefined && object.timeSpan !== null)
      ? TimeSpan.fromPartial(object.timeSpan)
      : undefined;
    return message;
  },
};

function createBaseBankAccountSummaryCardSystemFilters(): BankAccountSummaryCardSystemFilters {
  return { accountId: undefined };
}

export const BankAccountSummaryCardSystemFilters: MessageFns<BankAccountSummaryCardSystemFilters> = {
  encode(message: BankAccountSummaryCardSystemFilters, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.accountId !== undefined) {
      ObjectId.encode(message.accountId, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankAccountSummaryCardSystemFilters {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankAccountSummaryCardSystemFilters();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.accountId = ObjectId.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankAccountSummaryCardSystemFilters {
    return { accountId: isSet(object.accountId) ? ObjectId.fromJSON(object.accountId) : undefined };
  },

  toJSON(message: BankAccountSummaryCardSystemFilters): unknown {
    const obj: any = {};
    if (message.accountId !== undefined) {
      obj.accountId = ObjectId.toJSON(message.accountId);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankAccountSummaryCardSystemFilters>, I>>(
    base?: I,
  ): BankAccountSummaryCardSystemFilters {
    return BankAccountSummaryCardSystemFilters.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankAccountSummaryCardSystemFilters>, I>>(
    object: I,
  ): BankAccountSummaryCardSystemFilters {
    const message = createBaseBankAccountSummaryCardSystemFilters();
    message.accountId = (object.accountId !== undefined && object.accountId !== null)
      ? ObjectId.fromPartial(object.accountId)
      : undefined;
    return message;
  },
};

function createBaseEquitiesAccountSummaryCardSystemFilters(): EquitiesAccountSummaryCardSystemFilters {
  return { accountId: undefined };
}

export const EquitiesAccountSummaryCardSystemFilters: MessageFns<EquitiesAccountSummaryCardSystemFilters> = {
  encode(message: EquitiesAccountSummaryCardSystemFilters, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.accountId !== undefined) {
      ObjectId.encode(message.accountId, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EquitiesAccountSummaryCardSystemFilters {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEquitiesAccountSummaryCardSystemFilters();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.accountId = ObjectId.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): EquitiesAccountSummaryCardSystemFilters {
    return { accountId: isSet(object.accountId) ? ObjectId.fromJSON(object.accountId) : undefined };
  },

  toJSON(message: EquitiesAccountSummaryCardSystemFilters): unknown {
    const obj: any = {};
    if (message.accountId !== undefined) {
      obj.accountId = ObjectId.toJSON(message.accountId);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<EquitiesAccountSummaryCardSystemFilters>, I>>(
    base?: I,
  ): EquitiesAccountSummaryCardSystemFilters {
    return EquitiesAccountSummaryCardSystemFilters.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EquitiesAccountSummaryCardSystemFilters>, I>>(
    object: I,
  ): EquitiesAccountSummaryCardSystemFilters {
    const message = createBaseEquitiesAccountSummaryCardSystemFilters();
    message.accountId = (object.accountId !== undefined && object.accountId !== null)
      ? ObjectId.fromPartial(object.accountId)
      : undefined;
    return message;
  },
};

function createBaseMutualFundsAccountSummaryCardSystemFilters(): MutualFundsAccountSummaryCardSystemFilters {
  return { accountId: undefined };
}

export const MutualFundsAccountSummaryCardSystemFilters: MessageFns<MutualFundsAccountSummaryCardSystemFilters> = {
  encode(message: MutualFundsAccountSummaryCardSystemFilters, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.accountId !== undefined) {
      ObjectId.encode(message.accountId, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MutualFundsAccountSummaryCardSystemFilters {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMutualFundsAccountSummaryCardSystemFilters();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.accountId = ObjectId.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MutualFundsAccountSummaryCardSystemFilters {
    return { accountId: isSet(object.accountId) ? ObjectId.fromJSON(object.accountId) : undefined };
  },

  toJSON(message: MutualFundsAccountSummaryCardSystemFilters): unknown {
    const obj: any = {};
    if (message.accountId !== undefined) {
      obj.accountId = ObjectId.toJSON(message.accountId);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MutualFundsAccountSummaryCardSystemFilters>, I>>(
    base?: I,
  ): MutualFundsAccountSummaryCardSystemFilters {
    return MutualFundsAccountSummaryCardSystemFilters.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MutualFundsAccountSummaryCardSystemFilters>, I>>(
    object: I,
  ): MutualFundsAccountSummaryCardSystemFilters {
    const message = createBaseMutualFundsAccountSummaryCardSystemFilters();
    message.accountId = (object.accountId !== undefined && object.accountId !== null)
      ? ObjectId.fromPartial(object.accountId)
      : undefined;
    return message;
  },
};

function createBaseWrapOneOfSystemFilter(): WrapOneOfSystemFilter {
  return {
    bankAccountSummaryCardSystemFilters: undefined,
    equitiesAccountSummaryCardSystemFilters: undefined,
    mutualFundsAccountSummaryCardSystemFilters: undefined,
  };
}

export const WrapOneOfSystemFilter: MessageFns<WrapOneOfSystemFilter> = {
  encode(message: WrapOneOfSystemFilter, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bankAccountSummaryCardSystemFilters !== undefined) {
      BankAccountSummaryCardSystemFilters.encode(message.bankAccountSummaryCardSystemFilters, writer.uint32(10).fork())
        .join();
    }
    if (message.equitiesAccountSummaryCardSystemFilters !== undefined) {
      EquitiesAccountSummaryCardSystemFilters.encode(
        message.equitiesAccountSummaryCardSystemFilters,
        writer.uint32(18).fork(),
      ).join();
    }
    if (message.mutualFundsAccountSummaryCardSystemFilters !== undefined) {
      MutualFundsAccountSummaryCardSystemFilters.encode(
        message.mutualFundsAccountSummaryCardSystemFilters,
        writer.uint32(26).fork(),
      ).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WrapOneOfSystemFilter {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWrapOneOfSystemFilter();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bankAccountSummaryCardSystemFilters = BankAccountSummaryCardSystemFilters.decode(
            reader,
            reader.uint32(),
          );
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.equitiesAccountSummaryCardSystemFilters = EquitiesAccountSummaryCardSystemFilters.decode(
            reader,
            reader.uint32(),
          );
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.mutualFundsAccountSummaryCardSystemFilters = MutualFundsAccountSummaryCardSystemFilters.decode(
            reader,
            reader.uint32(),
          );
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WrapOneOfSystemFilter {
    return {
      bankAccountSummaryCardSystemFilters: isSet(object.bankAccountSummaryCardSystemFilters)
        ? BankAccountSummaryCardSystemFilters.fromJSON(object.bankAccountSummaryCardSystemFilters)
        : undefined,
      equitiesAccountSummaryCardSystemFilters: isSet(object.equitiesAccountSummaryCardSystemFilters)
        ? EquitiesAccountSummaryCardSystemFilters.fromJSON(object.equitiesAccountSummaryCardSystemFilters)
        : undefined,
      mutualFundsAccountSummaryCardSystemFilters: isSet(object.mutualFundsAccountSummaryCardSystemFilters)
        ? MutualFundsAccountSummaryCardSystemFilters.fromJSON(object.mutualFundsAccountSummaryCardSystemFilters)
        : undefined,
    };
  },

  toJSON(message: WrapOneOfSystemFilter): unknown {
    const obj: any = {};
    if (message.bankAccountSummaryCardSystemFilters !== undefined) {
      obj.bankAccountSummaryCardSystemFilters = BankAccountSummaryCardSystemFilters.toJSON(
        message.bankAccountSummaryCardSystemFilters,
      );
    }
    if (message.equitiesAccountSummaryCardSystemFilters !== undefined) {
      obj.equitiesAccountSummaryCardSystemFilters = EquitiesAccountSummaryCardSystemFilters.toJSON(
        message.equitiesAccountSummaryCardSystemFilters,
      );
    }
    if (message.mutualFundsAccountSummaryCardSystemFilters !== undefined) {
      obj.mutualFundsAccountSummaryCardSystemFilters = MutualFundsAccountSummaryCardSystemFilters.toJSON(
        message.mutualFundsAccountSummaryCardSystemFilters,
      );
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WrapOneOfSystemFilter>, I>>(base?: I): WrapOneOfSystemFilter {
    return WrapOneOfSystemFilter.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WrapOneOfSystemFilter>, I>>(object: I): WrapOneOfSystemFilter {
    const message = createBaseWrapOneOfSystemFilter();
    message.bankAccountSummaryCardSystemFilters =
      (object.bankAccountSummaryCardSystemFilters !== undefined && object.bankAccountSummaryCardSystemFilters !== null)
        ? BankAccountSummaryCardSystemFilters.fromPartial(object.bankAccountSummaryCardSystemFilters)
        : undefined;
    message.equitiesAccountSummaryCardSystemFilters =
      (object.equitiesAccountSummaryCardSystemFilters !== undefined &&
          object.equitiesAccountSummaryCardSystemFilters !== null)
        ? EquitiesAccountSummaryCardSystemFilters.fromPartial(object.equitiesAccountSummaryCardSystemFilters)
        : undefined;
    message.mutualFundsAccountSummaryCardSystemFilters =
      (object.mutualFundsAccountSummaryCardSystemFilters !== undefined &&
          object.mutualFundsAccountSummaryCardSystemFilters !== null)
        ? MutualFundsAccountSummaryCardSystemFilters.fromPartial(object.mutualFundsAccountSummaryCardSystemFilters)
        : undefined;
    return message;
  },
};

function createBaseWrapOneOfUserFilter(): WrapOneOfUserFilter {
  return {
    bankBalanceCardFilters: undefined,
    equitiesXirrCardFilters: undefined,
    mutualFundsXirrCardFilters: undefined,
    equitiesReturnsCardFilters: undefined,
    mutualFundsReturnsCardFilters: undefined,
  };
}

export const WrapOneOfUserFilter: MessageFns<WrapOneOfUserFilter> = {
  encode(message: WrapOneOfUserFilter, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bankBalanceCardFilters !== undefined) {
      BankBalanceCardUserFilters.encode(message.bankBalanceCardFilters, writer.uint32(10).fork()).join();
    }
    if (message.equitiesXirrCardFilters !== undefined) {
      EquitiesXIRRCardUserFilters.encode(message.equitiesXirrCardFilters, writer.uint32(18).fork()).join();
    }
    if (message.mutualFundsXirrCardFilters !== undefined) {
      MutualFundsXIRRCardUserFilters.encode(message.mutualFundsXirrCardFilters, writer.uint32(26).fork()).join();
    }
    if (message.equitiesReturnsCardFilters !== undefined) {
      EquitiesReturnsCardUserFilters.encode(message.equitiesReturnsCardFilters, writer.uint32(34).fork()).join();
    }
    if (message.mutualFundsReturnsCardFilters !== undefined) {
      MutualFundsReturnsCardUserFilters.encode(message.mutualFundsReturnsCardFilters, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WrapOneOfUserFilter {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWrapOneOfUserFilter();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bankBalanceCardFilters = BankBalanceCardUserFilters.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.equitiesXirrCardFilters = EquitiesXIRRCardUserFilters.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.mutualFundsXirrCardFilters = MutualFundsXIRRCardUserFilters.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.equitiesReturnsCardFilters = EquitiesReturnsCardUserFilters.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.mutualFundsReturnsCardFilters = MutualFundsReturnsCardUserFilters.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WrapOneOfUserFilter {
    return {
      bankBalanceCardFilters: isSet(object.bankBalanceCardFilters)
        ? BankBalanceCardUserFilters.fromJSON(object.bankBalanceCardFilters)
        : undefined,
      equitiesXirrCardFilters: isSet(object.equitiesXirrCardFilters)
        ? EquitiesXIRRCardUserFilters.fromJSON(object.equitiesXirrCardFilters)
        : undefined,
      mutualFundsXirrCardFilters: isSet(object.mutualFundsXirrCardFilters)
        ? MutualFundsXIRRCardUserFilters.fromJSON(object.mutualFundsXirrCardFilters)
        : undefined,
      equitiesReturnsCardFilters: isSet(object.equitiesReturnsCardFilters)
        ? EquitiesReturnsCardUserFilters.fromJSON(object.equitiesReturnsCardFilters)
        : undefined,
      mutualFundsReturnsCardFilters: isSet(object.mutualFundsReturnsCardFilters)
        ? MutualFundsReturnsCardUserFilters.fromJSON(object.mutualFundsReturnsCardFilters)
        : undefined,
    };
  },

  toJSON(message: WrapOneOfUserFilter): unknown {
    const obj: any = {};
    if (message.bankBalanceCardFilters !== undefined) {
      obj.bankBalanceCardFilters = BankBalanceCardUserFilters.toJSON(message.bankBalanceCardFilters);
    }
    if (message.equitiesXirrCardFilters !== undefined) {
      obj.equitiesXirrCardFilters = EquitiesXIRRCardUserFilters.toJSON(message.equitiesXirrCardFilters);
    }
    if (message.mutualFundsXirrCardFilters !== undefined) {
      obj.mutualFundsXirrCardFilters = MutualFundsXIRRCardUserFilters.toJSON(message.mutualFundsXirrCardFilters);
    }
    if (message.equitiesReturnsCardFilters !== undefined) {
      obj.equitiesReturnsCardFilters = EquitiesReturnsCardUserFilters.toJSON(message.equitiesReturnsCardFilters);
    }
    if (message.mutualFundsReturnsCardFilters !== undefined) {
      obj.mutualFundsReturnsCardFilters = MutualFundsReturnsCardUserFilters.toJSON(
        message.mutualFundsReturnsCardFilters,
      );
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WrapOneOfUserFilter>, I>>(base?: I): WrapOneOfUserFilter {
    return WrapOneOfUserFilter.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WrapOneOfUserFilter>, I>>(object: I): WrapOneOfUserFilter {
    const message = createBaseWrapOneOfUserFilter();
    message.bankBalanceCardFilters =
      (object.bankBalanceCardFilters !== undefined && object.bankBalanceCardFilters !== null)
        ? BankBalanceCardUserFilters.fromPartial(object.bankBalanceCardFilters)
        : undefined;
    message.equitiesXirrCardFilters =
      (object.equitiesXirrCardFilters !== undefined && object.equitiesXirrCardFilters !== null)
        ? EquitiesXIRRCardUserFilters.fromPartial(object.equitiesXirrCardFilters)
        : undefined;
    message.mutualFundsXirrCardFilters =
      (object.mutualFundsXirrCardFilters !== undefined && object.mutualFundsXirrCardFilters !== null)
        ? MutualFundsXIRRCardUserFilters.fromPartial(object.mutualFundsXirrCardFilters)
        : undefined;
    message.equitiesReturnsCardFilters =
      (object.equitiesReturnsCardFilters !== undefined && object.equitiesReturnsCardFilters !== null)
        ? EquitiesReturnsCardUserFilters.fromPartial(object.equitiesReturnsCardFilters)
        : undefined;
    message.mutualFundsReturnsCardFilters =
      (object.mutualFundsReturnsCardFilters !== undefined && object.mutualFundsReturnsCardFilters !== null)
        ? MutualFundsReturnsCardUserFilters.fromPartial(object.mutualFundsReturnsCardFilters)
        : undefined;
    return message;
  },
};

function createBaseUserFilters(): UserFilters {
  return { filter: undefined, userGroupFilters: undefined };
}

export const UserFilters: MessageFns<UserFilters> = {
  encode(message: UserFilters, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.filter !== undefined) {
      WrapOneOfUserFilter.encode(message.filter, writer.uint32(10).fork()).join();
    }
    if (message.userGroupFilters !== undefined) {
      UserGroupFilters.encode(message.userGroupFilters, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserFilters {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserFilters();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.filter = WrapOneOfUserFilter.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.userGroupFilters = UserGroupFilters.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserFilters {
    return {
      filter: isSet(object.filter) ? WrapOneOfUserFilter.fromJSON(object.filter) : undefined,
      userGroupFilters: isSet(object.userGroupFilters) ? UserGroupFilters.fromJSON(object.userGroupFilters) : undefined,
    };
  },

  toJSON(message: UserFilters): unknown {
    const obj: any = {};
    if (message.filter !== undefined) {
      obj.filter = WrapOneOfUserFilter.toJSON(message.filter);
    }
    if (message.userGroupFilters !== undefined) {
      obj.userGroupFilters = UserGroupFilters.toJSON(message.userGroupFilters);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserFilters>, I>>(base?: I): UserFilters {
    return UserFilters.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserFilters>, I>>(object: I): UserFilters {
    const message = createBaseUserFilters();
    message.filter = (object.filter !== undefined && object.filter !== null)
      ? WrapOneOfUserFilter.fromPartial(object.filter)
      : undefined;
    message.userGroupFilters = (object.userGroupFilters !== undefined && object.userGroupFilters !== null)
      ? UserGroupFilters.fromPartial(object.userGroupFilters)
      : undefined;
    return message;
  },
};

function createBaseSystemFilters(): SystemFilters {
  return { filter: undefined };
}

export const SystemFilters: MessageFns<SystemFilters> = {
  encode(message: SystemFilters, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.filter !== undefined) {
      WrapOneOfSystemFilter.encode(message.filter, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SystemFilters {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSystemFilters();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.filter = WrapOneOfSystemFilter.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SystemFilters {
    return { filter: isSet(object.filter) ? WrapOneOfSystemFilter.fromJSON(object.filter) : undefined };
  },

  toJSON(message: SystemFilters): unknown {
    const obj: any = {};
    if (message.filter !== undefined) {
      obj.filter = WrapOneOfSystemFilter.toJSON(message.filter);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SystemFilters>, I>>(base?: I): SystemFilters {
    return SystemFilters.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SystemFilters>, I>>(object: I): SystemFilters {
    const message = createBaseSystemFilters();
    message.filter = (object.filter !== undefined && object.filter !== null)
      ? WrapOneOfSystemFilter.fromPartial(object.filter)
      : undefined;
    return message;
  },
};

function createBaseUserGroupFilters(): UserGroupFilters {
  return { userGroupIds: [], userIds: [] };
}

export const UserGroupFilters: MessageFns<UserGroupFilters> = {
  encode(message: UserGroupFilters, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.userGroupIds) {
      writer.uint32(10).string(v!);
    }
    for (const v of message.userIds) {
      writer.uint32(18).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserGroupFilters {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserGroupFilters();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userGroupIds.push(reader.string());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.userIds.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserGroupFilters {
    return {
      userGroupIds: globalThis.Array.isArray(object?.userGroupIds)
        ? object.userGroupIds.map((e: any) => globalThis.String(e))
        : [],
      userIds: globalThis.Array.isArray(object?.userIds) ? object.userIds.map((e: any) => globalThis.String(e)) : [],
    };
  },

  toJSON(message: UserGroupFilters): unknown {
    const obj: any = {};
    if (message.userGroupIds?.length) {
      obj.userGroupIds = message.userGroupIds;
    }
    if (message.userIds?.length) {
      obj.userIds = message.userIds;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserGroupFilters>, I>>(base?: I): UserGroupFilters {
    return UserGroupFilters.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserGroupFilters>, I>>(object: I): UserGroupFilters {
    const message = createBaseUserGroupFilters();
    message.userGroupIds = object.userGroupIds?.map((e) => e) || [];
    message.userIds = object.userIds?.map((e) => e) || [];
    return message;
  },
};

function createBaseAccountsFilter(): AccountsFilter {
  return { accountIds: [], accountsOf: 0 };
}

export const AccountsFilter: MessageFns<AccountsFilter> = {
  encode(message: AccountsFilter, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.accountIds) {
      writer.uint32(10).string(v!);
    }
    if (message.accountsOf !== 0) {
      writer.uint32(16).int32(message.accountsOf);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AccountsFilter {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAccountsFilter();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.accountIds.push(reader.string());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.accountsOf = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AccountsFilter {
    return {
      accountIds: globalThis.Array.isArray(object?.accountIds)
        ? object.accountIds.map((e: any) => globalThis.String(e))
        : [],
      accountsOf: isSet(object.accountsOf) ? accountsOfFromJSON(object.accountsOf) : 0,
    };
  },

  toJSON(message: AccountsFilter): unknown {
    const obj: any = {};
    if (message.accountIds?.length) {
      obj.accountIds = message.accountIds;
    }
    if (message.accountsOf !== 0) {
      obj.accountsOf = accountsOfToJSON(message.accountsOf);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AccountsFilter>, I>>(base?: I): AccountsFilter {
    return AccountsFilter.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AccountsFilter>, I>>(object: I): AccountsFilter {
    const message = createBaseAccountsFilter();
    message.accountIds = object.accountIds?.map((e) => e) || [];
    message.accountsOf = object.accountsOf ?? 0;
    return message;
  },
};

function createBaseCardFilters(): CardFilters {
  return {
    Id: undefined,
    phoneNumber: "",
    cardType: "",
    cardId: "",
    priority: 0,
    systemFilters: undefined,
    userFilters: undefined,
    createdAt: 0,
    updatedAt: 0,
  };
}

export const CardFilters: MessageFns<CardFilters> = {
  encode(message: CardFilters, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.Id !== undefined) {
      ObjectId.encode(message.Id, writer.uint32(10).fork()).join();
    }
    if (message.phoneNumber !== "") {
      writer.uint32(18).string(message.phoneNumber);
    }
    if (message.cardType !== "") {
      writer.uint32(26).string(message.cardType);
    }
    if (message.cardId !== "") {
      writer.uint32(34).string(message.cardId);
    }
    if (message.priority !== 0) {
      writer.uint32(40).int64(message.priority);
    }
    if (message.systemFilters !== undefined) {
      SystemFilters.encode(message.systemFilters, writer.uint32(50).fork()).join();
    }
    if (message.userFilters !== undefined) {
      UserFilters.encode(message.userFilters, writer.uint32(58).fork()).join();
    }
    if (message.createdAt !== 0) {
      writer.uint32(64).int64(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      writer.uint32(72).int64(message.updatedAt);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CardFilters {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCardFilters();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.Id = ObjectId.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.phoneNumber = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.cardType = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.cardId = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.priority = longToNumber(reader.int64());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.systemFilters = SystemFilters.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.userFilters = UserFilters.decode(reader, reader.uint32());
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.createdAt = longToNumber(reader.int64());
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.updatedAt = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CardFilters {
    return {
      Id: isSet(object.Id) ? ObjectId.fromJSON(object.Id) : undefined,
      phoneNumber: isSet(object.phoneNumber) ? globalThis.String(object.phoneNumber) : "",
      cardType: isSet(object.cardType) ? globalThis.String(object.cardType) : "",
      cardId: isSet(object.cardId) ? globalThis.String(object.cardId) : "",
      priority: isSet(object.priority) ? globalThis.Number(object.priority) : 0,
      systemFilters: isSet(object.systemFilters) ? SystemFilters.fromJSON(object.systemFilters) : undefined,
      userFilters: isSet(object.userFilters) ? UserFilters.fromJSON(object.userFilters) : undefined,
      createdAt: isSet(object.createdAt) ? globalThis.Number(object.createdAt) : 0,
      updatedAt: isSet(object.updatedAt) ? globalThis.Number(object.updatedAt) : 0,
    };
  },

  toJSON(message: CardFilters): unknown {
    const obj: any = {};
    if (message.Id !== undefined) {
      obj.Id = ObjectId.toJSON(message.Id);
    }
    if (message.phoneNumber !== "") {
      obj.phoneNumber = message.phoneNumber;
    }
    if (message.cardType !== "") {
      obj.cardType = message.cardType;
    }
    if (message.cardId !== "") {
      obj.cardId = message.cardId;
    }
    if (message.priority !== 0) {
      obj.priority = Math.round(message.priority);
    }
    if (message.systemFilters !== undefined) {
      obj.systemFilters = SystemFilters.toJSON(message.systemFilters);
    }
    if (message.userFilters !== undefined) {
      obj.userFilters = UserFilters.toJSON(message.userFilters);
    }
    if (message.createdAt !== 0) {
      obj.createdAt = Math.round(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      obj.updatedAt = Math.round(message.updatedAt);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CardFilters>, I>>(base?: I): CardFilters {
    return CardFilters.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CardFilters>, I>>(object: I): CardFilters {
    const message = createBaseCardFilters();
    message.Id = (object.Id !== undefined && object.Id !== null) ? ObjectId.fromPartial(object.Id) : undefined;
    message.phoneNumber = object.phoneNumber ?? "";
    message.cardType = object.cardType ?? "";
    message.cardId = object.cardId ?? "";
    message.priority = object.priority ?? 0;
    message.systemFilters = (object.systemFilters !== undefined && object.systemFilters !== null)
      ? SystemFilters.fromPartial(object.systemFilters)
      : undefined;
    message.userFilters = (object.userFilters !== undefined && object.userFilters !== null)
      ? UserFilters.fromPartial(object.userFilters)
      : undefined;
    message.createdAt = object.createdAt ?? 0;
    message.updatedAt = object.updatedAt ?? 0;
    return message;
  },
};

function createBaseUserDefaults(): UserDefaults {
  return {
    Id: undefined,
    phoneNumber: "",
    userGroupFilters: undefined,
    accountFilters: undefined,
    createdAt: 0,
    updatedAt: 0,
  };
}

export const UserDefaults: MessageFns<UserDefaults> = {
  encode(message: UserDefaults, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.Id !== undefined) {
      ObjectId.encode(message.Id, writer.uint32(10).fork()).join();
    }
    if (message.phoneNumber !== "") {
      writer.uint32(18).string(message.phoneNumber);
    }
    if (message.userGroupFilters !== undefined) {
      UserGroupFilters.encode(message.userGroupFilters, writer.uint32(26).fork()).join();
    }
    if (message.accountFilters !== undefined) {
      AccountsFilter.encode(message.accountFilters, writer.uint32(34).fork()).join();
    }
    if (message.createdAt !== 0) {
      writer.uint32(40).int64(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      writer.uint32(48).int64(message.updatedAt);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserDefaults {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserDefaults();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.Id = ObjectId.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.phoneNumber = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.userGroupFilters = UserGroupFilters.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.accountFilters = AccountsFilter.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.createdAt = longToNumber(reader.int64());
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.updatedAt = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserDefaults {
    return {
      Id: isSet(object.Id) ? ObjectId.fromJSON(object.Id) : undefined,
      phoneNumber: isSet(object.phoneNumber) ? globalThis.String(object.phoneNumber) : "",
      userGroupFilters: isSet(object.userGroupFilters) ? UserGroupFilters.fromJSON(object.userGroupFilters) : undefined,
      accountFilters: isSet(object.accountFilters) ? AccountsFilter.fromJSON(object.accountFilters) : undefined,
      createdAt: isSet(object.createdAt) ? globalThis.Number(object.createdAt) : 0,
      updatedAt: isSet(object.updatedAt) ? globalThis.Number(object.updatedAt) : 0,
    };
  },

  toJSON(message: UserDefaults): unknown {
    const obj: any = {};
    if (message.Id !== undefined) {
      obj.Id = ObjectId.toJSON(message.Id);
    }
    if (message.phoneNumber !== "") {
      obj.phoneNumber = message.phoneNumber;
    }
    if (message.userGroupFilters !== undefined) {
      obj.userGroupFilters = UserGroupFilters.toJSON(message.userGroupFilters);
    }
    if (message.accountFilters !== undefined) {
      obj.accountFilters = AccountsFilter.toJSON(message.accountFilters);
    }
    if (message.createdAt !== 0) {
      obj.createdAt = Math.round(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      obj.updatedAt = Math.round(message.updatedAt);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserDefaults>, I>>(base?: I): UserDefaults {
    return UserDefaults.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserDefaults>, I>>(object: I): UserDefaults {
    const message = createBaseUserDefaults();
    message.Id = (object.Id !== undefined && object.Id !== null) ? ObjectId.fromPartial(object.Id) : undefined;
    message.phoneNumber = object.phoneNumber ?? "";
    message.userGroupFilters = (object.userGroupFilters !== undefined && object.userGroupFilters !== null)
      ? UserGroupFilters.fromPartial(object.userGroupFilters)
      : undefined;
    message.accountFilters = (object.accountFilters !== undefined && object.accountFilters !== null)
      ? AccountsFilter.fromPartial(object.accountFilters)
      : undefined;
    message.createdAt = object.createdAt ?? 0;
    message.updatedAt = object.updatedAt ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
