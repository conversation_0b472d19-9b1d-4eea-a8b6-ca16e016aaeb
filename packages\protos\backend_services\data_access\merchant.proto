syntax = "proto3";

package backend_services.data_access;

import "database/transaction_category.proto";
import "database/custom_type.proto";

service Merchant {
    rpc FindMerchantsForTagging(FindMerchantsForTaggingRequest) returns (FindMerchantsForTaggingResponse) {};
    rpc FindMerchantsForSearch(FindMerchantsForSearchRequest) returns (FindMerchantsForSearchResponse) {};
    rpc InsertMerchants(InsertMerchantsRequest) returns (InsertMerchantsResponse) {};
    rpc GetUserMerchants(GetUserMerchantsRequest) returns (GetUserMerchantsResponse) {};
    rpc GetMerchantsByNames(GetMerchantsByNamesRequest) returns (GetMerchantsByNamesResponse) {};
    rpc GetMerchantsByIds(GetMerchantsByIdsRequest) returns (GetMerchantsByIdsResponse) {};
    rpc DeleteUserMerchant(DeleteUserMerchantRequest) returns (DeleteUserMerchantResponse) {};
    rpc FindMerchantsForSearchSuggestions(FindMerchantsForSearchSuggestionsRequest) returns (FindMerchantsForSearchSuggestionsResponse) {};
}

message FindMerchantsForTaggingRequest {
    repeated string keywords = 1;
}

message FindMerchantsForTaggingResponse {

    repeated Record records = 1;
    repeated Tag tags = 2;

    message Record {
        Merchant merchant = 1;
        repeated database.Highlight highlights = 2;
    }

    message Merchant {
        string id = 1;
        string name = 2;
        repeated string tags_ids = 3;
    }

    message Tag {
        string id = 1;
        string debit_category = 2;
        string debit_subcategory = 3;
        string credit_category = 4;
        string credit_subcategory = 5;
    }
}

message InsertMerchantsRequest {
    repeated database.Merchant records = 1;
}
message InsertMerchantsResponse{
    repeated database.ObjectId merchant_ids = 1;
}

message GetUserMerchantsRequest{
    string auth_user_id = 1;
}
message GetUserMerchantsResponse{
    repeated database.Merchant records = 1;
}

message GetMerchantsByNamesRequest{
    string auth_user_id = 1;
    repeated string name = 2;
}
message GetMerchantsByNamesResponse{
    repeated database.Merchant records = 1;
}

message GetMerchantsByIdsRequest {
    repeated database.ObjectId merchant_ids = 1;
}
message GetMerchantsByIdsResponse {
    repeated database.Merchant records = 1;
}

message DeleteUserMerchantRequest{
    database.ObjectId merchant_id = 1;
}
message DeleteUserMerchantResponse{}

message FindMerchantsForSearchRequest {
    string keyword = 1;
    string auth_user_id = 2; // optional. If provided, the search will be done on user & global collection
    bool include_tags = 3; // If true, the response will include unique tags
}

message FindMerchantsForSearchResponse {

    repeated Record records = 1;
    repeated Tag tags = 2;

    message Record {
        Merchant merchant = 1;
        repeated database.Highlight highlights = 2;
    }

    message Merchant {
        string id = 1;
        string name = 2;
        repeated string tags_ids = 3;
    }

    message Tag {
        string id = 1;
        string debit_category = 2;
        string debit_subcategory = 3;
        string credit_category = 4;
        string credit_subcategory = 5;
    }
}

message FindMerchantsForSearchSuggestionsRequest {
    string keyword = 1;
    string auth_user_id = 2; // optional. If provided, the search will be done on user & global collection
}

message FindMerchantsForSearchSuggestionsResponse {
    repeated database.Merchant merchants = 1;
}