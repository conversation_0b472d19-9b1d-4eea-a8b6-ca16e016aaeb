syntax = "proto3";
package backend_services.data_access.public_data;
import "database/public_data.proto";

service PublicDataMutualFundNAV {
    rpc AddHistoricMutualFundNAV(HistoricalMutualFundNAVRequest) returns (HistoricalMutualFundNAVResponse){};
    rpc AddDailyMutualFundNAV(DailyNAVRequest) returns (DailyNAVResponse){};
    rpc GetMutualFundNAV(GetMutualFundNAVRequest) returns (GetMutualFundNAVResponse){};
    rpc GetMutualFundNAVs(GetMutualFundNAVsRequest) returns (GetMutualFundNAVsResponse) {};
}


message HistoricalMutualFundNAVRequest {
    string isin = 1;
    map<int64, string> datewise_nav= 2; 
}
message HistoricalMutualFundNAVResponse {}


message DailyNAVRequest{
    int64 date_timestamp = 1;
    map<string, string> isin_wise_nav = 2;
}
message DailyNAVResponse{}


message GetMutualFundNAVRequest{
    string isin = 1;
    int64 nav_date = 2;
}
message GetMutualFundNAVResponse{
    database.MutualFundNAV record = 1;
}

message GetMutualFundNAVsRequest {
    repeated string isin = 1;
    int64 from_date = 2;
    int64 to_date = 3;
}
message GetMutualFundNAVsResponse {
    repeated database.MutualFundNAV records = 1; 
}
