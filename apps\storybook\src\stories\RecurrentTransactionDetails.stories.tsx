import {
  creditCategories,
  debitCategories,
  dummyAccount,
  dummyRecurrentTransactions,
  RecurrentTransactionDetails,
} from "@repo/ui";
import { Meta, StoryObj } from "@storybook/react";

// Note: With keepCase: false in gRPC config, data should already be in camelCase
// If dummy data is still in snake_case, it should be updated to camelCase
const dummyCategories = [...debitCategories, ...creditCategories];
const handleClose = () => console.log("Close clicked");
const handleExcludedCashflowChanged = (groupId: string, flag: boolean) =>
  console.log(`Excluded cashflow changed for ${groupId}: ${flag}`);
const handleSavedChanged = (groupId: string, flag: boolean) =>
  console.log(`Saved status changed for ${groupId}: ${flag}`);
const handleNotesChanged = (groupId: string, notes: string) =>
  console.log(`Notes changed for ${groupId}: ${notes}`);
const handleMarkAsCompleted = (groupId: string) =>
  console.log(`Mark as completed clicked for ${groupId}`);
const handleDelete = (groupId: string) =>
  console.log(`Delete clicked for ${groupId}`);
const handleAddEditTagClick = (transaction: any) =>
  console.log(
    `Add/Edit tag clicked for ${transaction.txnCard.recurrentGroupId}`,
  );
const handleCompleteTransaction = (txnId: string) =>
  console.log(`Complete transaction clicked for ${txnId}`);
const handleDeleteTransaction = (txnId: string) =>
  console.log(`Delete transaction clicked for ${txnId}`);

const meta: Meta<typeof RecurrentTransactionDetails> = {
  title: "Recurrent Transaction/Recurrent Transaction Details",
  component: RecurrentTransactionDetails,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
  },
  decorators: [
    (Story) => (
      <div className="w-[500px]">
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    recurrentData: dummyRecurrentTransactions,
    accounts: dummyAccount.accounts,
    categories: dummyCategories,
    onClose: handleClose,
    onExcludedCashflowChanged: handleExcludedCashflowChanged,
    onSavedChanged: handleSavedChanged,
    onNotesChanged: handleNotesChanged,
    onMarkAsCompleted: handleMarkAsCompleted,
    onDelete: handleDelete,
    onAddEditTagClick: handleAddEditTagClick,
    onCompleteTransaction: handleCompleteTransaction,
    onDeleteTransaction: handleDeleteTransaction,
  },
};

export const NoMissedTransactions: Story = {
  args: {
    recurrentData: {
      ...dummyRecurrentTransactions,
      txns: dummyRecurrentTransactions.txns.filter(
        (txn: any) => txn.recurrentTxnType !== "RECURRENT_TXN_TYPE_MISSED"
      ),
    },
    accounts: dummyAccount.accounts,
    categories: dummyCategories,
    onClose: handleClose,
    onExcludedCashflowChanged: handleExcludedCashflowChanged,
    onSavedChanged: handleSavedChanged,
    onNotesChanged: handleNotesChanged,
    onMarkAsCompleted: handleMarkAsCompleted,
    onDelete: handleDelete,
    onAddEditTagClick: handleAddEditTagClick,
    onCompleteTransaction: handleCompleteTransaction,
    onDeleteTransaction: handleDeleteTransaction,
  },
};

export const WithEditModeEnabled: Story = {
  args: {
    recurrentData: dummyRecurrentTransactions,
    accounts: dummyAccount.accounts,
    categories: dummyCategories,
    onClose: handleClose,
    onExcludedCashflowChanged: handleExcludedCashflowChanged,
    onSavedChanged: handleSavedChanged,
    onNotesChanged: handleNotesChanged,
    onMarkAsCompleted: handleMarkAsCompleted,
    onDelete: handleDelete,
    onAddEditTagClick: handleAddEditTagClick,
    onCompleteTransaction: handleCompleteTransaction,
    onDeleteTransaction: handleDeleteTransaction,
    defaultEditMode: {
      upcoming: true,
      missed: true,
      history: true,
    },
  },
};
