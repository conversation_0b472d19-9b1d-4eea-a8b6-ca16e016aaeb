import { AmountRangeFilter } from "@repo/ui";
import type { Meta, StoryObj } from "@storybook/react";

const meta: Meta<typeof AmountRangeFilter> = {
  title: "Filters/AmountRangeFilter",
  component: AmountRangeFilter,
  tags: ["autodocs"],
  args: {
    values: [0, 1000000],
    onValueChange: () => {},
  },
  decorators: [
    (Story) => (
      <div style={{ width: "350px" }}>
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof AmountRangeFilter>;

export const Default: Story = {};
