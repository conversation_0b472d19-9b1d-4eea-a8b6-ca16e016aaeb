import { Metadata } from "next";
import React from "react";
import Login from "../components/Login";
import { cookies } from "next/headers";
import { CONSTANTS } from "../lib/constants";

export const metadata: Metadata = {
  title: "Login | CuspWeb",
};

export default async function Page() {
  const cookiesStore = await cookies();

  const session =
    cookiesStore.get(CONSTANTS.SESSION_COOKIE_NAME)?.value || null;

  return (
    <div className="h-full w-full">
      <Login session={session} />
    </div>
  );
}
