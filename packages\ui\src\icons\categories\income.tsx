import * as React from "react";
import type { SVGProps } from "react";
const SvgIncome = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="currentColor"
    viewBox="0 0 30 31"
    {...props}
  >
    <path d="M29.536 21.013c-.305-.528-1.111-1.246-2.198-.595l-5.831 3.491a3.22 3.22 0 0 1-1.102 2.114 3.08 3.08 0 0 1-2.007.757q-.374 0-.746-.092l-7.619-1.895a.75.75 0 0 1-.54-.907.734.734 0 0 1 .89-.55l7.62 1.895a1.61 1.61 0 0 0 1.453-.354c.406-.349.62-.844.592-1.364l-.002-.021a1.62 1.62 0 0 0-1.211-1.46l-3.902-.97a.7.7 0 0 1-.27-.132l-2.72-2.094c-3.234-2.52-6.871-2.46-11.416.194l1.74 7.143 12.54 3.774c1.321.397 2.594.237 3.783-.474l10.387-6.218q.664-.399.788-1.046c.07-.383-.013-.819-.23-1.196M19.548 15.528c4.03 0 7.314-3.28 7.314-7.315 0-4.032-3.283-7.316-7.314-7.316-4.034 0-7.317 3.284-7.317 7.316 0 4.037 3.283 7.315 7.317 7.315" />
  </svg>
);
export default SvgIncome;
