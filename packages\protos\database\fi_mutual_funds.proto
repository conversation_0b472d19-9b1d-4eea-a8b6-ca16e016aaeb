syntax = "proto3";
package database;

import "rebit/fi_schemas/mutual_funds/mutual_funds.proto";
import "database/custom_type.proto";

// flag:is-collection=true;
message FiMutualFundsAccountSummary {
    ObjectId _id = 1;

    // FIP Identifier
    string fip_id = 2;

    // Financial information type
    string fi_type = 3;

    // Masked Demat ID assigned or issued to investor
    string masked_demat_id = 5;

    // Masked Folio Number of Investor
    string masked_folio_no = 6;

    // Unique account number associated with aa account. 
    string linked_acc_ref = 7;
  
    rebit.fi_schemas.mutual_funds.Profile profile = 8;

    rebit.fi_schemas.mutual_funds.Summary summary = 9;
    
    // The date till which the transactions are reflected in the summary.
    string holdings_date = 10;

    // Document creation timestamp
    int64 created_at = 11;

    // Document modification timestamp
    int64 updated_at = 12;

    // This value should never be updated, this value will be used as a reference point to recreate the gains
    FiMutualFundsAccountInitialSummary initial_summary = 13;

}

message FiMutualFundsAccountInitialSummary {
    rebit.fi_schemas.mutual_funds.Summary summary = 1;
    int64 last_transaction_day_timestamp = 2; // @gotags: bson:"last_transaction_day_timestamp"
}

// flag:is-collection=true;
message FiMutualFundsAccountTransaction {
    ObjectId _id = 1;  

    // FIP Identifier
    string fip_id = 2;

    // Financial information type
    string fi_type = 3;

    // _id of account summary record in database
    ObjectId account_id = 5; 

    // Masked demat id
    string masked_demat_id = 6;

    // Masked Folio Number of Investor
    string masked_folio_no = 7;
  
    // Details of all transactions that have been posted in an account.
    rebit.fi_schemas.mutual_funds.Transaction transaction = 8;
  
    // Document creation timestamp
    int64 created_at = 9;

    // Document modification timestamp
    int64 updated_at = 10;
}


// flag:is-collection=true;
// Realized, unrealized and unknown gains
message MutualFundsGain {
    ObjectId _id = 1;  
    ObjectId account_id = 2; 
    string isin = 4;
    string units = 5;

    string buy_nav = 6;
    int64 buy_timestamp = 7;

    string sell_nav = 8;
    int64 sell_timestamp = 9;

    int64 created_at = 10;
    int64 updated_at = 11;

    int64 last_processed_transaction_day_timestamp = 12; // @gotags: bson:"last_processed_transaction_day_timestamp"
}

// flag:is-collection=true;
message DeletedFiMutualFundsAccountSummary {}

// flag:is-collection=true;
message DeletedFiMutualFundsAccountTransaction {}