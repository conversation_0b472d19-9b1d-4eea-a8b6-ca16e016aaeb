"use client";

import { useToast } from "../../hooks/use-toast";
import { Toast, ToastProvider, ToastViewport } from "./toast";

export function Toaster() {
  const { toasts } = useToast();

  return (
    <ToastProvider>
      {toasts.map(({ id, description, ...props }) => {
        return <Toast key={id} description={description} {...props} />;
      })}
      <ToastViewport />
    </ToastProvider>
  );
}
