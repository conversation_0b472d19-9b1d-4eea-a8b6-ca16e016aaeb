import * as React from "react";
import type { SVGProps } from "react";
const SvgAdd = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="currentColor"
    viewBox="0 0 24 24"
    {...props}
  >
    <path
      fillRule="evenodd"
      d="M11.499.809c2.952 0 5.624 1.196 7.559 3.13a10.65 10.65 0 0 1 3.131 7.56c0 2.952-1.197 5.624-3.131 7.559a10.65 10.65 0 0 1-7.56 3.131 10.66 10.66 0 0 1-7.558-3.131 10.65 10.65 0 0 1-3.131-7.56c0-2.952 1.196-5.623 3.13-7.558A10.65 10.65 0 0 1 11.5.809m-5.61 10.189h5.109V5.889a.501.501 0 0 1 1.002 0v5.109h5.108a.501.501 0 0 1 0 1.002H12v5.108a.5.5 0 1 1-1.002 0V12H5.889a.5.5 0 1 1 0-1.002"
      clipRule="evenodd"
    />
  </svg>
);
export default SvgAdd;
