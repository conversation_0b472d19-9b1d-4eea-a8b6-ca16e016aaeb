import * as React from "react";
import type { SVGProps } from "react";
const SvgCalendar = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="none"
    viewBox="0 0 17 17"
    {...props}
  >
    <g
      stroke="#EBE0F4"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={1.5}
      clipPath="url(#calendar_svg__a)"
    >
      <path d="M13.15 3.488H3.817c-.737 0-1.334.597-1.334 1.334v9.333c0 .736.597 1.333 1.334 1.333h9.333c.736 0 1.333-.597 1.333-1.333V4.822c0-.737-.597-1.334-1.333-1.334M11.15 2.156v2.667M5.817 2.156v2.667M2.483 7.488h12" />
    </g>
    <defs>
      <clipPath id="calendar_svg__a">
        <path fill="#fff" d="M.483.822h16v16h-16z" />
      </clipPath>
    </defs>
  </svg>
);
export default SvgCalendar;
