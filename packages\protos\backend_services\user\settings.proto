syntax = "proto3";
package backend_services.user;

import "database/cards_filters.proto";
import "database/core.proto";
import "database/settings.proto";

service Settings{
    rpc GetAccounts(GetAccountsRequest) returns (GetAccountsResponse) {};
    rpc GetDefaultFilters(GetDefaultFiltersRequest) returns (GetDefaultFiltersResponse) {};
    rpc SetDefaultFilters(SetDefaultFiltersRequest) returns (SetDefaultFiltersResponse) {};
    rpc SubmitFeedback(SubmitFeedbackRequest) returns (SubmitFeedbackResponse) {};
}


message Account {
    string account_id = 1;
    string linked_acc_ref = 2;
    string masked_acc_number = 3;
    string fip_id = 4;
    string holder_name = 5;
    string branch = 6;
    database.FiType fi_type = 7;
    string user_id = 8;
    int64 data_synced_at = 9;
}

message GetAccountsRequest{}
message GetAccountsResponse{
    repeated Account accounts = 1;
}


message GetDefaultFiltersRequest {}
message GetDefaultFiltersResponse {
    database.UserGroupFilters user_groups = 1;
    database.AccountsFilter account_filters = 2;   
}


message SetDefaultFiltersRequest {
    database.UserGroupFilters user_groups = 1;
    database.AccountsFilter account_filters = 2; 
}
message SetDefaultFiltersResponse {}


message SubmitFeedbackRequest {
   database.UserFeedback user_feedback = 1;
}
message SubmitFeedbackResponse {}
