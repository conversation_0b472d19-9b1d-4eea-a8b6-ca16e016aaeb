"use server";

import { revalidatePath } from "next/cache";
import { cookies } from "next/headers";
import {
  assignCategoryToTransaction,
  deleteTransactionDocumentsRPC,
  excludeTransactionFromCashFlow,
  fetchTransactionDocumentsRPC,
  saveTransaction,
  searchTransactionRPC,
  updateTransactionNotes,
} from "../lib/transaction";
import { CONSTANTS, ROUTE } from "../lib/constants";
import {
  ExcludecashFlowPayload,
  FetchDocuments,
  NotesPayload,
  SaveTxnPayload,
  TransactionActionFunction,
} from "@repo/ui";
import { SearchBy, Transaction } from "../lib/types/transaction.types";

const TransactionActionTypes = {
  EXCLUDE_CASH_FLOW: "EXCLUDE_CASH_FLOW",
  SAVE_TRANSACTION: "SAVE_TRANSACTION",
  NOTES: "NOTES",
} as const;

export async function updateTransactionCategory(
  transactionId: string,
  categoryId: string,
  subCategoryId: string,
) {
  try {
    const cookiesStore = await cookies();
    const authToken: string =
      cookiesStore.get(CONSTANTS.SESSION_COOKIE_NAME)?.value || "";

    const response = await assignCategoryToTransaction(
      authToken,
      transactionId,
      categoryId,
      subCategoryId,
    );
    if (response) {
      revalidatePath(ROUTE.DASHBOARD);
    }
  } catch (error) {
    console.error("gRPC Error:", error);
  }
}

export const updateTransaction: TransactionActionFunction = async (
  action,
  payload,
) => {
  switch (action) {
    case TransactionActionTypes.SAVE_TRANSACTION:
      updateSaveTransaction(payload as SaveTxnPayload);
      revalidatePath(ROUTE.DASHBOARD);
      break;
    case TransactionActionTypes.EXCLUDE_CASH_FLOW:
      updateExcludeCashFlow(payload as ExcludecashFlowPayload);
      revalidatePath(ROUTE.DASHBOARD);
      break;
    case TransactionActionTypes.NOTES:
      updateNotes(payload as NotesPayload);
      revalidatePath(ROUTE.DASHBOARD);
      break;
    default:
      console.error(`Unsupported action type: ${action}`);
  }
};

export async function updateSaveTransaction(payload: SaveTxnPayload) {
  try {
    const cookiesStore = await cookies();
    const authToken: string =
      cookiesStore.get(CONSTANTS.SESSION_COOKIE_NAME)?.value || "";

    return await saveTransaction(
      authToken,
      payload.transactionId,
      payload.isSaved,
    );
  } catch (error) {
    console.error("gRPC Error:", error);
  }
}

export async function updateExcludeCashFlow(payload: ExcludecashFlowPayload) {
  try {
    const cookiesStore = await cookies();
    const authToken: string =
      cookiesStore.get(CONSTANTS.SESSION_COOKIE_NAME)?.value || "";

    return await excludeTransactionFromCashFlow(
      authToken,
      payload.transactionId,
      payload.isExcludedFromCashFlow,
    );
  } catch (error) {
    console.error("gRPC Error:", error);
  }
}

export async function updateNotes(payload: NotesPayload) {
  try {
    const cookiesStore = await cookies();
    const authToken: string =
      cookiesStore.get(CONSTANTS.SESSION_COOKIE_NAME)?.value || "";

    const response = await updateTransactionNotes(
      authToken,
      payload.transactionId,
      payload.notes,
    );
  } catch (error) {
    console.error("gRPC Error:", error);
  }
}

export async function fetchTransactionDocuments(
  transactionId: string,
): Promise<FetchDocuments> {
  try {
    const cookiesStore = await cookies();
    const authToken: string =
      cookiesStore.get(CONSTANTS.SESSION_COOKIE_NAME)?.value || "";

    const response = await fetchTransactionDocumentsRPC(authToken, transactionId);
    return response;
  } catch (error) {
    console.error("gRPC Error:", error);
    return { documents: [] };
  }
}

export async function deleteTransactionDocuments(
  transactionId: string,
  fileNames: string[],
): Promise<{ ok: boolean }> {
  try {
    const cookiesStore = await cookies();
    const authToken: string =
      cookiesStore.get(CONSTANTS.SESSION_COOKIE_NAME)?.value || "";

    return await deleteTransactionDocumentsRPC(
      authToken,
      transactionId,
      fileNames,
    );
  } catch (error) {
    console.error("gRPC Error:", error);
    return { ok: false };
  }
}

export async function searchTransaction(
  input: string,
  searchBy: SearchBy,
  pageNumber: number,
  pageSize: number,
  filters?: {
    timeRange?: { fromTime: number; toTime: number };
    amountRange?: { minAmount: number; maxAmount: number };
    transactionType?: string;
    tagStatus?: string;
    bookmarkOptions?: {
      showFavorites?: boolean;
      excludedFromCashflow?: boolean;
      withNotes?: boolean;
    };
  },
): Promise<{ cards: Transaction[] }> {
  try {
    const cookiesStore = await cookies();
    const authToken: string =
      cookiesStore.get(CONSTANTS.SESSION_COOKIE_NAME)?.value || "";

    return await searchTransactionRPC(
      authToken,
      input,
      searchBy,
      pageNumber,
      pageSize,
      filters,
    );
  } catch (error) {
    console.error("gRPC Error:", error);
    return { cards: [] };
  }
}
