"use client";
import { useEffect, useRef } from "react";
import {
  Command,
  CommandGroup,
  CommandItem,
  CommandInput,
  CommandList,
  CommandEmpty,
  CommandSeparator,
} from "./ui/command";
import { cn } from "../lib/utils";
import SvgAdd from "../icons/add";
import { categoryIconMap } from "./AddEditTag";

export const Autocomplete = ({
  query,
  options,
  open,
  onQueryChange,
  onOptionSelect,
  onClear,
  onCreateTag,
  onOpenChange,
  className,
  isCategory,
}: {
  query: string;
  options: string[];
  open: boolean;
  onQueryChange: (value: string) => void;
  onOptionSelect: (option: string) => void;
  onClear: () => void;
  onCreateTag?: () => void;
  onOpenChange: (open: boolean) => void;
  className?: string | undefined;
  isCategory?: boolean;
}) => {
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    // Blur the input when autocomplete is closed
    if (!open) inputRef.current?.blur();
  }, [open]);

  return (
    <Command
      className={cn(
        "overflow-visible relative border border-[#DAD9DC] rounded-lg bg-white text-[#1E1E1E]",
        open && "border-b-0 rounded-b-none",
        className,
      )}
      shouldFilter={false}
      onKeyDown={(e: { key: string }) => {
        if (e.key === "Escape") {
          onClear();
        }
      }}
    >
      <CommandInput
        placeholder="Search for tags"
        ref={inputRef}
        value={query}
        onValueChange={onQueryChange}
        onFocus={() => onOpenChange(true)}
        onBlur={() => {
          if (!query) onOpenChange(false);
        }}
      />

      <CommandList
        className={cn(
          "absolute top-full w-full rounded-b-lg border-x border-b border-[#DAD9DC] box-content -m-[1px] z-50",
          !open && "hidden",
        )}
      >
        {open && <CommandSeparator alwaysRender={true} />}

        {open && (
          <>
            {query.length > 0 && !options.includes(query) && (
              <CommandItem
                value={`create-${query}`}
                onSelect={onCreateTag}
                className="text-primary font-semibold leading-5 py-3"
              >
                <SvgAdd />
                <div className="flex flex-wrap gap-1">
                  <span className="break-all">"{query}" &nbsp;</span>
                  <span className="underline">Create a new tag</span>
                </div>
              </CommandItem>
            )}
            {options.length === 0 ? (
              <CommandEmpty>
                <div className="px-4 text-sm py-2 text-gray-500">
                  No results found. Create a new tag?
                </div>
              </CommandEmpty>
            ) : (
              <CommandGroup>
                {options.map((option) => (
                  <CommandItem
                    key={option}
                    value={option}
                    onMouseDown={(e) => e.preventDefault()}
                    onSelect={() => onOptionSelect(option)}
                  >
                    <div className="flex gap-2">
                      {isCategory ? (
                        <span>
                          {categoryIconMap[option]
                            ? categoryIconMap[option]
                            : "❓"}
                        </span>
                      ) : (
                        ""
                      )}
                      <span>{option}</span>
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            )}
          </>
        )}
      </CommandList>
    </Command>
  );
};
