syntax = "proto3";

package workflows;

import "database/core.proto";

message FiFetchAndProcessArgs {
    string consent_id = 1;
    int64 from = 2;
    FiFetchAndProcessConfig config = 3;
    repeated string processed_accounts_refs = 4;
    string first_fetch_session_id = 5;
}

message FiFetchAndProcessConfig {
    bool is_scheduled_run = 1;
    int32 run_window_start_hour = 2;
    int32 run_window_end_hour = 3;
}

message FiProcessArgs {
    database.FiType fi_type = 1;
    string consent_id = 2;
}

message FiProcessResponse {
}