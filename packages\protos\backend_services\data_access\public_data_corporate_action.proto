syntax = "proto3";
package backend_services.data_access.public_data;
import "database/public_data.proto";

service PublicDataCorporateAction {
    rpc AddEquityCorporateActionInfo(AddEquityCorporateActionRequest) returns (AddEquityCorporateActionResponse){};
    rpc GetEquityCorporateActionsInfo(GetEquityCorporateActionsRequest) returns (GetEquityCorporateActionsResponse){};
}

message AddEquityCorporateActionRequest{
    repeated database.EquityCorporateAction records = 1;
}

message AddEquityCorporateActionResponse{}

message GetEquityCorporateActionsRequest{
    string isin = 1;
}

message GetEquityCorporateActionsResponse{
    repeated database.EquityCorporateAction records = 1;
}
