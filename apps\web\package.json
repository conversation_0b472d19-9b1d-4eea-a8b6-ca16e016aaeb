{"name": "web", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@grpc/grpc-js": "^1.8.0", "@grpc/proto-loader": "^0.6.0", "@repo/protos": "workspace:*", "@repo/ui": "workspace:*", "date-fns": "^4.1.0", "firebase": "^11.3.1", "mongodb": "^6.1.3", "next": "^15", "react": "^18.3.1"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/tailwind-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^22.10.2", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "autoprefixer": "^10.4.20", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "typescript": "^5.7.3"}}