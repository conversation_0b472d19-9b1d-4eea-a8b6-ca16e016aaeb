import * as React from "react";
import type { SVGProps } from "react";
const SvgAxis = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="#AE285D"
    viewBox="0 0 24 22"
    width="1em"
    height="1em"
    {...props}
  >
    <path d="M13.137 2.629 12.006.694c-2.4 4.12-4.811 8.234-7.222 12.348Q2.512 16.919.243 20.799a6 6 0 0 0-.23.475l-.013.03c2.49.003 4.979.004 7.468-.01l.859-1.48c.477-.825.954-1.65 1.44-2.47 1.556-2.626 3.056-5.283 4.556-7.94q.675-1.2 1.355-2.398c-.84-1.464-1.69-2.92-2.542-4.377M13.664 13.515l-1.656.002a245 245 0 0 0 2.633 4.57c.625 1.069 1.251 2.138 1.865 3.213q.993 0 1.984.003c1.837.002 3.673.005 5.51-.004-.825-1.455-1.67-2.9-2.513-4.343a481 481 0 0 1-1.984-3.416c-1.946-.032-3.893-.029-5.839-.025" />
  </svg>
);
export default SvgAxis;
