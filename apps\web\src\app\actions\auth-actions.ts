"use server";

import { cookies } from "next/headers";
import { CONSTANTS, ROUTE } from "../lib/constants";
import { redirect } from "next/navigation";

export async function createSession(token: string) {
  const cookiesStore = await cookies();
  cookiesStore.set(CONSTANTS.SESSION_COOKIE_NAME, token);
  redirect(ROUTE.DASHBOARD);
}

export async function addPhoneNumber(number: string) {
  const cookiesStore = await cookies();
  cookiesStore.set(CONSTANTS.USER_PHONE_NUMBER, number);
}

export async function removeSession() {
  const cookiesStore = await cookies();
  cookiesStore.delete(CONSTANTS.SESSION_COOKIE_NAME);
  redirect(ROUTE.ROOT);
}
