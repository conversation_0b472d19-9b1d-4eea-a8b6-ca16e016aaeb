import { <PERSON>a, StoryObj } from "@storybook/react";
import { TransactionSearch } from "@repo/ui";

const meta: Meta<typeof TransactionSearch> = {
  title: "Components/Transaction Search",
  component: TransactionSearch,
  tags: ["autodocs"],
  globals: {
    backgrounds: {
      value: "lightgray",
    },
  },
  args: {
    onSearch: (query) => {
      console.log(query);
    },
    onSearchCategoryChange: (category) =>
      console.log("Selected badge:", category),
  },
};

export default meta;

type Story = StoryObj<typeof TransactionSearch>;

export const Default: Story = {};
