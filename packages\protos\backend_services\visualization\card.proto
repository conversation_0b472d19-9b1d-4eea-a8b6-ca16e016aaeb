syntax = "proto3";
package backend_services.visualization;

import "database/cards_filters.proto";
import "backend_services/visualization/shared.proto";

// Service for Cards Fetch
service Visualization{
    rpc FetchCards(FetchCardsRequest) returns (stream FetchCardsResponse) {};
    rpc ApplyUserFilters(ApplyUserFiltersRequest) returns (Card) {};
    rpc SaveUserFilters(SaveUserFiltersRequest) returns (SaveUserFiltersResponse) {};
    rpc UpdatePriorities(UpdatePrioritiesRequest) returns (UpdatePrioritiesResponse) {};
}


message ApplyUserFiltersRequest {
    string card_id = 1;
    string card_type = 2;
    database.SystemFilters system_filters = 3;
    database.UserFilters user_filters = 4;
}

message SaveUserFiltersRequest {
    string card_id = 1;
    string card_type = 2;
    int64 priority = 3;
    database.SystemFilters system_filters = 4;
    database.UserFilters user_filters = 5;
}

message SaveUserFiltersResponse {}

message UpdatePrioritiesRequest {
    message PriorityRequest {
        string card_id = 1;
        string card_type = 2;
        database.SystemFilters system_filters = 3;
        int64 priority = 4;
    }
    repeated PriorityRequest priority_requests = 1;
}

message UpdatePrioritiesResponse {}

message FetchCardsRequest {}

message Card {
    oneof card {
        BankBalanceCard bank_balance_card = 1;
        TotalWealthCard total_wealth_card = 2;
        BankAccountSummaryCard bank_account_summary_card = 3;
        EquitiesAccountSummaryCard equities_account_summary_card = 4;
        MutualFundsAccountSummaryCard mutual_funds_account_summary_card = 5;
        MutualFundsXIRRCard mutual_funds_xirr_card = 6;
        MutualFundsReturnsCard mutual_funds_returns_card = 7;
        EquitiesReturnsCard equities_returns_card = 8;
        EquitiesXIRRCard equities_xirr_card = 9;
    }
    string id = 10;
    string type  = 11;
    string shape = 12;
    int64 priority = 13;  
    database.SystemFilters system_filters = 14;
    database.UserFilters user_filters = 15;
}

message FetchCardsResponse {   
    repeated Card cards = 1;
}

message BankAccountSummaryCard {
    message CardDetails {
        string fip_id = 1;
        string title = 2;
        string sub_title = 3;
        double value = 4;
        double sub_value = 5; // 0.0 as no value
        string type = 6;
        int64 balance_date = 8;
    }
    CardDetails card_details = 1;
}

message EquitiesAccountSummaryCard {
    message CardDetails {
        string fip_id = 1;
        string title = 2;
        string sub_title = 3;
        double value = 4;
        double sub_value = 5; // 0.0 as no value
        string type = 6;
        int64 holdings_date = 8;
        repeated string error_info = 9;
    }
    CardDetails card_details = 1;
}

message LineChart {
    int64 start_time = 1;
    int64 end_time = 2;
    TimeStep time_step = 3;
    message YAxis {
        repeated double y = 1;
    }
    repeated YAxis list_y = 4;
    repeated string list_y_name = 5;
    bool smooth = 6;
}

message BarChart {
    repeated string x = 1;
    message YAxis {
        repeated double y = 1;
    }
    repeated YAxis list_y = 2;
    repeated string list_y_name = 3;
    bool show_value = 4;
}

message PieChart {
    repeated string labels = 1;
    repeated double values = 2;
    bool show_percent = 3;
    bool show_number = 4;
    bool label_placement = 5; // TODO: Discuss
}

message Chart {
    oneof chart {
        PieChart pie_chart = 1;
        LineChart line_chart = 2;
        BarChart bar_chart = 3;
    }
    string type = 4;
}


message BankBalanceCard {
    message CardDetails {
        string title = 1;
        double value = 2;
        Chart chart = 3;
        repeated string error_info = 4;
    }
    CardDetails card_details = 1;
}

message TotalWealthCard {
    message CardDetails {
        string title = 1;
        string sub_title = 2;
        Chart chart = 3;
        repeated string error_info = 4;
    }
    CardDetails card_details = 1;
}

message EquitiesXIRRCard {
    message CardDetails {
        string title = 1;
        string sub_title_1 = 2;
        double sub_value_1 = 3;
        string sub_title_2 = 4;
        double sub_value_2 = 5;
        string rate_title = 6;
        double rate = 7;
        Chart chart = 8;
        repeated string error_info = 9;
    }
    CardDetails card_details = 1;
}

message EquitiesReturnsCard {
    message CardDetails {
        string title = 1;
        repeated string sub_title = 2;
        repeated double value = 3;
        Chart chart = 4;
        repeated string error_info = 5;
    }
    CardDetails card_details = 1;
}

message MutualFundsAccountSummaryCard {
    message CardDetails {
        string fip_id = 1;
        string title = 2;
        string sub_title = 3;
        double value = 4;
        double sub_value = 5; // 0.0 as no value
        int64 holdings_date = 7;
        repeated string error_info = 8;
    }
    CardDetails card_details = 1;
}

message MutualFundsXIRRCard {
    message CardDetails {
        string title = 1;
        string sub_title_1 = 2;
        double sub_value_1 = 3;
        string sub_title_2 = 4;
        double sub_value_2 = 5;
        string rate_title = 6;
        double rate = 7;
        Chart chart = 8;
        repeated string error_info = 9;
    }
    CardDetails card_details = 1;
}

message MutualFundsReturnsCard {
    message CardDetails {
        string title = 1;
        repeated string sub_title = 2;
        repeated double value = 3;
        Chart chart = 4;
        repeated string error_info = 5;
    }
    CardDetails card_details = 1;
}
