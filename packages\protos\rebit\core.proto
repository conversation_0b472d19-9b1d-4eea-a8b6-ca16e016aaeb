syntax = "proto3";

package rebit;

import "rebit/fi_schemas/deposit/deposit.proto";
import "rebit/fi_schemas/equities/equities.proto";
import "rebit/fi_schemas/mutual_funds/mutual_funds.proto";

message FiAccountInfo {
    string account_ref_no = 1; // @gotags: json:"accountRefNo"
    string link_ref_no = 2; // @gotags: json:"linkRefNo"
}

message WrapOneOfFiAccountData {
    oneof fi_object {
        rebit.fi_schemas.deposit.Account deposit_account = 1; // @gotags: json:"depositAccount"
        rebit.fi_schemas.equities.Account equities_account = 2; // @gotags: json:"equitiesAccount"
        rebit.fi_schemas.mutual_funds.Account mutual_funds_account = 3; // @gotags: json:"mutualFundsAccount"
    }
}

message FiAccountData {
    WrapOneOfFiAccountData fi_object = 1; // @gotags: json:"fiObject"
    repeated FiAccountInfo fi_account_info = 2; // @gotags: json:"fiAccountInfo"
    string fip_id = 3; // @gotags: json:"fipId"
    string fip_name = 4; // @gotags: json:"fipName"
}

enum FiStatus {
    FiStatus_UNSPECIFIED = 0;
    FiStatus_READY = 1;
    FiStatus_DENIED = 2;
    FiStatus_PENDING = 3;
    FiStatus_DELIVERED = 4;
    FiStatus_TIMEOUT = 5;
}

message FipAccounts {
    string fipId = 1; // @gotags: json:"fipID"
    repeated FiStatusAccount accounts = 2; // @gotags: json:"Accounts"
}

message FiStatusAccount {
    string link_ref_number = 1; // @gotags: json:"linkRefNumber"
    rebit.FiStatus fi_status = 2; // @gotags: json:"FIStatus"
}

enum SessionStatus {
    SessionStatus_UNSPECIFIED = 0;
    SessionStatus_ACTIVE = 1;
    SessionStatus_COMPLETED = 2;
    SessionStatus_EXPIRED = 3;
    SessionStatus_FAILED = 4;
}

message FiStatusNotification {
    string session_id = 1; // @gotags: json:"sessionId"
    SessionStatus session_status = 2; // @gotags: json:"sessionStatus"
    repeated FipAccounts fiStatusResponse = 3; // @gotags: json:"FIStatusResponse"
}