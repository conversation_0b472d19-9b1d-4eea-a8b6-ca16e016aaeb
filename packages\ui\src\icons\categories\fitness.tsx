import * as React from "react";
import type { SVGProps } from "react";
const SvgFitness = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="currentColor"
    viewBox="0 0 30 30"
    {...props}
  >
    <path
      fillRule="evenodd"
      d="M28.639 21.697a2.94 2.94 0 0 0 0-4.16l-2.697-2.697a2.943 2.943 0 0 0-4.16 0l-7.32 7.32a2.943 2.943 0 0 0 0 4.16l2.697 2.697a2.94 2.94 0 0 0 4.16 0zm-8.975-6.199-4.544 4.545-4.784-4.785 4.544-4.544zM15.54 8.596a2.94 2.94 0 0 0 0-4.16L12.842 1.74a2.94 2.94 0 0 0-4.16 0l-7.32 7.32a2.94 2.94 0 0 0 0 4.16l2.697 2.697a2.94 2.94 0 0 0 4.158 0z"
      clipRule="evenodd"
    />
  </svg>
);
export default SvgFitness;
