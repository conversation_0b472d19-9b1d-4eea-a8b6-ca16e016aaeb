import * as React from "react";
import type { SVGProps } from "react";
const SvgCheck = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="none"
    viewBox="0 0 24 24"
    {...props}
  >
    <rect width={24} height={24} fill="#FFDCDC" rx={12} />
    <g stroke="#C52D2D" strokeLinecap="round" strokeLinejoin="round">
      <path d="M12.001 17.518a5.517 5.517 0 1 0 0-11.034 5.517 5.517 0 0 0 0 11.034M13.658 10.346l-3.31 3.31M10.348 10.346l3.31 3.31" />
    </g>
  </svg>
);
export default SvgCheck;
