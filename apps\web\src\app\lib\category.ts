import { Category, Subcategory } from "@repo/ui";
import connectToDatabase from "./mongodb";
import { ObjectId } from "mongodb";

interface DBCategory extends Category {
  _id?: ObjectId;
}
interface DBSubcategory extends Subcategory {
  _id?: ObjectId;
}
interface FetchCategoriesResponse {
  categories: Category[];
}
interface FetchSubcategoriesResponse {
  subcategories: Subcategory[];
}

const { db } = await connectToDatabase();

export async function getCategoriesWithSubCategories(): Promise<FetchCategoriesResponse> {
  const categories = (await db
    .collection("category")
    .aggregate([
      {
        $lookup: {
          from: "subcategory",
          let: { categoryUUID: "$CategoryId" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$$categoryUUID", "$CategoryId"],
                },
              },
            },
          ],
          as: "subcategories",
        },
      },
    ])
    .toArray()) as Category[];

  return { categories };
}

export async function getCategories(): Promise<FetchCategoriesResponse> {
  const categories = (await db
    .collection("category")
    .find(
      {},
      {
        projection: {
          Name: 1,
          CategoryId: { $ifNull: ["$CategoryId", ""] },
          CategoryType: 1,
          _id: 0,
        },
      },
    )
    .toArray()) as DBCategory[];
  return { categories };
}

export async function getSubCategories(): Promise<FetchSubcategoriesResponse> {
  const subcategories = (await db
    .collection("subcategory")
    .find({})
    .toArray()) as DBSubcategory[];
  return { subcategories };
}
