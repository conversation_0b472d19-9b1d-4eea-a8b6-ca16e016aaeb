import * as React from "react";
import type { SVGProps } from "react";
const SvgHousehold = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="currentColor"
    viewBox="0 0 36 34"
    {...props}
  >
    <path d="M25.83 12.933 13.675 5.724a1 1 0 0 0-1.013 0L.506 12.934c-.153.09-.28.218-.369.373A1.04 1.04 0 0 0 0 13.82v18.537c0 .273.107.535.297.728s.447.302.716.302h9.116c.269 0 .527-.109.717-.302s.296-.455.296-.728v-7.209h4.052v7.209c0 .273.107.535.297.728s.447.302.716.302h9.116c.269 0 .527-.109.716-.302.19-.193.297-.455.297-.728V13.819a1.04 1.04 0 0 0-.137-.512 1 1 0 0 0-.37-.374" />
    <g stroke="#C4A4DC" strokeLinecap="round" strokeWidth={2}>
      <path d="M29.91 10.89v2.577M29.91 4.343V6.92M27.926 8.905h-2.578M34.473 8.905h-2.578" />
    </g>
  </svg>
);
export default SvgHousehold;
