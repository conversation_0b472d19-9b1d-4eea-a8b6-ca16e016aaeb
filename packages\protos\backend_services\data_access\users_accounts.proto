syntax = "proto3";
package backend_services.data_access;

import "database/core.proto";
import "database/custom_type.proto";

service UserAccounts {	
	rpc AddAccountsInFamilyFoundInConsent(AddAccountsInFamilyFoundInConsentRequest) returns (AddAccountsInFamilyFoundInConsentResponse) {};
	rpc MapUserToAccountsOfFamily(MapUserToAccountsOfFamilyRequest) returns (MapUserToAccountsOfFamilyResponse) {};
	rpc GetAccountsManagedByConsent(GetAccountsManagedByConsentRequest) returns (GetAccountsManagedByConsentResponse) {};
	rpc GetFamilyAccounts(GetFamilyAccountsRequest) returns (GetFamilyAccountsResponse) {};
	rpc DeleteAccountInFamily(DeleteAccountInFamilyRequest) returns (DeleteAccountInFamilyResponse) {};
	rpc DeleteUserUnlinkedAccountsOfConsent(DeleteUserUnlinkedAccountsOfConsentRequest) returns (DeleteUserUnlinkedAccountsOfConsentResponse) {};
	rpc GetUnmappedFamilyConsentAccounts(GetUnmappedFamilyConsentAccountsRequest) returns (GetUnmappedFamilyConsentAccountsResponse) {};
    
	rpc UpsertUserAccounts(UpsertUserAccountsRequest) returns (UpsertUserAccountsResponse) {}
    rpc GetUsersAccounts(GetUsersAccountsRequest) returns (GetUsersAccountsResponse) {}; // TODO: We may not be using this
	rpc GetManagedAccountIdsByFiType(GetManagedAccountIdsByFiTypeArgs) returns (GetManagedAccountIdsByFiTypeResponse) {};
	rpc GetUserOfAccount(GetUserOfAccountRequest) returns (GetUserOfAccountResponse) {};
}

message AddAccountsInFamilyFoundInConsentRequest {
	message Account {
		string linked_acc_ref = 1;
		database.FiType fi_type = 2;
		string account_id = 3;
	}
	string family_id = 1;
	string consent_id = 2;
	repeated Account accounts = 3;
}
message AddAccountsInFamilyFoundInConsentResponse {}

message MapUserToAccountsOfFamilyRequest{
	string family_id = 1;
	repeated string account_ids = 2;
	string user_id = 3;
}
message MapUserToAccountsOfFamilyResponse{}

message DeleteAccountInFamilyRequest {
	string family_id = 1;
    repeated string account_ids = 2;
}
message DeleteAccountInFamilyResponse {}


message GetAccountsManagedByConsentRequest{
	string consent_id = 1;
}
message GetAccountsManagedByConsentResponse{
	repeated database.UserAccount accounts = 1;
}

message GetFamilyAccountsRequest{
	string family_id = 1;
}

message GetFamilyAccountsResponse{
	repeated database.UserAccount accounts = 1;
}

message UpsertUserAccountsRequest{
	repeated database.UserAccount accounts = 1;
}
message UpsertUserAccountsResponse{}

message GetUsersAccountsRequest{
	repeated string user_id = 1;
	repeated database.FiType fi_type = 2;
}
message GetUsersAccountsResponse{
	repeated database.UserAccount accounts = 1;
}

message DeleteUserUnlinkedAccountsOfConsentRequest{
	string consent_id =1;
}
message DeleteUserUnlinkedAccountsOfConsentResponse{

}

message GetUnmappedFamilyConsentAccountsRequest{
	string consent_id = 1;
	string family_id = 2;
}
message GetUnmappedFamilyConsentAccountsResponse {
	repeated string account_ids = 1;
}

message GetManagedAccountIdsByFiTypeArgs {
	string consent_id = 1;
	database.FiType fi_type = 2;
}

message GetManagedAccountIdsByFiTypeResponse {
	repeated database.ObjectId ids = 1;
}

message GetUserOfAccountRequest {
	database.ObjectId account_id = 1;
	database.FiType fi_type = 2;
}
message GetUserOfAccountResponse {
	database.ObjectId user_id = 1;
}