syntax = "proto3";
package backend_services.data_access;
import "database/cards_filters.proto";

service UserDefaults {
    rpc GetUserDefaults(GetUserDefaultsRequest) returns (GetUserDefaultsResponse) {};
    rpc UpdateUserDefaults(UpdateUserDefaultsRequest) returns (UpdateUserDefaultsResponse) {};
}


message GetUserDefaultsRequest{
    string phone_number = 1;
}

message GetUserDefaultsResponse {
    database.UserDefaults user_defaults = 1;
}

message UpdateUserDefaultsRequest{
    database.UserDefaults user_defaults = 1;
}

message UpdateUserDefaultsResponse {}

