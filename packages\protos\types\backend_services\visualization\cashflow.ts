// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.2
//   protoc               v6.31.1
// source: backend_services/visualization/cashflow.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import {
  type CallOptions,
  ChannelCredentials,
  Client,
  type ClientOptions,
  type ClientUnaryCall,
  type handleUnaryCall,
  makeGenericClientConstructor,
  Metadata,
  type ServiceError,
  type UntypedServiceImplementation,
} from "@grpc/grpc-js";
import { AccountsFilter, UserGroupFilters } from "../../database/cards_filters";
import { TxnFilterTimeRange } from "../../database/fi_deposit";
import {
  CategoryCashflow,
  FlowType,
  flowTypeFromJSON,
  flowTypeToJSON,
  OverallCashflow,
  TimeStep,
  timeStepFromJSON,
  timeStepToJSON,
} from "./shared";

export const protobufPackage = "backend_services.visualization";

export interface GetCashflowChartRequest {
  userGroups: UserGroupFilters | undefined;
  accountFilters: AccountsFilter | undefined;
  timeRange: TxnFilterTimeRange | undefined;
}

export interface CashflowChart {
  startTime: number;
  endTime: number;
  timeStep: TimeStep;
  yAxes: CashflowChart_YAxis[];
}

export interface CashflowChart_YAxis {
  flowType: FlowType;
  amount: number[];
}

export interface GetCashflowRequest {
  userGroups: UserGroupFilters | undefined;
  accountFilters: AccountsFilter | undefined;
  timeRange: TxnFilterTimeRange | undefined;
}

export interface GetCashflowResponse {
  chart: CashflowChart | undefined;
  overallCashflows: OverallCashflow[];
  categoryCashflows: CategoryCashflow[];
}

export interface GetSubcategoryCashflowRequest {
  userGroups: UserGroupFilters | undefined;
  accountFilters: AccountsFilter | undefined;
  timeRange: TxnFilterTimeRange | undefined;
  categoryCollection: string;
  categoryId: string;
  flowType: FlowType;
}

export interface GetSubcategoryCashflowResponse {
  cashFlow: SubcategoryCashflow[];
}

export interface SubcategoryCashflow {
  amount: number;
  percentage: number;
  subcategoryId: string;
}

function createBaseGetCashflowChartRequest(): GetCashflowChartRequest {
  return { userGroups: undefined, accountFilters: undefined, timeRange: undefined };
}

export const GetCashflowChartRequest: MessageFns<GetCashflowChartRequest> = {
  encode(message: GetCashflowChartRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userGroups !== undefined) {
      UserGroupFilters.encode(message.userGroups, writer.uint32(10).fork()).join();
    }
    if (message.accountFilters !== undefined) {
      AccountsFilter.encode(message.accountFilters, writer.uint32(18).fork()).join();
    }
    if (message.timeRange !== undefined) {
      TxnFilterTimeRange.encode(message.timeRange, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetCashflowChartRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetCashflowChartRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userGroups = UserGroupFilters.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.accountFilters = AccountsFilter.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.timeRange = TxnFilterTimeRange.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetCashflowChartRequest {
    return {
      userGroups: isSet(object.userGroups) ? UserGroupFilters.fromJSON(object.userGroups) : undefined,
      accountFilters: isSet(object.accountFilters) ? AccountsFilter.fromJSON(object.accountFilters) : undefined,
      timeRange: isSet(object.timeRange) ? TxnFilterTimeRange.fromJSON(object.timeRange) : undefined,
    };
  },

  toJSON(message: GetCashflowChartRequest): unknown {
    const obj: any = {};
    if (message.userGroups !== undefined) {
      obj.userGroups = UserGroupFilters.toJSON(message.userGroups);
    }
    if (message.accountFilters !== undefined) {
      obj.accountFilters = AccountsFilter.toJSON(message.accountFilters);
    }
    if (message.timeRange !== undefined) {
      obj.timeRange = TxnFilterTimeRange.toJSON(message.timeRange);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetCashflowChartRequest>, I>>(base?: I): GetCashflowChartRequest {
    return GetCashflowChartRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetCashflowChartRequest>, I>>(object: I): GetCashflowChartRequest {
    const message = createBaseGetCashflowChartRequest();
    message.userGroups = (object.userGroups !== undefined && object.userGroups !== null)
      ? UserGroupFilters.fromPartial(object.userGroups)
      : undefined;
    message.accountFilters = (object.accountFilters !== undefined && object.accountFilters !== null)
      ? AccountsFilter.fromPartial(object.accountFilters)
      : undefined;
    message.timeRange = (object.timeRange !== undefined && object.timeRange !== null)
      ? TxnFilterTimeRange.fromPartial(object.timeRange)
      : undefined;
    return message;
  },
};

function createBaseCashflowChart(): CashflowChart {
  return { startTime: 0, endTime: 0, timeStep: 0, yAxes: [] };
}

export const CashflowChart: MessageFns<CashflowChart> = {
  encode(message: CashflowChart, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.startTime !== 0) {
      writer.uint32(8).int64(message.startTime);
    }
    if (message.endTime !== 0) {
      writer.uint32(16).int64(message.endTime);
    }
    if (message.timeStep !== 0) {
      writer.uint32(24).int32(message.timeStep);
    }
    for (const v of message.yAxes) {
      CashflowChart_YAxis.encode(v!, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CashflowChart {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCashflowChart();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.startTime = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.endTime = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.timeStep = reader.int32() as any;
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.yAxes.push(CashflowChart_YAxis.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CashflowChart {
    return {
      startTime: isSet(object.startTime) ? globalThis.Number(object.startTime) : 0,
      endTime: isSet(object.endTime) ? globalThis.Number(object.endTime) : 0,
      timeStep: isSet(object.timeStep) ? timeStepFromJSON(object.timeStep) : 0,
      yAxes: globalThis.Array.isArray(object?.yAxes)
        ? object.yAxes.map((e: any) => CashflowChart_YAxis.fromJSON(e))
        : [],
    };
  },

  toJSON(message: CashflowChart): unknown {
    const obj: any = {};
    if (message.startTime !== 0) {
      obj.startTime = Math.round(message.startTime);
    }
    if (message.endTime !== 0) {
      obj.endTime = Math.round(message.endTime);
    }
    if (message.timeStep !== 0) {
      obj.timeStep = timeStepToJSON(message.timeStep);
    }
    if (message.yAxes?.length) {
      obj.yAxes = message.yAxes.map((e) => CashflowChart_YAxis.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CashflowChart>, I>>(base?: I): CashflowChart {
    return CashflowChart.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CashflowChart>, I>>(object: I): CashflowChart {
    const message = createBaseCashflowChart();
    message.startTime = object.startTime ?? 0;
    message.endTime = object.endTime ?? 0;
    message.timeStep = object.timeStep ?? 0;
    message.yAxes = object.yAxes?.map((e) => CashflowChart_YAxis.fromPartial(e)) || [];
    return message;
  },
};

function createBaseCashflowChart_YAxis(): CashflowChart_YAxis {
  return { flowType: 0, amount: [] };
}

export const CashflowChart_YAxis: MessageFns<CashflowChart_YAxis> = {
  encode(message: CashflowChart_YAxis, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.flowType !== 0) {
      writer.uint32(8).int32(message.flowType);
    }
    writer.uint32(18).fork();
    for (const v of message.amount) {
      writer.double(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CashflowChart_YAxis {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCashflowChart_YAxis();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.flowType = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag === 17) {
            message.amount.push(reader.double());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.amount.push(reader.double());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CashflowChart_YAxis {
    return {
      flowType: isSet(object.flowType) ? flowTypeFromJSON(object.flowType) : 0,
      amount: globalThis.Array.isArray(object?.amount) ? object.amount.map((e: any) => globalThis.Number(e)) : [],
    };
  },

  toJSON(message: CashflowChart_YAxis): unknown {
    const obj: any = {};
    if (message.flowType !== 0) {
      obj.flowType = flowTypeToJSON(message.flowType);
    }
    if (message.amount?.length) {
      obj.amount = message.amount;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CashflowChart_YAxis>, I>>(base?: I): CashflowChart_YAxis {
    return CashflowChart_YAxis.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CashflowChart_YAxis>, I>>(object: I): CashflowChart_YAxis {
    const message = createBaseCashflowChart_YAxis();
    message.flowType = object.flowType ?? 0;
    message.amount = object.amount?.map((e) => e) || [];
    return message;
  },
};

function createBaseGetCashflowRequest(): GetCashflowRequest {
  return { userGroups: undefined, accountFilters: undefined, timeRange: undefined };
}

export const GetCashflowRequest: MessageFns<GetCashflowRequest> = {
  encode(message: GetCashflowRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userGroups !== undefined) {
      UserGroupFilters.encode(message.userGroups, writer.uint32(10).fork()).join();
    }
    if (message.accountFilters !== undefined) {
      AccountsFilter.encode(message.accountFilters, writer.uint32(18).fork()).join();
    }
    if (message.timeRange !== undefined) {
      TxnFilterTimeRange.encode(message.timeRange, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetCashflowRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetCashflowRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userGroups = UserGroupFilters.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.accountFilters = AccountsFilter.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.timeRange = TxnFilterTimeRange.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetCashflowRequest {
    return {
      userGroups: isSet(object.userGroups) ? UserGroupFilters.fromJSON(object.userGroups) : undefined,
      accountFilters: isSet(object.accountFilters) ? AccountsFilter.fromJSON(object.accountFilters) : undefined,
      timeRange: isSet(object.timeRange) ? TxnFilterTimeRange.fromJSON(object.timeRange) : undefined,
    };
  },

  toJSON(message: GetCashflowRequest): unknown {
    const obj: any = {};
    if (message.userGroups !== undefined) {
      obj.userGroups = UserGroupFilters.toJSON(message.userGroups);
    }
    if (message.accountFilters !== undefined) {
      obj.accountFilters = AccountsFilter.toJSON(message.accountFilters);
    }
    if (message.timeRange !== undefined) {
      obj.timeRange = TxnFilterTimeRange.toJSON(message.timeRange);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetCashflowRequest>, I>>(base?: I): GetCashflowRequest {
    return GetCashflowRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetCashflowRequest>, I>>(object: I): GetCashflowRequest {
    const message = createBaseGetCashflowRequest();
    message.userGroups = (object.userGroups !== undefined && object.userGroups !== null)
      ? UserGroupFilters.fromPartial(object.userGroups)
      : undefined;
    message.accountFilters = (object.accountFilters !== undefined && object.accountFilters !== null)
      ? AccountsFilter.fromPartial(object.accountFilters)
      : undefined;
    message.timeRange = (object.timeRange !== undefined && object.timeRange !== null)
      ? TxnFilterTimeRange.fromPartial(object.timeRange)
      : undefined;
    return message;
  },
};

function createBaseGetCashflowResponse(): GetCashflowResponse {
  return { chart: undefined, overallCashflows: [], categoryCashflows: [] };
}

export const GetCashflowResponse: MessageFns<GetCashflowResponse> = {
  encode(message: GetCashflowResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.chart !== undefined) {
      CashflowChart.encode(message.chart, writer.uint32(10).fork()).join();
    }
    for (const v of message.overallCashflows) {
      OverallCashflow.encode(v!, writer.uint32(18).fork()).join();
    }
    for (const v of message.categoryCashflows) {
      CategoryCashflow.encode(v!, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetCashflowResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetCashflowResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.chart = CashflowChart.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.overallCashflows.push(OverallCashflow.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.categoryCashflows.push(CategoryCashflow.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetCashflowResponse {
    return {
      chart: isSet(object.chart) ? CashflowChart.fromJSON(object.chart) : undefined,
      overallCashflows: globalThis.Array.isArray(object?.overallCashflows)
        ? object.overallCashflows.map((e: any) => OverallCashflow.fromJSON(e))
        : [],
      categoryCashflows: globalThis.Array.isArray(object?.categoryCashflows)
        ? object.categoryCashflows.map((e: any) => CategoryCashflow.fromJSON(e))
        : [],
    };
  },

  toJSON(message: GetCashflowResponse): unknown {
    const obj: any = {};
    if (message.chart !== undefined) {
      obj.chart = CashflowChart.toJSON(message.chart);
    }
    if (message.overallCashflows?.length) {
      obj.overallCashflows = message.overallCashflows.map((e) => OverallCashflow.toJSON(e));
    }
    if (message.categoryCashflows?.length) {
      obj.categoryCashflows = message.categoryCashflows.map((e) => CategoryCashflow.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetCashflowResponse>, I>>(base?: I): GetCashflowResponse {
    return GetCashflowResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetCashflowResponse>, I>>(object: I): GetCashflowResponse {
    const message = createBaseGetCashflowResponse();
    message.chart = (object.chart !== undefined && object.chart !== null)
      ? CashflowChart.fromPartial(object.chart)
      : undefined;
    message.overallCashflows = object.overallCashflows?.map((e) => OverallCashflow.fromPartial(e)) || [];
    message.categoryCashflows = object.categoryCashflows?.map((e) => CategoryCashflow.fromPartial(e)) || [];
    return message;
  },
};

function createBaseGetSubcategoryCashflowRequest(): GetSubcategoryCashflowRequest {
  return {
    userGroups: undefined,
    accountFilters: undefined,
    timeRange: undefined,
    categoryCollection: "",
    categoryId: "",
    flowType: 0,
  };
}

export const GetSubcategoryCashflowRequest: MessageFns<GetSubcategoryCashflowRequest> = {
  encode(message: GetSubcategoryCashflowRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userGroups !== undefined) {
      UserGroupFilters.encode(message.userGroups, writer.uint32(10).fork()).join();
    }
    if (message.accountFilters !== undefined) {
      AccountsFilter.encode(message.accountFilters, writer.uint32(18).fork()).join();
    }
    if (message.timeRange !== undefined) {
      TxnFilterTimeRange.encode(message.timeRange, writer.uint32(26).fork()).join();
    }
    if (message.categoryCollection !== "") {
      writer.uint32(34).string(message.categoryCollection);
    }
    if (message.categoryId !== "") {
      writer.uint32(42).string(message.categoryId);
    }
    if (message.flowType !== 0) {
      writer.uint32(48).int32(message.flowType);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetSubcategoryCashflowRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetSubcategoryCashflowRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userGroups = UserGroupFilters.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.accountFilters = AccountsFilter.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.timeRange = TxnFilterTimeRange.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.categoryCollection = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.categoryId = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.flowType = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetSubcategoryCashflowRequest {
    return {
      userGroups: isSet(object.userGroups) ? UserGroupFilters.fromJSON(object.userGroups) : undefined,
      accountFilters: isSet(object.accountFilters) ? AccountsFilter.fromJSON(object.accountFilters) : undefined,
      timeRange: isSet(object.timeRange) ? TxnFilterTimeRange.fromJSON(object.timeRange) : undefined,
      categoryCollection: isSet(object.categoryCollection) ? globalThis.String(object.categoryCollection) : "",
      categoryId: isSet(object.categoryId) ? globalThis.String(object.categoryId) : "",
      flowType: isSet(object.flowType) ? flowTypeFromJSON(object.flowType) : 0,
    };
  },

  toJSON(message: GetSubcategoryCashflowRequest): unknown {
    const obj: any = {};
    if (message.userGroups !== undefined) {
      obj.userGroups = UserGroupFilters.toJSON(message.userGroups);
    }
    if (message.accountFilters !== undefined) {
      obj.accountFilters = AccountsFilter.toJSON(message.accountFilters);
    }
    if (message.timeRange !== undefined) {
      obj.timeRange = TxnFilterTimeRange.toJSON(message.timeRange);
    }
    if (message.categoryCollection !== "") {
      obj.categoryCollection = message.categoryCollection;
    }
    if (message.categoryId !== "") {
      obj.categoryId = message.categoryId;
    }
    if (message.flowType !== 0) {
      obj.flowType = flowTypeToJSON(message.flowType);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetSubcategoryCashflowRequest>, I>>(base?: I): GetSubcategoryCashflowRequest {
    return GetSubcategoryCashflowRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetSubcategoryCashflowRequest>, I>>(
    object: I,
  ): GetSubcategoryCashflowRequest {
    const message = createBaseGetSubcategoryCashflowRequest();
    message.userGroups = (object.userGroups !== undefined && object.userGroups !== null)
      ? UserGroupFilters.fromPartial(object.userGroups)
      : undefined;
    message.accountFilters = (object.accountFilters !== undefined && object.accountFilters !== null)
      ? AccountsFilter.fromPartial(object.accountFilters)
      : undefined;
    message.timeRange = (object.timeRange !== undefined && object.timeRange !== null)
      ? TxnFilterTimeRange.fromPartial(object.timeRange)
      : undefined;
    message.categoryCollection = object.categoryCollection ?? "";
    message.categoryId = object.categoryId ?? "";
    message.flowType = object.flowType ?? 0;
    return message;
  },
};

function createBaseGetSubcategoryCashflowResponse(): GetSubcategoryCashflowResponse {
  return { cashFlow: [] };
}

export const GetSubcategoryCashflowResponse: MessageFns<GetSubcategoryCashflowResponse> = {
  encode(message: GetSubcategoryCashflowResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.cashFlow) {
      SubcategoryCashflow.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetSubcategoryCashflowResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetSubcategoryCashflowResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.cashFlow.push(SubcategoryCashflow.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetSubcategoryCashflowResponse {
    return {
      cashFlow: globalThis.Array.isArray(object?.cashFlow)
        ? object.cashFlow.map((e: any) => SubcategoryCashflow.fromJSON(e))
        : [],
    };
  },

  toJSON(message: GetSubcategoryCashflowResponse): unknown {
    const obj: any = {};
    if (message.cashFlow?.length) {
      obj.cashFlow = message.cashFlow.map((e) => SubcategoryCashflow.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetSubcategoryCashflowResponse>, I>>(base?: I): GetSubcategoryCashflowResponse {
    return GetSubcategoryCashflowResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetSubcategoryCashflowResponse>, I>>(
    object: I,
  ): GetSubcategoryCashflowResponse {
    const message = createBaseGetSubcategoryCashflowResponse();
    message.cashFlow = object.cashFlow?.map((e) => SubcategoryCashflow.fromPartial(e)) || [];
    return message;
  },
};

function createBaseSubcategoryCashflow(): SubcategoryCashflow {
  return { amount: 0, percentage: 0, subcategoryId: "" };
}

export const SubcategoryCashflow: MessageFns<SubcategoryCashflow> = {
  encode(message: SubcategoryCashflow, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.amount !== 0) {
      writer.uint32(9).double(message.amount);
    }
    if (message.percentage !== 0) {
      writer.uint32(17).double(message.percentage);
    }
    if (message.subcategoryId !== "") {
      writer.uint32(34).string(message.subcategoryId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubcategoryCashflow {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubcategoryCashflow();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.amount = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.percentage = reader.double();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.subcategoryId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SubcategoryCashflow {
    return {
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      percentage: isSet(object.percentage) ? globalThis.Number(object.percentage) : 0,
      subcategoryId: isSet(object.subcategoryId) ? globalThis.String(object.subcategoryId) : "",
    };
  },

  toJSON(message: SubcategoryCashflow): unknown {
    const obj: any = {};
    if (message.amount !== 0) {
      obj.amount = message.amount;
    }
    if (message.percentage !== 0) {
      obj.percentage = message.percentage;
    }
    if (message.subcategoryId !== "") {
      obj.subcategoryId = message.subcategoryId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubcategoryCashflow>, I>>(base?: I): SubcategoryCashflow {
    return SubcategoryCashflow.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubcategoryCashflow>, I>>(object: I): SubcategoryCashflow {
    const message = createBaseSubcategoryCashflow();
    message.amount = object.amount ?? 0;
    message.percentage = object.percentage ?? 0;
    message.subcategoryId = object.subcategoryId ?? "";
    return message;
  },
};

/** Service for Cashflow */
export type CashflowService = typeof CashflowService;
export const CashflowService = {
  getCashflow: {
    path: "/backend_services.visualization.Cashflow/GetCashflow",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: GetCashflowRequest) => Buffer.from(GetCashflowRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => GetCashflowRequest.decode(value),
    responseSerialize: (value: GetCashflowResponse) => Buffer.from(GetCashflowResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => GetCashflowResponse.decode(value),
  },
  getSubcategoryCashflow: {
    path: "/backend_services.visualization.Cashflow/GetSubcategoryCashflow",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: GetSubcategoryCashflowRequest) =>
      Buffer.from(GetSubcategoryCashflowRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => GetSubcategoryCashflowRequest.decode(value),
    responseSerialize: (value: GetSubcategoryCashflowResponse) =>
      Buffer.from(GetSubcategoryCashflowResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => GetSubcategoryCashflowResponse.decode(value),
  },
} as const;

export interface CashflowServer extends UntypedServiceImplementation {
  getCashflow: handleUnaryCall<GetCashflowRequest, GetCashflowResponse>;
  getSubcategoryCashflow: handleUnaryCall<GetSubcategoryCashflowRequest, GetSubcategoryCashflowResponse>;
}

export interface CashflowClient extends Client {
  getCashflow(
    request: GetCashflowRequest,
    callback: (error: ServiceError | null, response: GetCashflowResponse) => void,
  ): ClientUnaryCall;
  getCashflow(
    request: GetCashflowRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: GetCashflowResponse) => void,
  ): ClientUnaryCall;
  getCashflow(
    request: GetCashflowRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: GetCashflowResponse) => void,
  ): ClientUnaryCall;
  getSubcategoryCashflow(
    request: GetSubcategoryCashflowRequest,
    callback: (error: ServiceError | null, response: GetSubcategoryCashflowResponse) => void,
  ): ClientUnaryCall;
  getSubcategoryCashflow(
    request: GetSubcategoryCashflowRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: GetSubcategoryCashflowResponse) => void,
  ): ClientUnaryCall;
  getSubcategoryCashflow(
    request: GetSubcategoryCashflowRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: GetSubcategoryCashflowResponse) => void,
  ): ClientUnaryCall;
}

export const CashflowClient = makeGenericClientConstructor(
  CashflowService,
  "backend_services.visualization.Cashflow",
) as unknown as {
  new (address: string, credentials: ChannelCredentials, options?: Partial<ClientOptions>): CashflowClient;
  service: typeof CashflowService;
  serviceName: string;
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
