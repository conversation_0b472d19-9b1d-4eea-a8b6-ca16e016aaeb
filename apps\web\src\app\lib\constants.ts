export const CONSTANTS = {
  SESSION_COOKIE_NAME: "user_session",
  USER_PHONE_NUMBER: "user_phone_number",
  PHONE_NUMBER_PREFIX: "+91",
  INPUT_OTP_MAX_LENGTH: 6,
};

export const ROUTE = {
  ROOT: "/",
  DASHBOARD: "/dashboard",
  LOGIN: "/login",
  LOGOUT_API: "/api/auth/logout",
};

export const TRANSACTION_UPDATE_ACTION = {
  TAG_CHANGE: "tag_change",
  RECIVER_CHANGE: "reciver_change",
};

export const GRPC_CONFIG = {
  PROTO_DIR_PATH: process.env.PROTO_DIR_PATH || "",
  GRPC_SERVER_URI: process.env.GRPC_SERVER_URI || "",
  OPTIONS: {
    keepCase: false,
    longs: String,
    enums: String,
    defaults: true,
    oneofs: true,
  },
  PROTO_FILES: {
    USER: "backend_services/user/user.proto",
    TRANSACTION: "backend_services/visualization/deposit_transaction.proto",
    SETTINGS: "backend_services/user/settings.proto"
  }
};
