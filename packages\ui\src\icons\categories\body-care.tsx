import * as React from "react";
import type { SVGProps } from "react";
const SvgBodyCare = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="currentColor"
    viewBox="0 0 23 38"
    {...props}
  >
    <path d="M2.782 35.74h12.241a.76.76 0 0 0 .758-.758V18.638a4.325 4.325 0 0 0-4.34-4.22H6.364a4.325 4.325 0 0 0-4.34 4.22v16.344a.76.76 0 0 0 .758.758M5.871 6.743h2.112v1.66H6.364v4.619h5.076v-4.62H9.821v-1.66h2.114a.277.277 0 0 0 .277-.277v-.466a11.5 11.5 0 0 0 3.733-.86c1.505-.56 2.17-.807 3.472.414a.28.28 0 0 0 .39-.002l.906-.907a.277.277 0 0 0-.004-.397c-2.168-2.035-3.813-1.423-5.405-.83a9.9 9.9 0 0 1-3.092.744v-.157c0-1.8-6.618-1.8-6.618 0v2.461a.28.28 0 0 0 .277.278" />
  </svg>
);
export default SvgBodyCare;
