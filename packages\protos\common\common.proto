syntax = "proto3";

package common;

import "rebit/fi_schemas/deposit/deposit.proto";
import "rebit/fi_schemas/equities/equities.proto";
import "rebit/fi_schemas/mutual_funds/mutual_funds.proto";
import "database/core.proto";
import "database/fi_deposit.proto";
import "database/fi_equities.proto";
import "database/fi_mutual_funds.proto";

message FiAccount {
    oneof fi_account {
        DepositAccount deposit_account = 1;
        EquityAccount equity_account = 2;
        MutualFundAccount mutual_fund_account = 3;
    }
    database.FiType fi_type = 4;
}

message DepositAccount {
    rebit.fi_schemas.deposit.Account raw_data = 1;
    database.FiDepositAccountSummary summary = 2;
    repeated database.FiDepositAccountTransaction transactions = 3;
}

message EquityAccount {
    rebit.fi_schemas.equities.Account raw_data = 1;
    database.FiEquitiesAccountSummary summary = 2;
    repeated database.FiEquitiesAccountTransaction transactions = 3;
}

message MutualFundAccount {
    rebit.fi_schemas.mutual_funds.Account raw_data = 1;
    database.FiMutualFundsAccountSummary summary = 2;
    repeated database.FiMutualFundsAccountTransaction transactions = 3;
}