import * as React from "react";
import type { SVGProps } from "react";
const SvgSelected = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="none"
    viewBox="0 0 21 21"
    {...props}
  >
    <path fill="#1E1E1E" d="M0 0h21v21H0z" />
    <path
      fill="#444"
      d="M-5748-8411c0-1.1.9-2 2-2H7389c1.1 0 2 .9 2 2V2172c0 1.1-.9 2-2 2H-5746c-1.1 0-2-.9-2-2z"
    />
    <path
      fill="#fff"
      fillOpacity={0.1}
      d="M-5746-8412H7389v-2H-5746zm13136 1V2172h2V-8411zm-1 10584H-5746v2H7389zm-13136-1V-8411h-2V2172zm1 1c-.55 0-1-.45-1-1h-2c0 1.66 1.34 3 3 3zm13136-1c0 .55-.45 1-1 1v2c1.66 0 3-1.34 3-3zm-1-10584c.55 0 1 .45 1 1h2c0-1.66-1.34-3-3-3zm-13135-2c-1.66 0-3 1.34-3 3h2c0-.55.45-1 1-1z"
    />
    <g clipPath="url(#selected_svg__a)">
      <path fill="#F6F6F6" d="M-67-181h375v2136H-67z" />
      <path fill="#fff" d="M-68-114h375v305H-68z" />
      <rect width={48} height={48} x={-34} y={7} fill="#905BB5" rx={10.8} />
      <g strokeWidth={1.579}>
        <rect
          width={18.421}
          height={18.421}
          x={1.289}
          y={1.289}
          fill="#A7C4FE"
          stroke="#fff"
          rx={9.211}
        />
        <path
          stroke="#FBFBFB"
          strokeLinecap="round"
          strokeLinejoin="round"
          d="m7 10.497 1.827 1.713L13.316 8"
        />
      </g>
    </g>
    <defs>
      <clipPath id="selected_svg__a">
        <path
          fill="#fff"
          d="M-67-161c0-11.046 8.954-20 20-20h335c11.046 0 20 8.954 20 20v617H-67z"
        />
      </clipPath>
    </defs>
  </svg>
);
export default SvgSelected;
