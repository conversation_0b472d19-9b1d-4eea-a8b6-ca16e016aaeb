import * as React from "react";
import type { SVGProps } from "react";
const SvgFoodDrink = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="currentColor"
    viewBox="0 0 18 30"
    {...props}
  >
    <path
      fillRule="evenodd"
      d="M15.31 10.462v17.63c0 2.216-3.15 2.216-3.15 0v-17.63C7.067 8.154 11.049.492 13.737.492c2.685 0 6.669 7.662 1.574 9.97M5.213 12.4v15.693a1.534 1.534 0 0 1-3.057 0V12.4C.859 11.754.21 10.462.21 9.077V1.968c0-.923 1.39-.923 1.39 0V6.4c0 .923 1.389.923 1.389 0v-4.43c0-.924 1.389-.924 1.389 0v4.43c0 .923 1.39.923 1.39 0v-4.43c0-.924 1.389-.924 1.389 0v7.108c0 1.384-.649 2.676-1.945 3.323"
      clipRule="evenodd"
    />
  </svg>
);
export default SvgFoodDrink;
