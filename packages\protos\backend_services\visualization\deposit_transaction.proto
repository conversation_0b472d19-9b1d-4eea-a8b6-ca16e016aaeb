syntax = "proto3";
package backend_services.visualization;

import "database/cards_filters.proto";
import "database/fi_deposit.proto";

service DepositTransaction{
    rpc FetchDepositTxns(FetchDepositTxnsRequest) returns (FetchDepositTxnsResponse) {};
    rpc AssignMerchantToDepositTxns(AssignMerchantToDepositTxnsRequest) returns (AssignMerchantToDepositTxnsResponse) {};
    rpc AssignCategoryToDepositTxns(AssignCategoryToDepositTxnsRequest) returns (AssignCategoryToDepositTxnsResponse) {};
    rpc MarkDepositTxnFavorite(MarkDepositTxnFavoriteRequest) returns (MarkDepositTxnFavoriteResponse) {};
    rpc UpdateTransactionNotes(UpdateTransactionNotesRequest) returns (UpdateTransactionNotesResponse) {};
    rpc UpdateTxnCashFlowPeriod(UpdateTxnCashFlowPeriodRequest) returns (UpdateTxnCashFlowPeriodResponse) {};
    rpc ExcludeTxnFromCashFlow(ExcludeTxnFromCashFlowRequest) returns (ExcludeTxnFromCashFlowResponse) {};
    rpc FetchTransactionDocuments(FetchTransactionDocumentsRequest) returns (FetchTransactionDocumentsResponse) {};
    rpc DeleteTransactionDocuments(DeleteTransactionDocumentsRequest) returns (DeleteTransactionDocumentsResponse) {};
    rpc SearchTxns(SearchTxnsRequest) returns (SearchTxnsResponse) {};
    rpc FindSimilarTxnsForTagging(FindSimilarTxnsForTaggingRequest) returns (FindSimilarTxnsForTaggingResponse) {};
    rpc SearchSuggestions(SearchSuggestionsRequest) returns (SearchSuggestionsResponse) {};
}

message Tag {
    string category_collection = 1; // Collection in which category belongs: "global" or <uid>
    string category_id = 2; // Document Id of category collection
    string subcategory_id = 3; // Document Id of subcategory collection
}

message Merchant {
    string user_id = 1;
    string merchant_id = 2;
    string merchant_name = 3;
}


message DepositTransactionCard { 
    string txn_id = 1; // Transaction ID of our system
    string account_id = 2; // Account ID of our system
    double amount = 3; // Transaction amount
    string type = 4; // "CREDIT" or "DEBIT"
    int64 txn_timestamp = 5; // When was the txn carried out
    string mode = 6; // "UPI", "ATM" etc.
    string narration = 7; // Remarks
    string raw_txn_id = 8; // Transaction ID in bank
    bool favorite = 9; // Whether user has favorited this txn
    Tag tag = 10;
    Merchant merchant = 11;
    bool exclude_cash_flow = 13; // Whether transaction is excluded from cash-flow
    string fip_id = 15; // FIP ID of the account
    string user_notes = 16; // User added notes
    database.CashFlowPeriod cash_flow_period = 17; // Accounted in which period in cashflow
    int32 documents_count = 18;
    string recurrent_group_id = 19; // Recurrent txn group id
}



message DepositTxnCardFilters {
    database.UserGroupFilters user_groups = 1;
    database.AccountsFilter account_filters = 2;
    database.TxnFilters txn_filters = 3;
}

message FetchDepositTxnsRequest{
    DepositTxnCardFilters filter = 1;
    database.TxnPaginationParams pagination_params = 2;
}
message FetchDepositTxnsResponse{
    repeated DepositTransactionCard cards = 1;
    int32 total_count = 2;
}


message AssignCategoryToDepositTxnsRequest {
    repeated string transaction_ids = 1;
    string collection = 2;
    string category_id = 3;
    string subcategory_id = 4;
}
message AssignCategoryToDepositTxnsResponse {}

message AssignMerchantToDepositTxnsRequest {
    repeated string transaction_ids = 1;
    string merchant_id = 2;
}
message AssignMerchantToDepositTxnsResponse {}


message MarkDepositTxnFavoriteRequest {
    string transaction_id = 1;
    bool favorite = 2;
}
message MarkDepositTxnFavoriteResponse {}

message UpdateTransactionNotesRequest {
    string transaction_id = 1;
    string notes = 2;
}

message UpdateTransactionNotesResponse {
}

message UpdateTxnCashFlowPeriodRequest {
    string txn_id = 1;
    database.CashFlowPeriod cash_flow_period = 2;
}
message UpdateTxnCashFlowPeriodResponse {}

message ExcludeTxnFromCashFlowRequest {
    string txn_id = 1;
    bool exclude_cash_flow = 2;
}
message ExcludeTxnFromCashFlowResponse {}
message Document {
    string name = 1;
    string object_name = 2;
    string thumbnail = 3; // Base64 encoded
    string signed_url = 4;
    string content_type = 5;
}

message FetchTransactionDocumentsRequest {
    string transaction_id = 1;
}

message FetchTransactionDocumentsResponse {
    repeated Document documents = 1;
}

message DeleteTransactionDocumentsRequest {
    string transaction_id = 1;
    repeated string object_names = 2;
}

message DeleteTransactionDocumentsResponse {
}

message SearchTxnsRequest {
    string input = 1;
    SearchBy search_by = 2;
    DepositTxnCardFilters filter = 3;
    database.TxnPaginationParams pagination_params = 4;

    enum SearchBy {
        SEARCH_BY_ALL = 0;
        SEARCH_BY_TAG = 1;
        SEARCH_BY_MERCHANT = 2;
        SEARCH_BY_USER = 3;
        SEARCH_BY_NOTES = 4;
        SEARCH_BY_AMOUNT = 5;
        SEARCH_BY_REMARKS = 6;
    }
}

message SearchTxnsResponse{
    repeated DepositTransactionCard cards = 1;
}

message FindSimilarTxnsForTaggingRequest{
    string txn_id = 1;
    repeated string identifier_keywords = 2;
}

message FindSimilarTxnsForTaggingResponse{
    repeated DepositTransactionCard cards = 1;
}

message SearchSuggestionsRequest {
    string input = 1;
}

message SearchSuggestionsResponse {

    repeated Suggestion suggestions = 1;

    message Suggestion {
        string keyword = 1;
        repeated string merchant_ids = 2;
        repeated Tag tags = 3;
    }

    message Tag {
        string category_collection = 1;
        string category_id = 2;
        string subcategory_id = 3;
    }
}