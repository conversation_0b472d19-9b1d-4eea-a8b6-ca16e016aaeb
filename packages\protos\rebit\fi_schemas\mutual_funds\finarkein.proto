syntax = "proto3";
package rebit.fi_schemas.mutual_funds;
import "rebit/fi_schemas/mutual_funds/mutual_funds.proto";


message FinarkeinMutualFundAccount {
    repeated Holder profile = 1; // @gotags: json:"profile"
    repeated Holding summary = 2; // @gotags: json:"summary"
    repeated Transaction transactions = 3; // @gotags: json:"transactions"
    TransactionsMeta transactions_meta = 4; // @gotags: json:"transactionsMeta"
}


message TransactionsMeta {
    int64 fromTimestamp = 1; // @gotags: json:"fromTimestamp"
    int64 toTimestamp = 2; // @gotags: json:"toTimestamp"
}
  