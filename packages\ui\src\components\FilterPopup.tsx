import { cn } from "../lib/utils";
import { Popover, PopoverContent, PopoverTrigger } from "./ui/popover";
import React from "react";

interface FilterPopupProps {
  trigger: React.ReactNode;
  children: (props: { close: () => void }) => React.ReactNode;
  className?: string;
}

export const FilterPopup: React.FC<FilterPopupProps> = ({
  children,
  trigger,
  className,
}) => {
  const [open, setOpen] = React.useState(false);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        {React.cloneElement(trigger as React.ReactElement, {
          className: cn(
            (trigger as React.ReactElement).props.className,
            open ? "bg-[#905BB5]" : "",
          ),
        })}
      </PopoverTrigger>
      <PopoverContent
        className={cn(
          "z-50 bg-white border rounded-md shadow-lg p-3",
          className,
        )}
        align="start"
        sideOffset={5}
      >
        {children({ close: () => setOpen(false) })}
      </PopoverContent>
    </Popover>
  );
};
