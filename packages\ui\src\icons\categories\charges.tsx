import * as React from "react";
import type { SVGProps } from "react";
const SvgCharges = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="currentColor"
    viewBox="0 0 34 35"
    {...props}
  >
    <path d="M8.176 8.655v5.842H4.784a.94.94 0 0 1-.942-.942v-4.9c0-.676.549-1.225 1.225-1.225H6.95c.676 0 1.225.549 1.225 1.225" />
    <path
      fillRule="evenodd"
      d="M6.951 5.543h20.165a3.11 3.11 0 0 1 3.11 3.11v21.463a.317.317 0 0 1-.516.247l-1.805-1.454a.32.32 0 0 0-.398 0l-2.117 1.709a.32.32 0 0 1-.398 0l-2.12-1.71a.32.32 0 0 0-.398 0l-2.123 1.71a.32.32 0 0 1-.397 0l-2.123-1.712a.32.32 0 0 0-.397 0l-2.125 1.712a.32.32 0 0 1-.397 0l-2.128-1.712a.32.32 0 0 0-.397 0l-1.812 1.457a.317.317 0 0 1-.514-.247V8.652a3.11 3.11 0 0 0-3.11-3.11m8.48 18.918h9.423a.942.942 0 1 0 0-1.884h-9.422a.942.942 0 1 0 0 1.884m.345-7.947 5.4 4.103h2.13l-5.312-4.103h1.57c1.943 0 3.325-1.066 3.555-2.808h.893v-1.022h-.893c-.1-.706-.388-1.31-.835-1.771h1.728V9.89h-8.236v1.296h3.83c1.095 0 1.858.561 2.06 1.497h-5.89v1.022h5.89c-.188.936-.965 1.512-2.06 1.512h-3.83z"
      clipRule="evenodd"
    />
  </svg>
);
export default SvgCharges;
