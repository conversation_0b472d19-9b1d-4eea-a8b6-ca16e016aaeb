# Windows Build Guide

This guide addresses the Windows-specific build issues you may encounter when running `pnpm build`.

## Problem

The error `EPERM: operation not permitted, symlink` occurs because:

1. Next.js standalone builds try to create symlinks for optimization
2. Windows requires elevated privileges to create symlinks by default
3. pnpm's symlink-based node_modules structure conflicts with Next.js symlink creation

## Solutions

### Option 1: Use Windows-Compatible Build (Recommended for Development)

```bash
# Use the Windows-compatible build script
pnpm run build:windows --filter=web

# Or run turbo with the Windows build
turbo run build:windows --filter=web
```

### Option 2: Enable Developer Mode (Windows 10/11)

1. Open **Settings** → **Update & Security** → **For developers**
2. Enable **Developer Mode**
3. Restart your terminal/IDE
4. Run the normal build command:
   ```bash
   pnpm build
   ```

### Option 3: Run as Administrator

1. Open PowerShell or Command Prompt **as Administrator**
2. Navigate to your project directory
3. Run the build command:
   ```bash
   pnpm build
   ```

### Option 4: Use WSL2 (Windows Subsystem for Linux)

1. Install WSL2 with Ubuntu
2. Clone your project in the WSL2 environment
3. Run builds from within WSL2

## Production Builds

For production/Docker builds, the standalone output is automatically enabled and optimized for containerized environments.

## Configuration Details

- **Development builds**: Standalone output is disabled to prevent symlink issues
- **Production builds**: Standalone output is enabled with proper Docker configuration
- **Cross-platform compatibility**: Uses `cross-env` for environment variable handling

## Troubleshooting

If you continue to experience issues:

1. Clear Next.js cache: `rm -rf apps/web/.next`
2. Clear node_modules: `pnpm clean && pnpm install`
3. Try the Windows-specific build script: `pnpm run build:windows --filter=web`

## Alternative: Disable Standalone Output Completely

If none of the above solutions work, you can temporarily disable standalone output by editing `apps/web/next.config.js`:

```javascript
/** @type {import('next').NextConfig} */
module.exports = {
  reactStrictMode: true,
  // output: "standalone", // Comment this out
  transpilePackages: ["@repo/ui"],
};
```

Note: This will disable some optimizations but will allow the build to complete on Windows.
