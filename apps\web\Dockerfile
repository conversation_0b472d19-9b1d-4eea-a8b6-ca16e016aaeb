FROM node:lts-alpine AS alpine

# Secrets
ENV MONGODB_URI="mongodb+srv://mohammadkaif:<EMAIL>/testing"
ENV MONGODB_DB_NAME="testing"
ENV GRPC_SERVER_URI="dev.cuspmoney.com"
ENV NEXT_PUBLIC_FIREBASE_API_KEY="AIzaSyDztso8619O1wKWwewn60sNH4AWI819dXo"
ENV NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN="sampatti-415708.firebaseapp.com"
ENV NEXT_PUBLIC_FIREBASE_PROJECT_ID="sampatti-415708"
ENV NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET="dev-transaction-documents"
ENV NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID="85127312451"
ENV NEXT_PUBLIC_FIREBASE_APP_ID="1:85127312451:web:3d7c99efb12e50385b10f6"
ENV NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID="G-DCWBM9PF5J"

# Protocol buffers path
ENV PROTO_DIR_PATH="/app/packages/protos"

# Install libc6-compat for Alpine
RUN apk update
RUN apk add --no-cache libc6-compat


# STAGE 1: Install PNPM and Turbo CLI
# ------------------------------------------------------------------------------
FROM alpine AS base

# Enable pnpm
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable

# Install Turbo globally
RUN pnpm add --global turbo@2
# ------------------------------------------------------------------------------


# STAGE 2: Prune the workspace
# ------------------------------------------------------------------------------
FROM base AS pruner

WORKDIR /app

# Copy repo content
COPY . .

# Prune the workspace for the web app
RUN turbo prune web --docker
# ------------------------------------------------------------------------------


# STAGE 3: Install deps and build the app
# ------------------------------------------------------------------------------
FROM base AS builder

WORKDIR /app

# PNPM: Install the dependencies
COPY --from=pruner /app/out/json/ .
RUN pnpm install --frozen-lockfile

# Build the project with turbo
COPY --from=pruner /app/out/full/ .
COPY /packages/protos ./packages/protos

# Set environment variables for production build
ENV NODE_ENV=production
ENV DOCKER_BUILD=true

# Use production Next.js config for Docker builds
COPY apps/web/next.config.production.js apps/web/next.config.js

# Uncomment and use build args to enable remote caching
# ARG TURBO_TEAM
# ENV TURBO_TEAM=$TURBO_TEAM

# ARG TURBO_TOKEN
# ENV TURBO_TOKEN=$TURBO_TOKEN

RUN turbo build --filter=web
# ------------------------------------------------------------------------------


# STAGE 4: Run the server
# ------------------------------------------------------------------------------
FROM alpine AS runner
WORKDIR /app

# Don't run production as root
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs
USER nextjs

EXPOSE 3000

# Copy only the necessary files
COPY --from=builder --chown=nextjs:nodejs /app/apps/web/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/apps/web/.next/static ./apps/web/.next/static
COPY --from=builder --chown=nextjs:nodejs /app/apps/web/public ./apps/web/public
COPY /packages/protos ./packages/protos

# Start the Next.js application
CMD ["node", "apps/web/server.js"]
# ------------------------------------------------------------------------------