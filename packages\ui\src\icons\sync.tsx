import React from "react";

const SyncIcon: React.FC<React.SVGProps<SVGSVGElement>> = ({ ...props }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      {...props}
    >
      <path
        d="M12.5859 4.66797V7.16797H10.0859"
        stroke="currentColor"
        strokeWidth="0.833333"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.41406 11.332V8.83203H5.91406"
        stroke="currentColor"
        strokeWidth="0.833333"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M4.4599 6.74901C4.67122 6.15184 5.03037 5.61793 5.50384 5.19711C5.97731 4.77628 6.54967 4.48225 7.16752 4.34246C7.78536 4.20266 8.42854 4.22166 9.03706 4.39767C9.64557 4.57368 10.1996 4.90098 10.6474 5.34901L12.5807 7.16568M3.41406 8.83235L5.3474 10.649C5.79521 11.0971 6.34922 11.4243 6.95773 11.6004C7.56625 11.7764 8.20943 11.7954 8.82728 11.6556C9.44512 11.5158 10.0175 11.2217 10.491 10.8009C10.9644 10.3801 11.3236 9.84619 11.5349 9.24901"
        stroke="currentColor"
        strokeWidth="0.833333"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export { SyncIcon };
