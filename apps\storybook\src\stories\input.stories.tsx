import { <PERSON>a, StoryObj } from "@storybook/react";
import { Input } from "@repo/ui";
import { Search } from "@repo/ui/icons";

const meta: Meta<typeof Input> = {
  title: "Components/Input",
  component: Input,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Input>;

export const EmptyInput: Story = {
  args: {
    value: "",
    placeholder: `Try Seaching "Salary"`,
    type: "text",
    prefixIcon: <Search width="24px" height="25px" />,
  },
};

export const FilledInput: Story = {
  args: {
    ...EmptyInput.args,
    value: "Swiggy Instamart",
  },
};

export const PrefixTextInput: Story = {
  args: {
    ...FilledInput.args,
    prefixText: "Search",
  },
};
