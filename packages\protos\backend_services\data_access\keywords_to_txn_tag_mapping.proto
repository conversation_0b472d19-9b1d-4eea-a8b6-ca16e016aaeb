syntax = "proto3";
package backend_services.data_access;

import "database/transaction_category.proto";

service KeywordsToTxnTagMapping {
    rpc FindCategoriesForTagging(FindCategoriesForTaggingRequest) returns (FindCategoriesForTaggingResponse) {};
    rpc FindCategoriesForSearch(FindCategoriesForSearchRequest) returns (FindCategoriesForSearchResponse) {};
    rpc GetTxnTagsByIds(GetTxnTagsByIdsRequest) returns (GetTxnTagsByIdsResponse) {};
    rpc FindTagsForSearchSuggestions(FindTagsForSearchSuggestionsRequest) returns (FindTagsForSearchSuggestionsResponse) {};
}

message FindCategoriesForTaggingRequest {
	repeated string keywords = 1;
}

message FindCategoriesForTaggingResponse {
    repeated Record records = 1;

    message HighlightText{
        map<string, string> text = 1;
    }
    message Highlight {
        repeated HighlightText texts = 1;
    }
    message Record {
        string debit_subcategory = 1;
        string debit_category = 2;
        string credit_subcategory = 3;
        string credit_category = 4;
        repeated Highlight highlights = 5;
        bool is_generic = 6; // true if a generic record for category
    }
}

message GetTxnTagsByIdsRequest {
    repeated string ids = 1;
}

message GetTxnTagsByIdsResponse {
    repeated database.KeywordsToTxnTagMapping tags = 1;
}

message FindCategoriesForSearchRequest{
    string keyword = 1;
}

message FindCategoriesForSearchResponse {
    repeated Record records = 1;

    message HighlightText{
        map<string, string> text = 1;
    }
    message Highlight {
        repeated HighlightText texts = 1;
    }
    message Record {
        string debit_subcategory = 1;
        string debit_category = 2;
        string credit_subcategory = 3;
        string credit_category = 4;
        repeated Highlight highlights = 5;
        bool is_generic = 6; // true if a generic record for category
    }
}

message FindTagsForSearchSuggestionsRequest {
    string keyword = 1;
}

message FindTagsForSearchSuggestionsResponse {
    repeated database.KeywordsToTxnTagMapping tags = 1;
}