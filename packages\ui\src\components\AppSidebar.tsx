import React from "react";
import {
  AlignJustify,
  ArrowLeftRight,
  ChevronRight,
  LayoutDashboard,
  Settings,
  UserCircle,
} from "lucide-react";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarInset,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarProvider,
  SidebarTrigger,
} from "./ui/sidebar";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "./ui/collapsible";

type AppSidebarProps = {
  username: string;
  email: string;
  children: React.ReactNode;
};

const settingsItems = [
  {
    title: "Account Settings",
    url: "#",
  },
  {
    title: "App Settings",
    url: "#",
  },
  {
    title: "Privacy Policy",
    url: "https://www.cusp.money/legal?tab=privacy-policy",
  },
  {
    title: "Terms and Conditions",
    url: "https://www.cusp.money/legal?tab=terms-of-service",
  },
  {
    title: "Support",
    url: "#",
  },
  {
    title: "General",
    url: "#",
  },
];

const MenuLink = ({
  href,
  icon: Icon,
  label,
}: {
  href: string;
  icon: React.ElementType;
  label: string;
}) => (
  <SidebarMenuItem>
    <SidebarMenuButton asChild>
      <a href={href} className="flex items-center gap-2">
        <Icon />
        <span>{label}</span>
      </a>
    </SidebarMenuButton>
  </SidebarMenuItem>
);

export function AppSidebar({ username, email, children }: AppSidebarProps) {
  return (
    <SidebarProvider className=" h-screen ">
      <Sidebar collapsible="icon">
        <SidebarHeader>
          <SidebarMenuButton
            size="lg"
            className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
          >
            <SidebarTrigger className="size-8" aria-label="CuspMoney">
              <AlignJustify />
            </SidebarTrigger>
            <span className="truncate font-semibold">Cusp Money</span>
          </SidebarMenuButton>
        </SidebarHeader>

        <SidebarContent>
          <SidebarGroup>
            <SidebarGroupContent>
              <SidebarMenu>
                <MenuLink href="#" icon={LayoutDashboard} label="Dashboard" />
                <MenuLink href="#" icon={ArrowLeftRight} label="Transactions" />

                <Collapsible defaultOpen className="group/collapsible">
                  <SidebarMenuItem>
                    <CollapsibleTrigger asChild>
                      <SidebarMenuButton>
                        <Settings />
                        <span>Settings</span>
                        <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                      </SidebarMenuButton>
                    </CollapsibleTrigger>
                    <CollapsibleContent>
                      <SidebarMenuSub>
                        {settingsItems.map((item) => (
                          <SidebarMenuButton key={item.title}>
                            <a
                              href={item.url}
                              target={item.url == "#" ? "" : "blank"}
                            >
                              {item.title}
                            </a>
                          </SidebarMenuButton>
                        ))}
                      </SidebarMenuSub>
                    </CollapsibleContent>
                  </SidebarMenuItem>
                </Collapsible>
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        </SidebarContent>

        <SidebarFooter>
          <SidebarMenuButton
            size="lg"
            className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
          >
            <div className="w-8 h-8 flex items-center justify-center">
              <UserCircle className="size-8" aria-label="Profile" />
            </div>
            <div className="grid text-sm">
              <span className="truncate font-semibold">{username}</span>
              <span className="truncate text-xs">{email}</span>
            </div>
          </SidebarMenuButton>
        </SidebarFooter>
      </Sidebar>

      <SidebarInset className="flex-1 overflow-y-hidden bg-slate-200">
        {children}
      </SidebarInset>
    </SidebarProvider>
  );
}
