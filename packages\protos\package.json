{"name": "@repo/protos", "version": "0.0.0", "private": true, "scripts": {"proto:gen": "node generate-types.js", "clean": "node -e \"const fs = require('fs'); if (fs.existsSync('types')) fs.rmSync('types', { recursive: true, force: true });\"", "build": "pnpm proto:gen"}, "dependencies": {"@bufbuild/protobuf": "^2.5.0", "glob": "^10.3.10", "ts-proto": "^2.7.0"}, "devDependencies": {"@protobuf-ts/protoc": "^2.11.0", "@repo/typescript-config": "workspace:*", "@types/node": "^22.10.2", "protobufjs": "^7.2.6", "typescript": "^5.7.3"}, "type": "module"}