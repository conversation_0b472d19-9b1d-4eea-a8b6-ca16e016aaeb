import * as React from "react";
import type { SVGProps } from "react";
const SvgSavedTransaction = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="currentColor"
    viewBox="0 0 16 16"
    width="1em"
    height="1em"
    {...props}
  >
    <path d="M11.963 1.164H4.897A2.9 2.9 0 0 0 2 4.06v10.094a.33.33 0 0 0 .494.286l3.69-2.108 3.69 2.108a.33.33 0 0 0 .329 0 .33.33 0 0 0 .164-.286V4.577h3.853V3.421a2.26 2.26 0 0 0-2.257-2.257m1.597 2.752h-3.193l1-.495c0-.88-.284-1.597.596-1.597.881 0 1.597.717 1.597 1.597z" />
    <path d="M13.564 3.928q.063-.222.064-.466c0-.921-.73-1.668-1.63-1.668s-1.63.747-1.63 1.668q.001.244.065.466z" />
    <path d="M10.368 3.414h3.26V4.57h-3.26z" />
  </svg>
);
export default SvgSavedTransaction;
