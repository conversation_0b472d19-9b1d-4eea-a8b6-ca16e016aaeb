"use client";
import { useRef, useState } from "react";
import { Input } from "./ui/input";
import { Badge } from "./ui/badge";
import { cn } from "../lib/utils";
import { Close, Search } from "../icons";
import { Label } from "./ui/label";
import { Clock } from "lucide-react";
import {
  RECENT_SEARCH_KEY,
  useRecentSearches,
  saveRecentSearch,
} from "../hooks/use-recentSearches";

interface TransactionSearchProps {
  onSearch: (selection: string | null) => void;
  onSearchCategoryChange?: (category: string | undefined) => void;
}

const PrefixIcon = () => (
  <div className="text-primary text-sm flex gap-2">
    <Search width="20px" height="22px" />
  </div>
);

const SearchLabels: string[] = [
  "Notes",
  "Tags",
  "Merchant",
  "Amount",
  "Remarks",
  "User",
];

const TransactionSearch: React.FC<TransactionSearchProps> = ({
  onSearch,
  onSearchCategoryChange,
}) => {
  const [searcInKeys, setSearcInKeys] = useState<string>();
  const [search, setSearch] = useState("");
  const [isFocused, setIsFocused] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const { recentSearches, refreshRecentSearches, removeFromRecentSearch } =
    useRecentSearches();

  const handleBlur = (event: React.FocusEvent<HTMLDivElement>) => {
    // Only blur if the new focused element is outside the container
    if (!containerRef.current?.contains(event.relatedTarget as Node)) {
      setIsFocused(false);
    }
  };

  return (
    <div
      className="relative w-full max-w-2xl mx-auto z-50"
      key="txnSearch"
      ref={containerRef}
      tabIndex={-1}
      onFocus={() => setIsFocused(true)}
      onBlur={handleBlur}
    >
      <div
        className={cn(
          "bg-white rounded-full shadow-sm px-4 py-1 flex items-center gap-2",
          isFocused ? "rounded-t-xl rounded-b-none" : "",
          "border-0",
        )}
      >
        <Input
          id="txnSearchInput"
          name="txnSearchCustom"
          className="w-full border-none focus:outline-none focus:ring-0 focus:border-none !border-none"
          outerClassName="!border-none !ring-0 !shadow-none"
          prefixIcon={<PrefixIcon />}
          placeholder="Try searching 'Salary'"
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === "Enter") {
              onSearch(search);
              saveRecentSearch(search);
              refreshRecentSearches();
              setIsFocused(false);
              (document.activeElement as HTMLElement)?.blur();
            }
          }}
          autoComplete="off"
        />
        {search.trim().length != 0 && (
          <div
            className="absolute top-2.5 right-[3%] text-primary text-sm flex gap-2 cursor-pointer p-1"
            title="Clear search"
            onClick={() => {
              setSearch("");
              onSearch("");
              (document.activeElement as HTMLElement)?.blur();
            }}
          >
            <Close width="20px" height="22px" />
          </div>
        )}
      </div>

      {isFocused && (
        <div className="absolute top-full left-0 w-full bg-white rounded-b-xl shadow-lg p-3 transition-all duration-200 animate-in slide-in-from-top-1">
          <Label
            htmlFor="txnSearchInput"
            className="text-sm font-semibold text-[#AC80CC] px-3 py-2"
          >
            Search In
          </Label>
          <div className="flex flex-wrap gap-2 pl-5 mt-3">
            {SearchLabels.map((item) => (
              <Badge
                key={item}
                tabIndex={0}
                selected={searcInKeys === item}
                className={cn(
                  "rounded-full border cursor-pointer justify-center px-3 py-1 transition-transform duration-150",
                  searcInKeys !== item &&
                    "bg-[#FBFBFB] text-foreground border-[#EBEBEB] hover:scale-105",
                )}
                onClick={() => {
                  const newValue = searcInKeys === item ? undefined : item;
                  setSearcInKeys(newValue);
                  onSearchCategoryChange?.(newValue);
                }}
              >
                {item}
              </Badge>
            ))}
          </div>
          <div className="text-sm px-4 py-2 mt-3">
            <Label
              htmlFor="txnSearchInput"
              className="font-semibold text-[#AC80CC]"
            >
              Recently Searched
            </Label>
            <div className="mt-3 text-foreground space-y-1 pl-3">
              {recentSearches.length === 0 && (
                <p className="text-gray-700">No recent searches</p>
              )}
              {recentSearches.map((item, idx) => (
                <div
                  key={idx}
                  className="group flex items-center justify-between px-3 py-2 rounded-md hover:bg-[#F5F3FF] transition-colors"
                >
                  <div
                    className="flex items-center gap-2 cursor-pointer w-full"
                    onClick={() => {
                      setSearch(item);
                      onSearch(item);
                      saveRecentSearch(item);
                      refreshRecentSearches();
                      setIsFocused(false);
                      (document.activeElement as HTMLElement)?.blur();
                    }}
                  >
                    <Clock width="16px" height="16px" />
                    <span className="truncate">{item}</span>
                  </div>

                  {/* Delete icon (only visible on hover) */}
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      const updated = recentSearches.filter(
                        (_, i) => i !== idx,
                      );
                      removeFromRecentSearch(idx);
                    }}
                    className="invisible group-hover:visible text-gray-600 hover:text-red-500 ml-2"
                    title="Remove"
                  >
                    ✕
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export { TransactionSearch };
