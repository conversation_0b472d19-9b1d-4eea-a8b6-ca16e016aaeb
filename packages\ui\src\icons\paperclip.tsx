import * as React from "react";
import type { SVGProps } from "react";
const SvgPaperclip = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="none"
    viewBox="0 0 16 17"
    {...props}
  >
    <g clipPath="url(#paperclip_svg__a)">
      <path
        stroke="#fff"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="m14.29 7.867-6.126 6.127a4.002 4.002 0 1 1-5.66-5.66l6.127-6.127a2.668 2.668 0 0 1 3.773 3.774l-6.133 6.126a1.334 1.334 0 0 1-1.887-1.886l5.66-5.654"
      />
    </g>
    <defs>
      <clipPath id="paperclip_svg__a">
        <path fill="#fff" d="M0 .5h16v16H0z" />
      </clipPath>
    </defs>
  </svg>
);
export default SvgPaperclip;
