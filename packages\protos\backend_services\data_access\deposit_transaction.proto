syntax = "proto3";

package backend_services.data_access;

import "database/fi_deposit.proto";
import "database/cards_filters.proto";

service DepositTransaction {
	rpc UpsertDepositTransactions(UpsertDepositTransactionsRequest) returns (UpsertDepositTransactionsResponse) {};
    rpc GetLatestDepositTransaction(GetLatestDepositTransactionRequest) returns (GetLatestDepositTransactionResponse) {};
    rpc GetLatestDepositTransactions(GetLatestDepositTransactionsRequest) returns (GetLatestDepositTransactionsResponse) {};
    rpc GetAccountsLatestDepositTransaction(GetAccountsLatestDepositTransactionRequest) returns (GetAccountsLatestDepositTransactionResponse) {};
    rpc CountAccountsLatestDepositTransaction(CountAccountsLatestDepositTransactionRequest) returns (CountAccountsLatestDepositTransactionResponse) {};
    rpc InsertDepositTransactions(InsertDepositTransactionsRequest) returns (InsertDepositTransactionsResponse) {};
    rpc PopulateDepositUserPersonalizedTagsFreq(PopulateDepositUserPersonalizedTagsFreqRequest) returns (PopulateDepositUserPersonalizedTagsFreqResponse) {};
    rpc PopulateDepositTxnLlmPredictedTagFreq(PopulateDepositTxnLlmPredictedTagFreqRequest) returns (PopulateDepositTxnLlmPredictedTagFreqResponse){};
    rpc GetDepositTxnsMatchingIdentifierKeywords(GetDepositTxnsMatchingIdentifierKeywordsRequest) returns (GetDepositTxnsMatchingIdentifierKeywordsResponse) {};
    rpc PopulateUntaggedDepositTxnAnnotatorReview(PopulateUntaggedDepositTxnAnnotatorReviewRequest) returns (PopulateUntaggedDepositTxnAnnotatorReviewResponse){};
}

message UpsertDepositTransactionsRequest{
    repeated database.DepositTransaction records = 1;
}
message UpsertDepositTransactionsResponse{
    
}

message GetLatestDepositTransactionRequest{
    string txn_id = 1;
}
message GetLatestDepositTransactionResponse{
    database.DepositTransaction txn = 1;
}

message GetLatestDepositTransactionsRequest{
    repeated string txn_ids = 1;
}
message GetLatestDepositTransactionsResponse{
    repeated database.DepositTransaction records = 1;
}



message GetAccountsLatestDepositTransactionRequest{
    repeated string account_ids = 1;
    database.TxnFilters txn_filters = 2;
    database.TxnPaginationParams pagination_params = 3;
    database.TxnSearchFilters search_filters = 4;
}
message GetAccountsLatestDepositTransactionResponse{
    repeated Record records = 1;

    message Record {
        database.DepositTransaction transaction = 1;
        int32 documents_count = 2;
    }
}

message CountAccountsLatestDepositTransactionRequest{
    repeated string account_ids = 1;
    database.TxnFilters txn_filters = 2;
}
message CountAccountsLatestDepositTransactionResponse{
    int32 count = 1;
}

message InsertDepositTransactionsRequest{
    repeated database.DepositTransaction records = 1;
}
message InsertDepositTransactionsResponse{}

message PopulateDepositUserPersonalizedTagsFreqRequest{}
message PopulateDepositUserPersonalizedTagsFreqResponse{}

message PopulateDepositTxnLlmPredictedTagFreqRequest{}
message PopulateDepositTxnLlmPredictedTagFreqResponse{}

message GetDepositTxnsMatchingIdentifierKeywordsRequest{
    string txn_id = 1;
    repeated string identifier_keywords = 2;
    repeated string account_ids = 3;
}
message GetDepositTxnsMatchingIdentifierKeywordsResponse{
    repeated Record records = 1;

    message Record {
        database.DepositTransaction transaction = 1;
        int32 documents_count = 2;
    }
}

message PopulateUntaggedDepositTxnAnnotatorReviewRequest{}
message PopulateUntaggedDepositTxnAnnotatorReviewResponse{}
