syntax = "proto3";

package shared;

message Date {
    // Year of the date. Must be from 1 to 9999, or 0 to specify a date without
    // a year.
    int32 year = 1;
  
    // Month of a year. Must be from 1 to 12, or 0 to specify a year without a
    // month and day.
    int32 month = 2;
  
    // Day of a month. Must be from 1 to 31 and valid for the year and month, or 0
    // to specify a year by itself or a year and month where the day isn't
    // significant.
    int32 day = 3;
}
  
enum CalendarPeriod {
    // Undefined period, raises an error.
    CALENDAR_PERIOD_UNSPECIFIED = 0;
  
    // A day.
    DAY = 1;
  
    // A week. Weeks begin on Monday, following
    // [ISO 8601](https://en.wikipedia.org/wiki/ISO_week_date).
    WEEK = 2;
  
    // A fortnight. The first calendar fortnight of the year begins at the start
    // of week 1 according to
    // [ISO 8601](https://en.wikipedia.org/wiki/ISO_week_date).
    FORTNIGHT = 3;
  
    // A month.
    MONTH = 4;
  
    // A quarter. Quarters start on dates 1-Jan, 1-Apr, 1-Jul, and 1-Oct of each
    // year.
    QUARTER = 5;
  
    // A half-year. Half-years start on dates 1-Jan and 1-Jul.
    HALF = 6;
  
    // A year.
    YEAR = 7;
}
  
enum DayOfWeek {
    // The day of the week is unspecified.
    DAY_OF_WEEK_UNSPECIFIED = 0;
  
    // Monday
    MONDAY = 1;
  
    // Tuesday
    TUESDAY = 2;
  
    // Wednesday
    WEDNESDAY = 3;
  
    // Thursday
    THURSDAY = 4;
  
    // Friday
    FRIDAY = 5;
  
    // Saturday
    SATURDAY = 6;
  
    // Sunday
    SUNDAY = 7;
}
