import * as React from "react";
import type { SVGProps } from "react";
const SvgUtility = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="currentColor"
    viewBox="0 0 25 30"
    {...props}
  >
    <path d="M2.114 28.354a2.34 2.34 0 0 0 2.511-.3c.206-.183.474-.28.75-.27.47 0 .668.168 1.091.583a3.245 3.245 0 0 0 4.959 0c.428-.415.62-.583 1.089-.583.47 0 .66.168 1.088.583a3.24 3.24 0 0 0 4.95 0c.428-.415.62-.583 1.09-.583.269-.01.532.083.735.261a2.35 2.35 0 0 0 2.517.295c.396-.19.729-.491.962-.866a2.35 2.35 0 0 0 .35-1.253V2.619c0-.626-.246-1.227-.684-1.67a2.33 2.33 0 0 0-1.651-.696H3.138c-.62 0-1.216.25-1.654.694a2.38 2.38 0 0 0-.687 1.672V26.23c-.003.444.12.88.352 1.256.234.376.568.677.965.868m8.384-22.692h7.359c.266 0 .521.107.71.298a1.02 1.02 0 0 1 0 1.434 1 1 0 0 1-.71.297h-7.36a1 1 0 0 1-.71-.297 1.02 1.02 0 0 1 0-1.434 1 1 0 0 1 .71-.298m0 6.763h7.359c.266 0 .521.106.71.297a1.02 1.02 0 0 1 0 1.434 1 1 0 0 1-.71.297h-7.36a1 1 0 0 1-.71-.297 1.02 1.02 0 0 1 0-1.434 1 1 0 0 1 .71-.297m0 6.762h7.359c.266 0 .521.107.71.297a1.02 1.02 0 0 1 0 1.434 1 1 0 0 1-.71.297h-7.36a1 1 0 0 1-.71-.297 1.02 1.02 0 0 1 0-1.434 1 1 0 0 1 .71-.297M7.152 5.324c.265 0 .524.08.744.228s.391.36.493.607a1.37 1.37 0 0 1-.29 1.474 1.33 1.33 0 0 1-1.459.293 1.34 1.34 0 0 1-.6-.498 1.36 1.36 0 0 1 .166-1.708 1.33 1.33 0 0 1 .946-.396m0 6.762c.265 0 .524.08.744.228a1.366 1.366 0 0 1 .203 2.08 1.33 1.33 0 0 1-1.459.294 1.34 1.34 0 0 1-.6-.498 1.36 1.36 0 0 1 .166-1.707 1.33 1.33 0 0 1 .946-.397m0 6.763c.265 0 .524.079.744.228a1.366 1.366 0 0 1 .203 2.08 1.33 1.33 0 0 1-1.459.294 1.34 1.34 0 0 1-.6-.498 1.36 1.36 0 0 1 .166-1.708 1.33 1.33 0 0 1 .946-.396" />
  </svg>
);
export default SvgUtility;
