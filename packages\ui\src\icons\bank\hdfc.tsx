import * as React from "react";
import type { SVGProps } from "react";
const SvgHdfc = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    width="1em"
    height="1em"
    {...props}
  >
    <path fill="#ED232A" d="M0 0h24v24H0z" />
    <path fill="#fff" d="M4.2 4.2h15.602v15.602H4.201z" />
    <path fill="#fff" d="M10.8 0h2.4v24h-2.4z" />
    <path fill="#fff" d="M0 10.803h24v2.4H0z" />
    <path fill="#004C8F" d="M8.401 8.401h7.2v7.2h-7.2z" />
  </svg>
);
export default SvgHdfc;
