import { Page } from "@playwright/test";
import { AxeBuilder } from "@axe-core/playwright";

// Define the result type for accessibility tests
interface AccessibilityResults {
  passes: any[];
  violations: any[];
  incomplete: any[];
  inapplicable: any[];
}

/**
 * Options for accessibility testing
 */
export interface AccessibilityOptions {
  /** Include only specific rules */
  includeRules?: string[];
  /** Exclude specific rules */
  excludeRules?: string[];
  /** Selector to include for testing */
  include?: string;
  /** Selector to exclude from testing */
  exclude?: string;
}

/**
 * Run accessibility tests on a page
 * @param page - Playwright page
 * @param options - Accessibility options
 */
export async function runAccessibilityTests(
  page: Page,
  options: AccessibilityOptions = {},
): Promise<AccessibilityResults> {
  const { includeRules, excludeRules, include, exclude } = options;

  try {
    // Create axe builder
    let axeBuilder = new AxeBuilder({ page });

    // Configure rules
    if (includeRules?.length) {
      axeBuilder = axeBuilder.withRules(includeRules);
    }

    if (excludeRules?.length) {
      axeBuilder = axeBuilder.disableRules(excludeRules);
    }

    // Configure selectors
    if (include) {
      axeBuilder = axeBuilder.include(include);
    }

    if (exclude) {
      axeBuilder = axeBuilder.exclude(exclude);
    }

    // Make sure page is ready
    await page.waitForLoadState("domcontentloaded");

    // Run analysis with timeout and retry
    let attempts = 0;
    const maxAttempts = 3;
    let accessibilityScanResults: AccessibilityResults | undefined;

    while (attempts < maxAttempts) {
      try {
        accessibilityScanResults = await axeBuilder.analyze();
        break;
      } catch (error) {
        attempts++;
        if (attempts >= maxAttempts) {
          throw error;
        }
        console.log(
          `Retry ${attempts}/${maxAttempts} for accessibility analysis`,
        );
        await page.waitForTimeout(1000); // Wait before retry
      }
    }

    // If we got here without results, return empty results
    if (!accessibilityScanResults) {
      return {
        passes: [],
        violations: [],
        incomplete: [],
        inapplicable: [],
      };
    }

    return accessibilityScanResults;
  } catch (error) {
    console.log("Error in runAccessibilityTests:", error);
    // Return empty results instead of failing
    return {
      passes: [],
      violations: [],
      incomplete: [],
      inapplicable: [],
    };
  }
}

/**
 * Run accessibility tests on a specific component
 * @param page - Playwright page
 * @param selector - CSS selector for the component
 * @param options - Accessibility options
 */
export async function runComponentAccessibilityTests(
  page: Page,
  selector: string,
  options: AccessibilityOptions = {},
): Promise<AccessibilityResults> {
  // First check if the selector exists on the page
  const elementCount = await page.locator(selector).count();
  if (elementCount === 0) {
    console.log(`Warning: No elements found for selector "${selector}"`);
    // Return empty results instead of failing
    return {
      passes: [],
      violations: [],
      incomplete: [],
      inapplicable: [],
    };
  }

  try {
    return await runAccessibilityTests(page, {
      ...options,
      include: selector,
    });
  } catch (error) {
    console.log(
      `Error running accessibility tests for selector "${selector}":`,
      error,
    );
    // Return empty results instead of failing
    return {
      passes: [],
      violations: [],
      incomplete: [],
      inapplicable: [],
    };
  }
}
