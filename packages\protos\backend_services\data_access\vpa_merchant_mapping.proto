syntax = "proto3";
package backend_services.data_access;

service VpaMerchantMapping {
    rpc GetVpaNameAtBank(GetVpaNameAtBankRequest) returns (GetVpaNameAtBankResponse) {};
    rpc UpsertVpaNameAtBankRecord(UpsertVpaNameAtBankRecordRequest) returns (UpsertVpaNameAtBankRecordResponse) {};
}

message GetVpaNameAtBankRequest {
    repeated string vpas = 1;
}

message GetVpaNameAtBankResponse {
    string name_at_bank = 1;
}

message UpsertVpaNameAtBankRecordRequest {
    string vpa = 1;
    string name_at_bank = 2;
}

message UpsertVpaNameAtBankRecordResponse {}