syntax = "proto3";
package backend_services.data_access.public_data;
import "database/public_data.proto";

// Service that includes RPCs for adding ETF data.
service PublicDataETF {
    rpc AddETFPrice(ETFPriceRequest) returns (ETFPriceResponse) {};
    rpc AddETFInfo(ETFInfoRequest) returns (ETFInfoResponse) {};
    rpc AddETFInfoGroww(stream AddETFInfoGrowwRequest) returns (AddETFInfoGrowwResponse);
}

message ETFPriceRequest{
    repeated database.ETFPrice records = 1;
}
message ETFPriceResponse{}

message ETFInfoRequest{
    repeated database.ETFInfo info_records = 1;
}
message ETFInfoResponse{}

message AddETFInfoGrowwRequest{
    database.ETFInfoGroww record = 1;
}

message AddETFInfoGrowwResponse{}