syntax = "proto3";
package rebit.fi_schemas.equities;

/*
  REBIT FI Type - Equities Schema as per https://specifications.rebit.org.in/api_schema/account_aggregator/documentation/equities.html
*/


message Account {
  string linked_acc_ref = 1; // @gotags: json:"linkedAccRef"
  // Demat ID assigned or issued to investor
  string masked_demat_id = 2; // @gotags: json:"maskedDematId"
  float version = 3; // @gotags: json:"version,string"
  string type = 4; // @gotags: json:"type"
  Profile profile = 5; // @gotags: json:"Profile"
  Summary summary = 6; // @gotags: json:"Summary"
  Transactions transactions = 7; // @gotags: json:"Transactions"
}

message Holder {
  // Name of primary holder operating the account
  string name = 1; // @gotags: json:"name"
  // Date of birth of primary account holder
  DateOfBirth dob = 2; // @gotags: json:"dob"
  // Primary mobile number of account holder
  string mobile = 3; // @gotags: json:"mobile"
  // Status whether nominee is registered or not for the account
  string nominee = 4; // @gotags: json:"nominee"
  // Demat ID assigned or issued to investor
  string demat_id = 5; // @gotags: json:"dematId"
  // Landline number of primary account holder
  string landline = 6; // @gotags: json:"landline"
  // Address of primary account holder
  string address = 7; // @gotags: json:"address"
  // Email ID of primary account holder
  string email = 8; // @gotags: json:"email"
  // PAN number of primary account holder
  string pan = 9; // @gotags: json:"pan"
  // KYC status whether its completed or pending
  // Note: This differs between ReBIT and Finarkein.
  bool kyc_compliance = 10; // @gotags: json:"ckycCompliance"

  // Note: This differs between TSP and ReBIT
  string masked_acc_number = 11; // @gotags: json:"maskedAccNumber"
  string linked_acc_ref = 12; // @gotags: json:"linkedAccRef"

  message DateOfBirth {
    int64 value = 1;
  }
}
message Holders {
  repeated Holder holder = 1; // @gotags: json:"Holder"
}

message Holding {
  // Name of party who issued the equities in which investment made
  string issuer_name = 1; // @gotags: json:"issuerName"
  // International Securities Identification Number (ISIN) uniquely identifies a security.
  string isin = 2; // @gotags: json:"isin"
  // Long Name of ISIN
  string isin_description = 3; // @gotags: json:"isinDescription"
  // Units allotted in folio till date
  uint64 units = 4; // @gotags: json:"units"
  // Last trade price of security or closing price
  string last_traded_price = 5; // @gotags: json:"lastTradedPrice"
  // Type of Holding.
  // Note: This differs between Finarkein and ReBIT
  string type = 6; // @gotags: json:"type"
}
message Holdings {
  string type = 1; // @gotags: json:"type"
  repeated Holding holding = 2; // @gotags: json:"Holding"
}
message Investment {
  Holdings holdings = 1; // @gotags: json:"Holdings"
}
message Profile {
  Holders holders = 1; // @gotags: json:"Holders"
}
message Summary {
  // Current value of investment as on date.
  // Note: Finarkein does not return this and instead returns holding-wise current value,
  // but we anyway don't use it as we rely on public data to get live value.
  double current_value = 1; // @gotags: json:"currentValue"
  Investment investment = 2; // @gotags: json:"Investment"
}
message Transaction {
  // Transaction ID recorded or captured for investment made
  string txn_id = 1; // @gotags: json:"txnId"
  // Order ID which generated at the time of placing the order
  string order_id = 2; // @gotags: json:"orderId"
  // Name of company in which investment made
  string company_name = 3; // @gotags: json:"companyName"
  // Transaction date time stamp for particular record when investment taken place
  TransactionDateTime transaction_date_time = 4; // @gotags: json:"transactionDateTime"
  // Stock Exchange on which security is traded
  string exchange = 5; // @gotags: json:"exchange"
  // International Securities Identification Number (ISIN) uniquely identifies a security.
  string isin = 6; // @gotags: json:"isin"
  // Long Name of ISIN
  string isin_description = 7; // @gotags: json:"isinDescription"
  string equity_category = 8; // @gotags: json:"equityCategory"
  // Narration is additional details in form of description of remark associated with investment
  string narration = 9; // @gotags: json:"narration"
  // Rate or net price on which investment made in security
  string rate = 10; // @gotags: json:"rate"
  // Transaction type may be BUY or SELL
  string type = 11; // @gotags: json:"type"
  // Units which are allotted in the portfolio of investor
  uint64 units = 12; // @gotags: json:"units"

  message TransactionDateTime {
    int64 value = 1;
  }
}
message Transactions {
  // The date from which the Financial Information was requested
  string start_date = 1; // @gotags: json:"startDate"
  // The date till which the Financial Information was requested
  string end_date = 2; // @gotags: json:"endDate"
  repeated Transaction transaction = 3; // @gotags: json:"Transaction"
}

