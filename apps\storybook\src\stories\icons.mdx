import { Meta, IconGallery, IconItem } from "@storybook/blocks";

import * as icons from "@repo/ui/icons";
import * as categoriesIcons from "@repo/ui/icons/categories";
import * as bankIcons from "@repo/ui/icons/bank";

<Meta title="Icons" />

# Incoming/Outgoing

<IconGallery>
  {Object.entries(categoriesIcons).map(([key, Icon]) => (
    <IconItem key={key} name={key}>
      <Icon className="text-[#C4A4DC]" />
    </IconItem>
  ))}
</IconGallery>

# General

<IconGallery>
  {Object.entries(icons).map(([key, Icon]) => (
    <IconItem name={key}>
      <Icon className="text-[#C4A4DC]" />
    </IconItem>
  ))}
</IconGallery>

# Bank

<IconGallery>
  {Object.entries(bankIcons).map(([key, Icon]) => (
    <IconItem name={key}>
      <Icon className="text-[#C4A4DC]" />
    </IconItem>
  ))}
</IconGallery>
