syntax = "proto3";
package backend_services.data_access.public_data;
import "database/public_data.proto";

// Service that includes RPCs for adding stocks data.
service PublicDataStock {
    rpc AddDailyStockPrice(DailyStockPriceRequest) returns (DailyStockPriceResponse) {};
    rpc GetStockPrice(StockPriceRequest) returns (StockPriceResponse) {};
    rpc GetStockPrices(GetStockPricesRequest) returns (GetStockPricesResponse) {};
    rpc AddStockInfo(StockInfoRequest) returns (StockInfoResponse){};
    rpc GetDividends(GetDividendsRequest) returns (GetDividendsResponse){};
    rpc AddPeRatio(PeRatioRequest) returns (PeRatioResponse){};
    rpc AddPriceChange(PriceChangeRequest) returns (PriceChangeResponse){};
    rpc AddStockInfoGroww(StockInfoGrowwRequest) returns (StockInfoGrowwResponse){};
}

message GetDividendsRequest{
    repeated string isin = 1;
    int64 from_date = 2;
    int64 to_date = 3;
}

message GetDividendsResponse{
    repeated database.StockDividend records = 1;
}

message StockPrice{
    string stock_exchange = 1;
    int64 date = 2;
    string isin_code = 3;
    string open_price = 4;
    string high_price = 5;
    string low_price = 6;
    string close_price = 7;
    string last_price = 8;
    int64 created_at = 9;
    int64 updated_at = 10;
}

message StockInfo{
    string isin_code = 1;
    string stock_exchange = 2;
    string tracker_symbol = 3;
    string financial_instrument_id = 4;
    string financial_instrument_name = 5;
    string image_path = 6;
}

message DailyStockPriceRequest{
    repeated StockPrice records = 2;
}
message DailyStockPriceResponse{}

message StockPriceRequest{
    string isin = 1;
    string stock_exchange = 2;
    int64 date = 3;
}
message StockPriceResponse{
    database.StockPrice record = 1;
}

message GetStockPricesRequest {
    repeated string isin = 1;
    string stock_exchange = 2;
    int64 from_date = 3;
    int64 to_date = 4;
}
message GetStockPricesResponse {
    repeated database.StockPrice records = 1; 
}
message StockInfoRequest{
    repeated StockInfo info_records = 1;
}

message StockInfoResponse{}

message PeRatioRequest{
    map<string, float> nse_pe_ratio = 1;
    map<string, float> bse_pe_ratio = 2;
}

message PeRatioResponse{
}

message PriceChangeRequest{
    int64 change_date = 1;
    map<string, database.PriceChange> nse_changes = 2;
    map<string, database.PriceChange> bse_changes = 3;
}

message PriceChangeResponse{}


message StockInfoGrowwRequest{
    map<string, database.StockInfoGroww> stock_info_groww = 1;
}

message StockInfoGrowwResponse{}
