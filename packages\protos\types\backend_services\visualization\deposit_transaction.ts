// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.2
//   protoc               v6.31.1
// source: backend_services/visualization/deposit_transaction.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import {
  type CallOptions,
  ChannelCredentials,
  Client,
  type ClientOptions,
  type ClientUnaryCall,
  type handleUnaryCall,
  makeGenericClientConstructor,
  Metadata,
  type ServiceError,
  type UntypedServiceImplementation,
} from "@grpc/grpc-js";
import { AccountsFilter, UserGroupFilters } from "../../database/cards_filters";
import { CashFlowPeriod, TxnFilters, TxnPaginationParams } from "../../database/fi_deposit";

export const protobufPackage = "backend_services.visualization";

export interface Tag {
  /** Collection in which category belongs: "global" or <uid> */
  categoryCollection: string;
  /** Document Id of category collection */
  categoryId: string;
  /** Document Id of subcategory collection */
  subcategoryId: string;
}

export interface Merchant {
  userId: string;
  merchantId: string;
  merchantName: string;
}

export interface DepositTransactionCard {
  /** Transaction ID of our system */
  txnId: string;
  /** Account ID of our system */
  accountId: string;
  /** Transaction amount */
  amount: number;
  /** "CREDIT" or "DEBIT" */
  type: string;
  /** When was the txn carried out */
  txnTimestamp: number;
  /** "UPI", "ATM" etc. */
  mode: string;
  /** Remarks */
  narration: string;
  /** Transaction ID in bank */
  rawTxnId: string;
  /** Whether user has favorited this txn */
  favorite: boolean;
  tag: Tag | undefined;
  merchant:
    | Merchant
    | undefined;
  /** Whether transaction is excluded from cash-flow */
  excludeCashFlow: boolean;
  /** FIP ID of the account */
  fipId: string;
  /** User added notes */
  userNotes: string;
  /** Accounted in which period in cashflow */
  cashFlowPeriod: CashFlowPeriod | undefined;
  documentsCount: number;
  /** Recurrent txn group id */
  recurrentGroupId: string;
}

export interface DepositTxnCardFilters {
  userGroups: UserGroupFilters | undefined;
  accountFilters: AccountsFilter | undefined;
  txnFilters: TxnFilters | undefined;
}

export interface FetchDepositTxnsRequest {
  filter: DepositTxnCardFilters | undefined;
  paginationParams: TxnPaginationParams | undefined;
}

export interface FetchDepositTxnsResponse {
  cards: DepositTransactionCard[];
  totalCount: number;
}

export interface AssignCategoryToDepositTxnsRequest {
  transactionIds: string[];
  collection: string;
  categoryId: string;
  subcategoryId: string;
}

export interface AssignCategoryToDepositTxnsResponse {
}

export interface AssignMerchantToDepositTxnsRequest {
  transactionIds: string[];
  merchantId: string;
}

export interface AssignMerchantToDepositTxnsResponse {
}

export interface MarkDepositTxnFavoriteRequest {
  transactionId: string;
  favorite: boolean;
}

export interface MarkDepositTxnFavoriteResponse {
}

export interface UpdateTransactionNotesRequest {
  transactionId: string;
  notes: string;
}

export interface UpdateTransactionNotesResponse {
}

export interface UpdateTxnCashFlowPeriodRequest {
  txnId: string;
  cashFlowPeriod: CashFlowPeriod | undefined;
}

export interface UpdateTxnCashFlowPeriodResponse {
}

export interface ExcludeTxnFromCashFlowRequest {
  txnId: string;
  excludeCashFlow: boolean;
}

export interface ExcludeTxnFromCashFlowResponse {
}

export interface Document {
  name: string;
  objectName: string;
  /** Base64 encoded */
  thumbnail: string;
  signedUrl: string;
  contentType: string;
}

export interface FetchTransactionDocumentsRequest {
  transactionId: string;
}

export interface FetchTransactionDocumentsResponse {
  documents: Document[];
}

export interface DeleteTransactionDocumentsRequest {
  transactionId: string;
  objectNames: string[];
}

export interface DeleteTransactionDocumentsResponse {
}

export interface SearchTxnsRequest {
  input: string;
  searchBy: SearchTxnsRequest_SearchBy;
  filter: DepositTxnCardFilters | undefined;
  paginationParams: TxnPaginationParams | undefined;
}

export enum SearchTxnsRequest_SearchBy {
  SEARCH_BY_ALL = 0,
  SEARCH_BY_TAG = 1,
  SEARCH_BY_MERCHANT = 2,
  SEARCH_BY_USER = 3,
  SEARCH_BY_NOTES = 4,
  SEARCH_BY_AMOUNT = 5,
  SEARCH_BY_REMARKS = 6,
  UNRECOGNIZED = -1,
}

export function searchTxnsRequest_SearchByFromJSON(object: any): SearchTxnsRequest_SearchBy {
  switch (object) {
    case 0:
    case "SEARCH_BY_ALL":
      return SearchTxnsRequest_SearchBy.SEARCH_BY_ALL;
    case 1:
    case "SEARCH_BY_TAG":
      return SearchTxnsRequest_SearchBy.SEARCH_BY_TAG;
    case 2:
    case "SEARCH_BY_MERCHANT":
      return SearchTxnsRequest_SearchBy.SEARCH_BY_MERCHANT;
    case 3:
    case "SEARCH_BY_USER":
      return SearchTxnsRequest_SearchBy.SEARCH_BY_USER;
    case 4:
    case "SEARCH_BY_NOTES":
      return SearchTxnsRequest_SearchBy.SEARCH_BY_NOTES;
    case 5:
    case "SEARCH_BY_AMOUNT":
      return SearchTxnsRequest_SearchBy.SEARCH_BY_AMOUNT;
    case 6:
    case "SEARCH_BY_REMARKS":
      return SearchTxnsRequest_SearchBy.SEARCH_BY_REMARKS;
    case -1:
    case "UNRECOGNIZED":
    default:
      return SearchTxnsRequest_SearchBy.UNRECOGNIZED;
  }
}

export function searchTxnsRequest_SearchByToJSON(object: SearchTxnsRequest_SearchBy): string {
  switch (object) {
    case SearchTxnsRequest_SearchBy.SEARCH_BY_ALL:
      return "SEARCH_BY_ALL";
    case SearchTxnsRequest_SearchBy.SEARCH_BY_TAG:
      return "SEARCH_BY_TAG";
    case SearchTxnsRequest_SearchBy.SEARCH_BY_MERCHANT:
      return "SEARCH_BY_MERCHANT";
    case SearchTxnsRequest_SearchBy.SEARCH_BY_USER:
      return "SEARCH_BY_USER";
    case SearchTxnsRequest_SearchBy.SEARCH_BY_NOTES:
      return "SEARCH_BY_NOTES";
    case SearchTxnsRequest_SearchBy.SEARCH_BY_AMOUNT:
      return "SEARCH_BY_AMOUNT";
    case SearchTxnsRequest_SearchBy.SEARCH_BY_REMARKS:
      return "SEARCH_BY_REMARKS";
    case SearchTxnsRequest_SearchBy.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface SearchTxnsResponse {
  cards: DepositTransactionCard[];
}

export interface FindSimilarTxnsForTaggingRequest {
  txnId: string;
  identifierKeywords: string[];
}

export interface FindSimilarTxnsForTaggingResponse {
  cards: DepositTransactionCard[];
}

export interface SearchSuggestionsRequest {
  input: string;
}

export interface SearchSuggestionsResponse {
  suggestions: SearchSuggestionsResponse_Suggestion[];
}

export interface SearchSuggestionsResponse_Suggestion {
  keyword: string;
  merchantIds: string[];
  tags: SearchSuggestionsResponse_Tag[];
}

export interface SearchSuggestionsResponse_Tag {
  categoryCollection: string;
  categoryId: string;
  subcategoryId: string;
}

function createBaseTag(): Tag {
  return { categoryCollection: "", categoryId: "", subcategoryId: "" };
}

export const Tag: MessageFns<Tag> = {
  encode(message: Tag, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.categoryCollection !== "") {
      writer.uint32(10).string(message.categoryCollection);
    }
    if (message.categoryId !== "") {
      writer.uint32(18).string(message.categoryId);
    }
    if (message.subcategoryId !== "") {
      writer.uint32(26).string(message.subcategoryId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Tag {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTag();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.categoryCollection = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.categoryId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.subcategoryId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Tag {
    return {
      categoryCollection: isSet(object.categoryCollection) ? globalThis.String(object.categoryCollection) : "",
      categoryId: isSet(object.categoryId) ? globalThis.String(object.categoryId) : "",
      subcategoryId: isSet(object.subcategoryId) ? globalThis.String(object.subcategoryId) : "",
    };
  },

  toJSON(message: Tag): unknown {
    const obj: any = {};
    if (message.categoryCollection !== "") {
      obj.categoryCollection = message.categoryCollection;
    }
    if (message.categoryId !== "") {
      obj.categoryId = message.categoryId;
    }
    if (message.subcategoryId !== "") {
      obj.subcategoryId = message.subcategoryId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Tag>, I>>(base?: I): Tag {
    return Tag.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Tag>, I>>(object: I): Tag {
    const message = createBaseTag();
    message.categoryCollection = object.categoryCollection ?? "";
    message.categoryId = object.categoryId ?? "";
    message.subcategoryId = object.subcategoryId ?? "";
    return message;
  },
};

function createBaseMerchant(): Merchant {
  return { userId: "", merchantId: "", merchantName: "" };
}

export const Merchant: MessageFns<Merchant> = {
  encode(message: Merchant, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userId !== "") {
      writer.uint32(10).string(message.userId);
    }
    if (message.merchantId !== "") {
      writer.uint32(18).string(message.merchantId);
    }
    if (message.merchantName !== "") {
      writer.uint32(26).string(message.merchantName);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Merchant {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMerchant();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.merchantId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.merchantName = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Merchant {
    return {
      userId: isSet(object.userId) ? globalThis.String(object.userId) : "",
      merchantId: isSet(object.merchantId) ? globalThis.String(object.merchantId) : "",
      merchantName: isSet(object.merchantName) ? globalThis.String(object.merchantName) : "",
    };
  },

  toJSON(message: Merchant): unknown {
    const obj: any = {};
    if (message.userId !== "") {
      obj.userId = message.userId;
    }
    if (message.merchantId !== "") {
      obj.merchantId = message.merchantId;
    }
    if (message.merchantName !== "") {
      obj.merchantName = message.merchantName;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Merchant>, I>>(base?: I): Merchant {
    return Merchant.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Merchant>, I>>(object: I): Merchant {
    const message = createBaseMerchant();
    message.userId = object.userId ?? "";
    message.merchantId = object.merchantId ?? "";
    message.merchantName = object.merchantName ?? "";
    return message;
  },
};

function createBaseDepositTransactionCard(): DepositTransactionCard {
  return {
    txnId: "",
    accountId: "",
    amount: 0,
    type: "",
    txnTimestamp: 0,
    mode: "",
    narration: "",
    rawTxnId: "",
    favorite: false,
    tag: undefined,
    merchant: undefined,
    excludeCashFlow: false,
    fipId: "",
    userNotes: "",
    cashFlowPeriod: undefined,
    documentsCount: 0,
    recurrentGroupId: "",
  };
}

export const DepositTransactionCard: MessageFns<DepositTransactionCard> = {
  encode(message: DepositTransactionCard, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.txnId !== "") {
      writer.uint32(10).string(message.txnId);
    }
    if (message.accountId !== "") {
      writer.uint32(18).string(message.accountId);
    }
    if (message.amount !== 0) {
      writer.uint32(25).double(message.amount);
    }
    if (message.type !== "") {
      writer.uint32(34).string(message.type);
    }
    if (message.txnTimestamp !== 0) {
      writer.uint32(40).int64(message.txnTimestamp);
    }
    if (message.mode !== "") {
      writer.uint32(50).string(message.mode);
    }
    if (message.narration !== "") {
      writer.uint32(58).string(message.narration);
    }
    if (message.rawTxnId !== "") {
      writer.uint32(66).string(message.rawTxnId);
    }
    if (message.favorite !== false) {
      writer.uint32(72).bool(message.favorite);
    }
    if (message.tag !== undefined) {
      Tag.encode(message.tag, writer.uint32(82).fork()).join();
    }
    if (message.merchant !== undefined) {
      Merchant.encode(message.merchant, writer.uint32(90).fork()).join();
    }
    if (message.excludeCashFlow !== false) {
      writer.uint32(104).bool(message.excludeCashFlow);
    }
    if (message.fipId !== "") {
      writer.uint32(122).string(message.fipId);
    }
    if (message.userNotes !== "") {
      writer.uint32(130).string(message.userNotes);
    }
    if (message.cashFlowPeriod !== undefined) {
      CashFlowPeriod.encode(message.cashFlowPeriod, writer.uint32(138).fork()).join();
    }
    if (message.documentsCount !== 0) {
      writer.uint32(144).int32(message.documentsCount);
    }
    if (message.recurrentGroupId !== "") {
      writer.uint32(154).string(message.recurrentGroupId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DepositTransactionCard {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDepositTransactionCard();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.txnId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.accountId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.amount = reader.double();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.type = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.txnTimestamp = longToNumber(reader.int64());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.mode = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.narration = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.rawTxnId = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.favorite = reader.bool();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.tag = Tag.decode(reader, reader.uint32());
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.merchant = Merchant.decode(reader, reader.uint32());
          continue;
        }
        case 13: {
          if (tag !== 104) {
            break;
          }

          message.excludeCashFlow = reader.bool();
          continue;
        }
        case 15: {
          if (tag !== 122) {
            break;
          }

          message.fipId = reader.string();
          continue;
        }
        case 16: {
          if (tag !== 130) {
            break;
          }

          message.userNotes = reader.string();
          continue;
        }
        case 17: {
          if (tag !== 138) {
            break;
          }

          message.cashFlowPeriod = CashFlowPeriod.decode(reader, reader.uint32());
          continue;
        }
        case 18: {
          if (tag !== 144) {
            break;
          }

          message.documentsCount = reader.int32();
          continue;
        }
        case 19: {
          if (tag !== 154) {
            break;
          }

          message.recurrentGroupId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DepositTransactionCard {
    return {
      txnId: isSet(object.txnId) ? globalThis.String(object.txnId) : "",
      accountId: isSet(object.accountId) ? globalThis.String(object.accountId) : "",
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      type: isSet(object.type) ? globalThis.String(object.type) : "",
      txnTimestamp: isSet(object.txnTimestamp) ? globalThis.Number(object.txnTimestamp) : 0,
      mode: isSet(object.mode) ? globalThis.String(object.mode) : "",
      narration: isSet(object.narration) ? globalThis.String(object.narration) : "",
      rawTxnId: isSet(object.rawTxnId) ? globalThis.String(object.rawTxnId) : "",
      favorite: isSet(object.favorite) ? globalThis.Boolean(object.favorite) : false,
      tag: isSet(object.tag) ? Tag.fromJSON(object.tag) : undefined,
      merchant: isSet(object.merchant) ? Merchant.fromJSON(object.merchant) : undefined,
      excludeCashFlow: isSet(object.excludeCashFlow) ? globalThis.Boolean(object.excludeCashFlow) : false,
      fipId: isSet(object.fipId) ? globalThis.String(object.fipId) : "",
      userNotes: isSet(object.userNotes) ? globalThis.String(object.userNotes) : "",
      cashFlowPeriod: isSet(object.cashFlowPeriod) ? CashFlowPeriod.fromJSON(object.cashFlowPeriod) : undefined,
      documentsCount: isSet(object.documentsCount) ? globalThis.Number(object.documentsCount) : 0,
      recurrentGroupId: isSet(object.recurrentGroupId) ? globalThis.String(object.recurrentGroupId) : "",
    };
  },

  toJSON(message: DepositTransactionCard): unknown {
    const obj: any = {};
    if (message.txnId !== "") {
      obj.txnId = message.txnId;
    }
    if (message.accountId !== "") {
      obj.accountId = message.accountId;
    }
    if (message.amount !== 0) {
      obj.amount = message.amount;
    }
    if (message.type !== "") {
      obj.type = message.type;
    }
    if (message.txnTimestamp !== 0) {
      obj.txnTimestamp = Math.round(message.txnTimestamp);
    }
    if (message.mode !== "") {
      obj.mode = message.mode;
    }
    if (message.narration !== "") {
      obj.narration = message.narration;
    }
    if (message.rawTxnId !== "") {
      obj.rawTxnId = message.rawTxnId;
    }
    if (message.favorite !== false) {
      obj.favorite = message.favorite;
    }
    if (message.tag !== undefined) {
      obj.tag = Tag.toJSON(message.tag);
    }
    if (message.merchant !== undefined) {
      obj.merchant = Merchant.toJSON(message.merchant);
    }
    if (message.excludeCashFlow !== false) {
      obj.excludeCashFlow = message.excludeCashFlow;
    }
    if (message.fipId !== "") {
      obj.fipId = message.fipId;
    }
    if (message.userNotes !== "") {
      obj.userNotes = message.userNotes;
    }
    if (message.cashFlowPeriod !== undefined) {
      obj.cashFlowPeriod = CashFlowPeriod.toJSON(message.cashFlowPeriod);
    }
    if (message.documentsCount !== 0) {
      obj.documentsCount = Math.round(message.documentsCount);
    }
    if (message.recurrentGroupId !== "") {
      obj.recurrentGroupId = message.recurrentGroupId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DepositTransactionCard>, I>>(base?: I): DepositTransactionCard {
    return DepositTransactionCard.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DepositTransactionCard>, I>>(object: I): DepositTransactionCard {
    const message = createBaseDepositTransactionCard();
    message.txnId = object.txnId ?? "";
    message.accountId = object.accountId ?? "";
    message.amount = object.amount ?? 0;
    message.type = object.type ?? "";
    message.txnTimestamp = object.txnTimestamp ?? 0;
    message.mode = object.mode ?? "";
    message.narration = object.narration ?? "";
    message.rawTxnId = object.rawTxnId ?? "";
    message.favorite = object.favorite ?? false;
    message.tag = (object.tag !== undefined && object.tag !== null) ? Tag.fromPartial(object.tag) : undefined;
    message.merchant = (object.merchant !== undefined && object.merchant !== null)
      ? Merchant.fromPartial(object.merchant)
      : undefined;
    message.excludeCashFlow = object.excludeCashFlow ?? false;
    message.fipId = object.fipId ?? "";
    message.userNotes = object.userNotes ?? "";
    message.cashFlowPeriod = (object.cashFlowPeriod !== undefined && object.cashFlowPeriod !== null)
      ? CashFlowPeriod.fromPartial(object.cashFlowPeriod)
      : undefined;
    message.documentsCount = object.documentsCount ?? 0;
    message.recurrentGroupId = object.recurrentGroupId ?? "";
    return message;
  },
};

function createBaseDepositTxnCardFilters(): DepositTxnCardFilters {
  return { userGroups: undefined, accountFilters: undefined, txnFilters: undefined };
}

export const DepositTxnCardFilters: MessageFns<DepositTxnCardFilters> = {
  encode(message: DepositTxnCardFilters, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userGroups !== undefined) {
      UserGroupFilters.encode(message.userGroups, writer.uint32(10).fork()).join();
    }
    if (message.accountFilters !== undefined) {
      AccountsFilter.encode(message.accountFilters, writer.uint32(18).fork()).join();
    }
    if (message.txnFilters !== undefined) {
      TxnFilters.encode(message.txnFilters, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DepositTxnCardFilters {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDepositTxnCardFilters();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userGroups = UserGroupFilters.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.accountFilters = AccountsFilter.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.txnFilters = TxnFilters.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DepositTxnCardFilters {
    return {
      userGroups: isSet(object.userGroups) ? UserGroupFilters.fromJSON(object.userGroups) : undefined,
      accountFilters: isSet(object.accountFilters) ? AccountsFilter.fromJSON(object.accountFilters) : undefined,
      txnFilters: isSet(object.txnFilters) ? TxnFilters.fromJSON(object.txnFilters) : undefined,
    };
  },

  toJSON(message: DepositTxnCardFilters): unknown {
    const obj: any = {};
    if (message.userGroups !== undefined) {
      obj.userGroups = UserGroupFilters.toJSON(message.userGroups);
    }
    if (message.accountFilters !== undefined) {
      obj.accountFilters = AccountsFilter.toJSON(message.accountFilters);
    }
    if (message.txnFilters !== undefined) {
      obj.txnFilters = TxnFilters.toJSON(message.txnFilters);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DepositTxnCardFilters>, I>>(base?: I): DepositTxnCardFilters {
    return DepositTxnCardFilters.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DepositTxnCardFilters>, I>>(object: I): DepositTxnCardFilters {
    const message = createBaseDepositTxnCardFilters();
    message.userGroups = (object.userGroups !== undefined && object.userGroups !== null)
      ? UserGroupFilters.fromPartial(object.userGroups)
      : undefined;
    message.accountFilters = (object.accountFilters !== undefined && object.accountFilters !== null)
      ? AccountsFilter.fromPartial(object.accountFilters)
      : undefined;
    message.txnFilters = (object.txnFilters !== undefined && object.txnFilters !== null)
      ? TxnFilters.fromPartial(object.txnFilters)
      : undefined;
    return message;
  },
};

function createBaseFetchDepositTxnsRequest(): FetchDepositTxnsRequest {
  return { filter: undefined, paginationParams: undefined };
}

export const FetchDepositTxnsRequest: MessageFns<FetchDepositTxnsRequest> = {
  encode(message: FetchDepositTxnsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.filter !== undefined) {
      DepositTxnCardFilters.encode(message.filter, writer.uint32(10).fork()).join();
    }
    if (message.paginationParams !== undefined) {
      TxnPaginationParams.encode(message.paginationParams, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FetchDepositTxnsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFetchDepositTxnsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.filter = DepositTxnCardFilters.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.paginationParams = TxnPaginationParams.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FetchDepositTxnsRequest {
    return {
      filter: isSet(object.filter) ? DepositTxnCardFilters.fromJSON(object.filter) : undefined,
      paginationParams: isSet(object.paginationParams)
        ? TxnPaginationParams.fromJSON(object.paginationParams)
        : undefined,
    };
  },

  toJSON(message: FetchDepositTxnsRequest): unknown {
    const obj: any = {};
    if (message.filter !== undefined) {
      obj.filter = DepositTxnCardFilters.toJSON(message.filter);
    }
    if (message.paginationParams !== undefined) {
      obj.paginationParams = TxnPaginationParams.toJSON(message.paginationParams);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FetchDepositTxnsRequest>, I>>(base?: I): FetchDepositTxnsRequest {
    return FetchDepositTxnsRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FetchDepositTxnsRequest>, I>>(object: I): FetchDepositTxnsRequest {
    const message = createBaseFetchDepositTxnsRequest();
    message.filter = (object.filter !== undefined && object.filter !== null)
      ? DepositTxnCardFilters.fromPartial(object.filter)
      : undefined;
    message.paginationParams = (object.paginationParams !== undefined && object.paginationParams !== null)
      ? TxnPaginationParams.fromPartial(object.paginationParams)
      : undefined;
    return message;
  },
};

function createBaseFetchDepositTxnsResponse(): FetchDepositTxnsResponse {
  return { cards: [], totalCount: 0 };
}

export const FetchDepositTxnsResponse: MessageFns<FetchDepositTxnsResponse> = {
  encode(message: FetchDepositTxnsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.cards) {
      DepositTransactionCard.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.totalCount !== 0) {
      writer.uint32(16).int32(message.totalCount);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FetchDepositTxnsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFetchDepositTxnsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.cards.push(DepositTransactionCard.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.totalCount = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FetchDepositTxnsResponse {
    return {
      cards: globalThis.Array.isArray(object?.cards)
        ? object.cards.map((e: any) => DepositTransactionCard.fromJSON(e))
        : [],
      totalCount: isSet(object.totalCount) ? globalThis.Number(object.totalCount) : 0,
    };
  },

  toJSON(message: FetchDepositTxnsResponse): unknown {
    const obj: any = {};
    if (message.cards?.length) {
      obj.cards = message.cards.map((e) => DepositTransactionCard.toJSON(e));
    }
    if (message.totalCount !== 0) {
      obj.totalCount = Math.round(message.totalCount);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FetchDepositTxnsResponse>, I>>(base?: I): FetchDepositTxnsResponse {
    return FetchDepositTxnsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FetchDepositTxnsResponse>, I>>(object: I): FetchDepositTxnsResponse {
    const message = createBaseFetchDepositTxnsResponse();
    message.cards = object.cards?.map((e) => DepositTransactionCard.fromPartial(e)) || [];
    message.totalCount = object.totalCount ?? 0;
    return message;
  },
};

function createBaseAssignCategoryToDepositTxnsRequest(): AssignCategoryToDepositTxnsRequest {
  return { transactionIds: [], collection: "", categoryId: "", subcategoryId: "" };
}

export const AssignCategoryToDepositTxnsRequest: MessageFns<AssignCategoryToDepositTxnsRequest> = {
  encode(message: AssignCategoryToDepositTxnsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.transactionIds) {
      writer.uint32(10).string(v!);
    }
    if (message.collection !== "") {
      writer.uint32(18).string(message.collection);
    }
    if (message.categoryId !== "") {
      writer.uint32(26).string(message.categoryId);
    }
    if (message.subcategoryId !== "") {
      writer.uint32(34).string(message.subcategoryId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AssignCategoryToDepositTxnsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAssignCategoryToDepositTxnsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.transactionIds.push(reader.string());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.collection = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.categoryId = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.subcategoryId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AssignCategoryToDepositTxnsRequest {
    return {
      transactionIds: globalThis.Array.isArray(object?.transactionIds)
        ? object.transactionIds.map((e: any) => globalThis.String(e))
        : [],
      collection: isSet(object.collection) ? globalThis.String(object.collection) : "",
      categoryId: isSet(object.categoryId) ? globalThis.String(object.categoryId) : "",
      subcategoryId: isSet(object.subcategoryId) ? globalThis.String(object.subcategoryId) : "",
    };
  },

  toJSON(message: AssignCategoryToDepositTxnsRequest): unknown {
    const obj: any = {};
    if (message.transactionIds?.length) {
      obj.transactionIds = message.transactionIds;
    }
    if (message.collection !== "") {
      obj.collection = message.collection;
    }
    if (message.categoryId !== "") {
      obj.categoryId = message.categoryId;
    }
    if (message.subcategoryId !== "") {
      obj.subcategoryId = message.subcategoryId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AssignCategoryToDepositTxnsRequest>, I>>(
    base?: I,
  ): AssignCategoryToDepositTxnsRequest {
    return AssignCategoryToDepositTxnsRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AssignCategoryToDepositTxnsRequest>, I>>(
    object: I,
  ): AssignCategoryToDepositTxnsRequest {
    const message = createBaseAssignCategoryToDepositTxnsRequest();
    message.transactionIds = object.transactionIds?.map((e) => e) || [];
    message.collection = object.collection ?? "";
    message.categoryId = object.categoryId ?? "";
    message.subcategoryId = object.subcategoryId ?? "";
    return message;
  },
};

function createBaseAssignCategoryToDepositTxnsResponse(): AssignCategoryToDepositTxnsResponse {
  return {};
}

export const AssignCategoryToDepositTxnsResponse: MessageFns<AssignCategoryToDepositTxnsResponse> = {
  encode(_: AssignCategoryToDepositTxnsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AssignCategoryToDepositTxnsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAssignCategoryToDepositTxnsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): AssignCategoryToDepositTxnsResponse {
    return {};
  },

  toJSON(_: AssignCategoryToDepositTxnsResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<AssignCategoryToDepositTxnsResponse>, I>>(
    base?: I,
  ): AssignCategoryToDepositTxnsResponse {
    return AssignCategoryToDepositTxnsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AssignCategoryToDepositTxnsResponse>, I>>(
    _: I,
  ): AssignCategoryToDepositTxnsResponse {
    const message = createBaseAssignCategoryToDepositTxnsResponse();
    return message;
  },
};

function createBaseAssignMerchantToDepositTxnsRequest(): AssignMerchantToDepositTxnsRequest {
  return { transactionIds: [], merchantId: "" };
}

export const AssignMerchantToDepositTxnsRequest: MessageFns<AssignMerchantToDepositTxnsRequest> = {
  encode(message: AssignMerchantToDepositTxnsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.transactionIds) {
      writer.uint32(10).string(v!);
    }
    if (message.merchantId !== "") {
      writer.uint32(18).string(message.merchantId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AssignMerchantToDepositTxnsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAssignMerchantToDepositTxnsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.transactionIds.push(reader.string());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.merchantId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AssignMerchantToDepositTxnsRequest {
    return {
      transactionIds: globalThis.Array.isArray(object?.transactionIds)
        ? object.transactionIds.map((e: any) => globalThis.String(e))
        : [],
      merchantId: isSet(object.merchantId) ? globalThis.String(object.merchantId) : "",
    };
  },

  toJSON(message: AssignMerchantToDepositTxnsRequest): unknown {
    const obj: any = {};
    if (message.transactionIds?.length) {
      obj.transactionIds = message.transactionIds;
    }
    if (message.merchantId !== "") {
      obj.merchantId = message.merchantId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AssignMerchantToDepositTxnsRequest>, I>>(
    base?: I,
  ): AssignMerchantToDepositTxnsRequest {
    return AssignMerchantToDepositTxnsRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AssignMerchantToDepositTxnsRequest>, I>>(
    object: I,
  ): AssignMerchantToDepositTxnsRequest {
    const message = createBaseAssignMerchantToDepositTxnsRequest();
    message.transactionIds = object.transactionIds?.map((e) => e) || [];
    message.merchantId = object.merchantId ?? "";
    return message;
  },
};

function createBaseAssignMerchantToDepositTxnsResponse(): AssignMerchantToDepositTxnsResponse {
  return {};
}

export const AssignMerchantToDepositTxnsResponse: MessageFns<AssignMerchantToDepositTxnsResponse> = {
  encode(_: AssignMerchantToDepositTxnsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AssignMerchantToDepositTxnsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAssignMerchantToDepositTxnsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): AssignMerchantToDepositTxnsResponse {
    return {};
  },

  toJSON(_: AssignMerchantToDepositTxnsResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<AssignMerchantToDepositTxnsResponse>, I>>(
    base?: I,
  ): AssignMerchantToDepositTxnsResponse {
    return AssignMerchantToDepositTxnsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AssignMerchantToDepositTxnsResponse>, I>>(
    _: I,
  ): AssignMerchantToDepositTxnsResponse {
    const message = createBaseAssignMerchantToDepositTxnsResponse();
    return message;
  },
};

function createBaseMarkDepositTxnFavoriteRequest(): MarkDepositTxnFavoriteRequest {
  return { transactionId: "", favorite: false };
}

export const MarkDepositTxnFavoriteRequest: MessageFns<MarkDepositTxnFavoriteRequest> = {
  encode(message: MarkDepositTxnFavoriteRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.transactionId !== "") {
      writer.uint32(10).string(message.transactionId);
    }
    if (message.favorite !== false) {
      writer.uint32(16).bool(message.favorite);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MarkDepositTxnFavoriteRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMarkDepositTxnFavoriteRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.transactionId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.favorite = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MarkDepositTxnFavoriteRequest {
    return {
      transactionId: isSet(object.transactionId) ? globalThis.String(object.transactionId) : "",
      favorite: isSet(object.favorite) ? globalThis.Boolean(object.favorite) : false,
    };
  },

  toJSON(message: MarkDepositTxnFavoriteRequest): unknown {
    const obj: any = {};
    if (message.transactionId !== "") {
      obj.transactionId = message.transactionId;
    }
    if (message.favorite !== false) {
      obj.favorite = message.favorite;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MarkDepositTxnFavoriteRequest>, I>>(base?: I): MarkDepositTxnFavoriteRequest {
    return MarkDepositTxnFavoriteRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MarkDepositTxnFavoriteRequest>, I>>(
    object: I,
  ): MarkDepositTxnFavoriteRequest {
    const message = createBaseMarkDepositTxnFavoriteRequest();
    message.transactionId = object.transactionId ?? "";
    message.favorite = object.favorite ?? false;
    return message;
  },
};

function createBaseMarkDepositTxnFavoriteResponse(): MarkDepositTxnFavoriteResponse {
  return {};
}

export const MarkDepositTxnFavoriteResponse: MessageFns<MarkDepositTxnFavoriteResponse> = {
  encode(_: MarkDepositTxnFavoriteResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MarkDepositTxnFavoriteResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMarkDepositTxnFavoriteResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): MarkDepositTxnFavoriteResponse {
    return {};
  },

  toJSON(_: MarkDepositTxnFavoriteResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<MarkDepositTxnFavoriteResponse>, I>>(base?: I): MarkDepositTxnFavoriteResponse {
    return MarkDepositTxnFavoriteResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MarkDepositTxnFavoriteResponse>, I>>(_: I): MarkDepositTxnFavoriteResponse {
    const message = createBaseMarkDepositTxnFavoriteResponse();
    return message;
  },
};

function createBaseUpdateTransactionNotesRequest(): UpdateTransactionNotesRequest {
  return { transactionId: "", notes: "" };
}

export const UpdateTransactionNotesRequest: MessageFns<UpdateTransactionNotesRequest> = {
  encode(message: UpdateTransactionNotesRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.transactionId !== "") {
      writer.uint32(10).string(message.transactionId);
    }
    if (message.notes !== "") {
      writer.uint32(18).string(message.notes);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateTransactionNotesRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateTransactionNotesRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.transactionId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.notes = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdateTransactionNotesRequest {
    return {
      transactionId: isSet(object.transactionId) ? globalThis.String(object.transactionId) : "",
      notes: isSet(object.notes) ? globalThis.String(object.notes) : "",
    };
  },

  toJSON(message: UpdateTransactionNotesRequest): unknown {
    const obj: any = {};
    if (message.transactionId !== "") {
      obj.transactionId = message.transactionId;
    }
    if (message.notes !== "") {
      obj.notes = message.notes;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateTransactionNotesRequest>, I>>(base?: I): UpdateTransactionNotesRequest {
    return UpdateTransactionNotesRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateTransactionNotesRequest>, I>>(
    object: I,
  ): UpdateTransactionNotesRequest {
    const message = createBaseUpdateTransactionNotesRequest();
    message.transactionId = object.transactionId ?? "";
    message.notes = object.notes ?? "";
    return message;
  },
};

function createBaseUpdateTransactionNotesResponse(): UpdateTransactionNotesResponse {
  return {};
}

export const UpdateTransactionNotesResponse: MessageFns<UpdateTransactionNotesResponse> = {
  encode(_: UpdateTransactionNotesResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateTransactionNotesResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateTransactionNotesResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): UpdateTransactionNotesResponse {
    return {};
  },

  toJSON(_: UpdateTransactionNotesResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateTransactionNotesResponse>, I>>(base?: I): UpdateTransactionNotesResponse {
    return UpdateTransactionNotesResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateTransactionNotesResponse>, I>>(_: I): UpdateTransactionNotesResponse {
    const message = createBaseUpdateTransactionNotesResponse();
    return message;
  },
};

function createBaseUpdateTxnCashFlowPeriodRequest(): UpdateTxnCashFlowPeriodRequest {
  return { txnId: "", cashFlowPeriod: undefined };
}

export const UpdateTxnCashFlowPeriodRequest: MessageFns<UpdateTxnCashFlowPeriodRequest> = {
  encode(message: UpdateTxnCashFlowPeriodRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.txnId !== "") {
      writer.uint32(10).string(message.txnId);
    }
    if (message.cashFlowPeriod !== undefined) {
      CashFlowPeriod.encode(message.cashFlowPeriod, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateTxnCashFlowPeriodRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateTxnCashFlowPeriodRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.txnId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.cashFlowPeriod = CashFlowPeriod.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdateTxnCashFlowPeriodRequest {
    return {
      txnId: isSet(object.txnId) ? globalThis.String(object.txnId) : "",
      cashFlowPeriod: isSet(object.cashFlowPeriod) ? CashFlowPeriod.fromJSON(object.cashFlowPeriod) : undefined,
    };
  },

  toJSON(message: UpdateTxnCashFlowPeriodRequest): unknown {
    const obj: any = {};
    if (message.txnId !== "") {
      obj.txnId = message.txnId;
    }
    if (message.cashFlowPeriod !== undefined) {
      obj.cashFlowPeriod = CashFlowPeriod.toJSON(message.cashFlowPeriod);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateTxnCashFlowPeriodRequest>, I>>(base?: I): UpdateTxnCashFlowPeriodRequest {
    return UpdateTxnCashFlowPeriodRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateTxnCashFlowPeriodRequest>, I>>(
    object: I,
  ): UpdateTxnCashFlowPeriodRequest {
    const message = createBaseUpdateTxnCashFlowPeriodRequest();
    message.txnId = object.txnId ?? "";
    message.cashFlowPeriod = (object.cashFlowPeriod !== undefined && object.cashFlowPeriod !== null)
      ? CashFlowPeriod.fromPartial(object.cashFlowPeriod)
      : undefined;
    return message;
  },
};

function createBaseUpdateTxnCashFlowPeriodResponse(): UpdateTxnCashFlowPeriodResponse {
  return {};
}

export const UpdateTxnCashFlowPeriodResponse: MessageFns<UpdateTxnCashFlowPeriodResponse> = {
  encode(_: UpdateTxnCashFlowPeriodResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateTxnCashFlowPeriodResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateTxnCashFlowPeriodResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): UpdateTxnCashFlowPeriodResponse {
    return {};
  },

  toJSON(_: UpdateTxnCashFlowPeriodResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateTxnCashFlowPeriodResponse>, I>>(base?: I): UpdateTxnCashFlowPeriodResponse {
    return UpdateTxnCashFlowPeriodResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateTxnCashFlowPeriodResponse>, I>>(_: I): UpdateTxnCashFlowPeriodResponse {
    const message = createBaseUpdateTxnCashFlowPeriodResponse();
    return message;
  },
};

function createBaseExcludeTxnFromCashFlowRequest(): ExcludeTxnFromCashFlowRequest {
  return { txnId: "", excludeCashFlow: false };
}

export const ExcludeTxnFromCashFlowRequest: MessageFns<ExcludeTxnFromCashFlowRequest> = {
  encode(message: ExcludeTxnFromCashFlowRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.txnId !== "") {
      writer.uint32(10).string(message.txnId);
    }
    if (message.excludeCashFlow !== false) {
      writer.uint32(16).bool(message.excludeCashFlow);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ExcludeTxnFromCashFlowRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseExcludeTxnFromCashFlowRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.txnId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.excludeCashFlow = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ExcludeTxnFromCashFlowRequest {
    return {
      txnId: isSet(object.txnId) ? globalThis.String(object.txnId) : "",
      excludeCashFlow: isSet(object.excludeCashFlow) ? globalThis.Boolean(object.excludeCashFlow) : false,
    };
  },

  toJSON(message: ExcludeTxnFromCashFlowRequest): unknown {
    const obj: any = {};
    if (message.txnId !== "") {
      obj.txnId = message.txnId;
    }
    if (message.excludeCashFlow !== false) {
      obj.excludeCashFlow = message.excludeCashFlow;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ExcludeTxnFromCashFlowRequest>, I>>(base?: I): ExcludeTxnFromCashFlowRequest {
    return ExcludeTxnFromCashFlowRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ExcludeTxnFromCashFlowRequest>, I>>(
    object: I,
  ): ExcludeTxnFromCashFlowRequest {
    const message = createBaseExcludeTxnFromCashFlowRequest();
    message.txnId = object.txnId ?? "";
    message.excludeCashFlow = object.excludeCashFlow ?? false;
    return message;
  },
};

function createBaseExcludeTxnFromCashFlowResponse(): ExcludeTxnFromCashFlowResponse {
  return {};
}

export const ExcludeTxnFromCashFlowResponse: MessageFns<ExcludeTxnFromCashFlowResponse> = {
  encode(_: ExcludeTxnFromCashFlowResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ExcludeTxnFromCashFlowResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseExcludeTxnFromCashFlowResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): ExcludeTxnFromCashFlowResponse {
    return {};
  },

  toJSON(_: ExcludeTxnFromCashFlowResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<ExcludeTxnFromCashFlowResponse>, I>>(base?: I): ExcludeTxnFromCashFlowResponse {
    return ExcludeTxnFromCashFlowResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ExcludeTxnFromCashFlowResponse>, I>>(_: I): ExcludeTxnFromCashFlowResponse {
    const message = createBaseExcludeTxnFromCashFlowResponse();
    return message;
  },
};

function createBaseDocument(): Document {
  return { name: "", objectName: "", thumbnail: "", signedUrl: "", contentType: "" };
}

export const Document: MessageFns<Document> = {
  encode(message: Document, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.objectName !== "") {
      writer.uint32(18).string(message.objectName);
    }
    if (message.thumbnail !== "") {
      writer.uint32(26).string(message.thumbnail);
    }
    if (message.signedUrl !== "") {
      writer.uint32(34).string(message.signedUrl);
    }
    if (message.contentType !== "") {
      writer.uint32(42).string(message.contentType);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Document {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDocument();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.objectName = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.thumbnail = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.signedUrl = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.contentType = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Document {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      objectName: isSet(object.objectName) ? globalThis.String(object.objectName) : "",
      thumbnail: isSet(object.thumbnail) ? globalThis.String(object.thumbnail) : "",
      signedUrl: isSet(object.signedUrl) ? globalThis.String(object.signedUrl) : "",
      contentType: isSet(object.contentType) ? globalThis.String(object.contentType) : "",
    };
  },

  toJSON(message: Document): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.objectName !== "") {
      obj.objectName = message.objectName;
    }
    if (message.thumbnail !== "") {
      obj.thumbnail = message.thumbnail;
    }
    if (message.signedUrl !== "") {
      obj.signedUrl = message.signedUrl;
    }
    if (message.contentType !== "") {
      obj.contentType = message.contentType;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Document>, I>>(base?: I): Document {
    return Document.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Document>, I>>(object: I): Document {
    const message = createBaseDocument();
    message.name = object.name ?? "";
    message.objectName = object.objectName ?? "";
    message.thumbnail = object.thumbnail ?? "";
    message.signedUrl = object.signedUrl ?? "";
    message.contentType = object.contentType ?? "";
    return message;
  },
};

function createBaseFetchTransactionDocumentsRequest(): FetchTransactionDocumentsRequest {
  return { transactionId: "" };
}

export const FetchTransactionDocumentsRequest: MessageFns<FetchTransactionDocumentsRequest> = {
  encode(message: FetchTransactionDocumentsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.transactionId !== "") {
      writer.uint32(10).string(message.transactionId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FetchTransactionDocumentsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFetchTransactionDocumentsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.transactionId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FetchTransactionDocumentsRequest {
    return { transactionId: isSet(object.transactionId) ? globalThis.String(object.transactionId) : "" };
  },

  toJSON(message: FetchTransactionDocumentsRequest): unknown {
    const obj: any = {};
    if (message.transactionId !== "") {
      obj.transactionId = message.transactionId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FetchTransactionDocumentsRequest>, I>>(
    base?: I,
  ): FetchTransactionDocumentsRequest {
    return FetchTransactionDocumentsRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FetchTransactionDocumentsRequest>, I>>(
    object: I,
  ): FetchTransactionDocumentsRequest {
    const message = createBaseFetchTransactionDocumentsRequest();
    message.transactionId = object.transactionId ?? "";
    return message;
  },
};

function createBaseFetchTransactionDocumentsResponse(): FetchTransactionDocumentsResponse {
  return { documents: [] };
}

export const FetchTransactionDocumentsResponse: MessageFns<FetchTransactionDocumentsResponse> = {
  encode(message: FetchTransactionDocumentsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.documents) {
      Document.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FetchTransactionDocumentsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFetchTransactionDocumentsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.documents.push(Document.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FetchTransactionDocumentsResponse {
    return {
      documents: globalThis.Array.isArray(object?.documents)
        ? object.documents.map((e: any) => Document.fromJSON(e))
        : [],
    };
  },

  toJSON(message: FetchTransactionDocumentsResponse): unknown {
    const obj: any = {};
    if (message.documents?.length) {
      obj.documents = message.documents.map((e) => Document.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FetchTransactionDocumentsResponse>, I>>(
    base?: I,
  ): FetchTransactionDocumentsResponse {
    return FetchTransactionDocumentsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FetchTransactionDocumentsResponse>, I>>(
    object: I,
  ): FetchTransactionDocumentsResponse {
    const message = createBaseFetchTransactionDocumentsResponse();
    message.documents = object.documents?.map((e) => Document.fromPartial(e)) || [];
    return message;
  },
};

function createBaseDeleteTransactionDocumentsRequest(): DeleteTransactionDocumentsRequest {
  return { transactionId: "", objectNames: [] };
}

export const DeleteTransactionDocumentsRequest: MessageFns<DeleteTransactionDocumentsRequest> = {
  encode(message: DeleteTransactionDocumentsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.transactionId !== "") {
      writer.uint32(10).string(message.transactionId);
    }
    for (const v of message.objectNames) {
      writer.uint32(18).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteTransactionDocumentsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteTransactionDocumentsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.transactionId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.objectNames.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DeleteTransactionDocumentsRequest {
    return {
      transactionId: isSet(object.transactionId) ? globalThis.String(object.transactionId) : "",
      objectNames: globalThis.Array.isArray(object?.objectNames)
        ? object.objectNames.map((e: any) => globalThis.String(e))
        : [],
    };
  },

  toJSON(message: DeleteTransactionDocumentsRequest): unknown {
    const obj: any = {};
    if (message.transactionId !== "") {
      obj.transactionId = message.transactionId;
    }
    if (message.objectNames?.length) {
      obj.objectNames = message.objectNames;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DeleteTransactionDocumentsRequest>, I>>(
    base?: I,
  ): DeleteTransactionDocumentsRequest {
    return DeleteTransactionDocumentsRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteTransactionDocumentsRequest>, I>>(
    object: I,
  ): DeleteTransactionDocumentsRequest {
    const message = createBaseDeleteTransactionDocumentsRequest();
    message.transactionId = object.transactionId ?? "";
    message.objectNames = object.objectNames?.map((e) => e) || [];
    return message;
  },
};

function createBaseDeleteTransactionDocumentsResponse(): DeleteTransactionDocumentsResponse {
  return {};
}

export const DeleteTransactionDocumentsResponse: MessageFns<DeleteTransactionDocumentsResponse> = {
  encode(_: DeleteTransactionDocumentsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteTransactionDocumentsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteTransactionDocumentsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): DeleteTransactionDocumentsResponse {
    return {};
  },

  toJSON(_: DeleteTransactionDocumentsResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<DeleteTransactionDocumentsResponse>, I>>(
    base?: I,
  ): DeleteTransactionDocumentsResponse {
    return DeleteTransactionDocumentsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteTransactionDocumentsResponse>, I>>(
    _: I,
  ): DeleteTransactionDocumentsResponse {
    const message = createBaseDeleteTransactionDocumentsResponse();
    return message;
  },
};

function createBaseSearchTxnsRequest(): SearchTxnsRequest {
  return { input: "", searchBy: 0, filter: undefined, paginationParams: undefined };
}

export const SearchTxnsRequest: MessageFns<SearchTxnsRequest> = {
  encode(message: SearchTxnsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.input !== "") {
      writer.uint32(10).string(message.input);
    }
    if (message.searchBy !== 0) {
      writer.uint32(16).int32(message.searchBy);
    }
    if (message.filter !== undefined) {
      DepositTxnCardFilters.encode(message.filter, writer.uint32(26).fork()).join();
    }
    if (message.paginationParams !== undefined) {
      TxnPaginationParams.encode(message.paginationParams, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SearchTxnsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSearchTxnsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.input = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.searchBy = reader.int32() as any;
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.filter = DepositTxnCardFilters.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.paginationParams = TxnPaginationParams.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SearchTxnsRequest {
    return {
      input: isSet(object.input) ? globalThis.String(object.input) : "",
      searchBy: isSet(object.searchBy) ? searchTxnsRequest_SearchByFromJSON(object.searchBy) : 0,
      filter: isSet(object.filter) ? DepositTxnCardFilters.fromJSON(object.filter) : undefined,
      paginationParams: isSet(object.paginationParams)
        ? TxnPaginationParams.fromJSON(object.paginationParams)
        : undefined,
    };
  },

  toJSON(message: SearchTxnsRequest): unknown {
    const obj: any = {};
    if (message.input !== "") {
      obj.input = message.input;
    }
    if (message.searchBy !== 0) {
      obj.searchBy = searchTxnsRequest_SearchByToJSON(message.searchBy);
    }
    if (message.filter !== undefined) {
      obj.filter = DepositTxnCardFilters.toJSON(message.filter);
    }
    if (message.paginationParams !== undefined) {
      obj.paginationParams = TxnPaginationParams.toJSON(message.paginationParams);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SearchTxnsRequest>, I>>(base?: I): SearchTxnsRequest {
    return SearchTxnsRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchTxnsRequest>, I>>(object: I): SearchTxnsRequest {
    const message = createBaseSearchTxnsRequest();
    message.input = object.input ?? "";
    message.searchBy = object.searchBy ?? 0;
    message.filter = (object.filter !== undefined && object.filter !== null)
      ? DepositTxnCardFilters.fromPartial(object.filter)
      : undefined;
    message.paginationParams = (object.paginationParams !== undefined && object.paginationParams !== null)
      ? TxnPaginationParams.fromPartial(object.paginationParams)
      : undefined;
    return message;
  },
};

function createBaseSearchTxnsResponse(): SearchTxnsResponse {
  return { cards: [] };
}

export const SearchTxnsResponse: MessageFns<SearchTxnsResponse> = {
  encode(message: SearchTxnsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.cards) {
      DepositTransactionCard.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SearchTxnsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSearchTxnsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.cards.push(DepositTransactionCard.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SearchTxnsResponse {
    return {
      cards: globalThis.Array.isArray(object?.cards)
        ? object.cards.map((e: any) => DepositTransactionCard.fromJSON(e))
        : [],
    };
  },

  toJSON(message: SearchTxnsResponse): unknown {
    const obj: any = {};
    if (message.cards?.length) {
      obj.cards = message.cards.map((e) => DepositTransactionCard.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SearchTxnsResponse>, I>>(base?: I): SearchTxnsResponse {
    return SearchTxnsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchTxnsResponse>, I>>(object: I): SearchTxnsResponse {
    const message = createBaseSearchTxnsResponse();
    message.cards = object.cards?.map((e) => DepositTransactionCard.fromPartial(e)) || [];
    return message;
  },
};

function createBaseFindSimilarTxnsForTaggingRequest(): FindSimilarTxnsForTaggingRequest {
  return { txnId: "", identifierKeywords: [] };
}

export const FindSimilarTxnsForTaggingRequest: MessageFns<FindSimilarTxnsForTaggingRequest> = {
  encode(message: FindSimilarTxnsForTaggingRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.txnId !== "") {
      writer.uint32(10).string(message.txnId);
    }
    for (const v of message.identifierKeywords) {
      writer.uint32(18).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FindSimilarTxnsForTaggingRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFindSimilarTxnsForTaggingRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.txnId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.identifierKeywords.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FindSimilarTxnsForTaggingRequest {
    return {
      txnId: isSet(object.txnId) ? globalThis.String(object.txnId) : "",
      identifierKeywords: globalThis.Array.isArray(object?.identifierKeywords)
        ? object.identifierKeywords.map((e: any) => globalThis.String(e))
        : [],
    };
  },

  toJSON(message: FindSimilarTxnsForTaggingRequest): unknown {
    const obj: any = {};
    if (message.txnId !== "") {
      obj.txnId = message.txnId;
    }
    if (message.identifierKeywords?.length) {
      obj.identifierKeywords = message.identifierKeywords;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FindSimilarTxnsForTaggingRequest>, I>>(
    base?: I,
  ): FindSimilarTxnsForTaggingRequest {
    return FindSimilarTxnsForTaggingRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FindSimilarTxnsForTaggingRequest>, I>>(
    object: I,
  ): FindSimilarTxnsForTaggingRequest {
    const message = createBaseFindSimilarTxnsForTaggingRequest();
    message.txnId = object.txnId ?? "";
    message.identifierKeywords = object.identifierKeywords?.map((e) => e) || [];
    return message;
  },
};

function createBaseFindSimilarTxnsForTaggingResponse(): FindSimilarTxnsForTaggingResponse {
  return { cards: [] };
}

export const FindSimilarTxnsForTaggingResponse: MessageFns<FindSimilarTxnsForTaggingResponse> = {
  encode(message: FindSimilarTxnsForTaggingResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.cards) {
      DepositTransactionCard.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FindSimilarTxnsForTaggingResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFindSimilarTxnsForTaggingResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.cards.push(DepositTransactionCard.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FindSimilarTxnsForTaggingResponse {
    return {
      cards: globalThis.Array.isArray(object?.cards)
        ? object.cards.map((e: any) => DepositTransactionCard.fromJSON(e))
        : [],
    };
  },

  toJSON(message: FindSimilarTxnsForTaggingResponse): unknown {
    const obj: any = {};
    if (message.cards?.length) {
      obj.cards = message.cards.map((e) => DepositTransactionCard.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FindSimilarTxnsForTaggingResponse>, I>>(
    base?: I,
  ): FindSimilarTxnsForTaggingResponse {
    return FindSimilarTxnsForTaggingResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FindSimilarTxnsForTaggingResponse>, I>>(
    object: I,
  ): FindSimilarTxnsForTaggingResponse {
    const message = createBaseFindSimilarTxnsForTaggingResponse();
    message.cards = object.cards?.map((e) => DepositTransactionCard.fromPartial(e)) || [];
    return message;
  },
};

function createBaseSearchSuggestionsRequest(): SearchSuggestionsRequest {
  return { input: "" };
}

export const SearchSuggestionsRequest: MessageFns<SearchSuggestionsRequest> = {
  encode(message: SearchSuggestionsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.input !== "") {
      writer.uint32(10).string(message.input);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SearchSuggestionsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSearchSuggestionsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.input = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SearchSuggestionsRequest {
    return { input: isSet(object.input) ? globalThis.String(object.input) : "" };
  },

  toJSON(message: SearchSuggestionsRequest): unknown {
    const obj: any = {};
    if (message.input !== "") {
      obj.input = message.input;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SearchSuggestionsRequest>, I>>(base?: I): SearchSuggestionsRequest {
    return SearchSuggestionsRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchSuggestionsRequest>, I>>(object: I): SearchSuggestionsRequest {
    const message = createBaseSearchSuggestionsRequest();
    message.input = object.input ?? "";
    return message;
  },
};

function createBaseSearchSuggestionsResponse(): SearchSuggestionsResponse {
  return { suggestions: [] };
}

export const SearchSuggestionsResponse: MessageFns<SearchSuggestionsResponse> = {
  encode(message: SearchSuggestionsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.suggestions) {
      SearchSuggestionsResponse_Suggestion.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SearchSuggestionsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSearchSuggestionsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.suggestions.push(SearchSuggestionsResponse_Suggestion.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SearchSuggestionsResponse {
    return {
      suggestions: globalThis.Array.isArray(object?.suggestions)
        ? object.suggestions.map((e: any) => SearchSuggestionsResponse_Suggestion.fromJSON(e))
        : [],
    };
  },

  toJSON(message: SearchSuggestionsResponse): unknown {
    const obj: any = {};
    if (message.suggestions?.length) {
      obj.suggestions = message.suggestions.map((e) => SearchSuggestionsResponse_Suggestion.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SearchSuggestionsResponse>, I>>(base?: I): SearchSuggestionsResponse {
    return SearchSuggestionsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchSuggestionsResponse>, I>>(object: I): SearchSuggestionsResponse {
    const message = createBaseSearchSuggestionsResponse();
    message.suggestions = object.suggestions?.map((e) => SearchSuggestionsResponse_Suggestion.fromPartial(e)) || [];
    return message;
  },
};

function createBaseSearchSuggestionsResponse_Suggestion(): SearchSuggestionsResponse_Suggestion {
  return { keyword: "", merchantIds: [], tags: [] };
}

export const SearchSuggestionsResponse_Suggestion: MessageFns<SearchSuggestionsResponse_Suggestion> = {
  encode(message: SearchSuggestionsResponse_Suggestion, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.keyword !== "") {
      writer.uint32(10).string(message.keyword);
    }
    for (const v of message.merchantIds) {
      writer.uint32(18).string(v!);
    }
    for (const v of message.tags) {
      SearchSuggestionsResponse_Tag.encode(v!, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SearchSuggestionsResponse_Suggestion {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSearchSuggestionsResponse_Suggestion();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.keyword = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.merchantIds.push(reader.string());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.tags.push(SearchSuggestionsResponse_Tag.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SearchSuggestionsResponse_Suggestion {
    return {
      keyword: isSet(object.keyword) ? globalThis.String(object.keyword) : "",
      merchantIds: globalThis.Array.isArray(object?.merchantIds)
        ? object.merchantIds.map((e: any) => globalThis.String(e))
        : [],
      tags: globalThis.Array.isArray(object?.tags)
        ? object.tags.map((e: any) => SearchSuggestionsResponse_Tag.fromJSON(e))
        : [],
    };
  },

  toJSON(message: SearchSuggestionsResponse_Suggestion): unknown {
    const obj: any = {};
    if (message.keyword !== "") {
      obj.keyword = message.keyword;
    }
    if (message.merchantIds?.length) {
      obj.merchantIds = message.merchantIds;
    }
    if (message.tags?.length) {
      obj.tags = message.tags.map((e) => SearchSuggestionsResponse_Tag.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SearchSuggestionsResponse_Suggestion>, I>>(
    base?: I,
  ): SearchSuggestionsResponse_Suggestion {
    return SearchSuggestionsResponse_Suggestion.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchSuggestionsResponse_Suggestion>, I>>(
    object: I,
  ): SearchSuggestionsResponse_Suggestion {
    const message = createBaseSearchSuggestionsResponse_Suggestion();
    message.keyword = object.keyword ?? "";
    message.merchantIds = object.merchantIds?.map((e) => e) || [];
    message.tags = object.tags?.map((e) => SearchSuggestionsResponse_Tag.fromPartial(e)) || [];
    return message;
  },
};

function createBaseSearchSuggestionsResponse_Tag(): SearchSuggestionsResponse_Tag {
  return { categoryCollection: "", categoryId: "", subcategoryId: "" };
}

export const SearchSuggestionsResponse_Tag: MessageFns<SearchSuggestionsResponse_Tag> = {
  encode(message: SearchSuggestionsResponse_Tag, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.categoryCollection !== "") {
      writer.uint32(10).string(message.categoryCollection);
    }
    if (message.categoryId !== "") {
      writer.uint32(18).string(message.categoryId);
    }
    if (message.subcategoryId !== "") {
      writer.uint32(26).string(message.subcategoryId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SearchSuggestionsResponse_Tag {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSearchSuggestionsResponse_Tag();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.categoryCollection = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.categoryId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.subcategoryId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SearchSuggestionsResponse_Tag {
    return {
      categoryCollection: isSet(object.categoryCollection) ? globalThis.String(object.categoryCollection) : "",
      categoryId: isSet(object.categoryId) ? globalThis.String(object.categoryId) : "",
      subcategoryId: isSet(object.subcategoryId) ? globalThis.String(object.subcategoryId) : "",
    };
  },

  toJSON(message: SearchSuggestionsResponse_Tag): unknown {
    const obj: any = {};
    if (message.categoryCollection !== "") {
      obj.categoryCollection = message.categoryCollection;
    }
    if (message.categoryId !== "") {
      obj.categoryId = message.categoryId;
    }
    if (message.subcategoryId !== "") {
      obj.subcategoryId = message.subcategoryId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SearchSuggestionsResponse_Tag>, I>>(base?: I): SearchSuggestionsResponse_Tag {
    return SearchSuggestionsResponse_Tag.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchSuggestionsResponse_Tag>, I>>(
    object: I,
  ): SearchSuggestionsResponse_Tag {
    const message = createBaseSearchSuggestionsResponse_Tag();
    message.categoryCollection = object.categoryCollection ?? "";
    message.categoryId = object.categoryId ?? "";
    message.subcategoryId = object.subcategoryId ?? "";
    return message;
  },
};

export type DepositTransactionService = typeof DepositTransactionService;
export const DepositTransactionService = {
  fetchDepositTxns: {
    path: "/backend_services.visualization.DepositTransaction/FetchDepositTxns",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: FetchDepositTxnsRequest) => Buffer.from(FetchDepositTxnsRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => FetchDepositTxnsRequest.decode(value),
    responseSerialize: (value: FetchDepositTxnsResponse) =>
      Buffer.from(FetchDepositTxnsResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => FetchDepositTxnsResponse.decode(value),
  },
  assignMerchantToDepositTxns: {
    path: "/backend_services.visualization.DepositTransaction/AssignMerchantToDepositTxns",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: AssignMerchantToDepositTxnsRequest) =>
      Buffer.from(AssignMerchantToDepositTxnsRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => AssignMerchantToDepositTxnsRequest.decode(value),
    responseSerialize: (value: AssignMerchantToDepositTxnsResponse) =>
      Buffer.from(AssignMerchantToDepositTxnsResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => AssignMerchantToDepositTxnsResponse.decode(value),
  },
  assignCategoryToDepositTxns: {
    path: "/backend_services.visualization.DepositTransaction/AssignCategoryToDepositTxns",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: AssignCategoryToDepositTxnsRequest) =>
      Buffer.from(AssignCategoryToDepositTxnsRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => AssignCategoryToDepositTxnsRequest.decode(value),
    responseSerialize: (value: AssignCategoryToDepositTxnsResponse) =>
      Buffer.from(AssignCategoryToDepositTxnsResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => AssignCategoryToDepositTxnsResponse.decode(value),
  },
  markDepositTxnFavorite: {
    path: "/backend_services.visualization.DepositTransaction/MarkDepositTxnFavorite",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: MarkDepositTxnFavoriteRequest) =>
      Buffer.from(MarkDepositTxnFavoriteRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => MarkDepositTxnFavoriteRequest.decode(value),
    responseSerialize: (value: MarkDepositTxnFavoriteResponse) =>
      Buffer.from(MarkDepositTxnFavoriteResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => MarkDepositTxnFavoriteResponse.decode(value),
  },
  updateTransactionNotes: {
    path: "/backend_services.visualization.DepositTransaction/UpdateTransactionNotes",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: UpdateTransactionNotesRequest) =>
      Buffer.from(UpdateTransactionNotesRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => UpdateTransactionNotesRequest.decode(value),
    responseSerialize: (value: UpdateTransactionNotesResponse) =>
      Buffer.from(UpdateTransactionNotesResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => UpdateTransactionNotesResponse.decode(value),
  },
  updateTxnCashFlowPeriod: {
    path: "/backend_services.visualization.DepositTransaction/UpdateTxnCashFlowPeriod",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: UpdateTxnCashFlowPeriodRequest) =>
      Buffer.from(UpdateTxnCashFlowPeriodRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => UpdateTxnCashFlowPeriodRequest.decode(value),
    responseSerialize: (value: UpdateTxnCashFlowPeriodResponse) =>
      Buffer.from(UpdateTxnCashFlowPeriodResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => UpdateTxnCashFlowPeriodResponse.decode(value),
  },
  excludeTxnFromCashFlow: {
    path: "/backend_services.visualization.DepositTransaction/ExcludeTxnFromCashFlow",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: ExcludeTxnFromCashFlowRequest) =>
      Buffer.from(ExcludeTxnFromCashFlowRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => ExcludeTxnFromCashFlowRequest.decode(value),
    responseSerialize: (value: ExcludeTxnFromCashFlowResponse) =>
      Buffer.from(ExcludeTxnFromCashFlowResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => ExcludeTxnFromCashFlowResponse.decode(value),
  },
  fetchTransactionDocuments: {
    path: "/backend_services.visualization.DepositTransaction/FetchTransactionDocuments",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: FetchTransactionDocumentsRequest) =>
      Buffer.from(FetchTransactionDocumentsRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => FetchTransactionDocumentsRequest.decode(value),
    responseSerialize: (value: FetchTransactionDocumentsResponse) =>
      Buffer.from(FetchTransactionDocumentsResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => FetchTransactionDocumentsResponse.decode(value),
  },
  deleteTransactionDocuments: {
    path: "/backend_services.visualization.DepositTransaction/DeleteTransactionDocuments",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: DeleteTransactionDocumentsRequest) =>
      Buffer.from(DeleteTransactionDocumentsRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => DeleteTransactionDocumentsRequest.decode(value),
    responseSerialize: (value: DeleteTransactionDocumentsResponse) =>
      Buffer.from(DeleteTransactionDocumentsResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => DeleteTransactionDocumentsResponse.decode(value),
  },
  searchTxns: {
    path: "/backend_services.visualization.DepositTransaction/SearchTxns",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: SearchTxnsRequest) => Buffer.from(SearchTxnsRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => SearchTxnsRequest.decode(value),
    responseSerialize: (value: SearchTxnsResponse) => Buffer.from(SearchTxnsResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => SearchTxnsResponse.decode(value),
  },
  findSimilarTxnsForTagging: {
    path: "/backend_services.visualization.DepositTransaction/FindSimilarTxnsForTagging",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: FindSimilarTxnsForTaggingRequest) =>
      Buffer.from(FindSimilarTxnsForTaggingRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => FindSimilarTxnsForTaggingRequest.decode(value),
    responseSerialize: (value: FindSimilarTxnsForTaggingResponse) =>
      Buffer.from(FindSimilarTxnsForTaggingResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => FindSimilarTxnsForTaggingResponse.decode(value),
  },
  searchSuggestions: {
    path: "/backend_services.visualization.DepositTransaction/SearchSuggestions",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: SearchSuggestionsRequest) => Buffer.from(SearchSuggestionsRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => SearchSuggestionsRequest.decode(value),
    responseSerialize: (value: SearchSuggestionsResponse) =>
      Buffer.from(SearchSuggestionsResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => SearchSuggestionsResponse.decode(value),
  },
} as const;

export interface DepositTransactionServer extends UntypedServiceImplementation {
  fetchDepositTxns: handleUnaryCall<FetchDepositTxnsRequest, FetchDepositTxnsResponse>;
  assignMerchantToDepositTxns: handleUnaryCall<AssignMerchantToDepositTxnsRequest, AssignMerchantToDepositTxnsResponse>;
  assignCategoryToDepositTxns: handleUnaryCall<AssignCategoryToDepositTxnsRequest, AssignCategoryToDepositTxnsResponse>;
  markDepositTxnFavorite: handleUnaryCall<MarkDepositTxnFavoriteRequest, MarkDepositTxnFavoriteResponse>;
  updateTransactionNotes: handleUnaryCall<UpdateTransactionNotesRequest, UpdateTransactionNotesResponse>;
  updateTxnCashFlowPeriod: handleUnaryCall<UpdateTxnCashFlowPeriodRequest, UpdateTxnCashFlowPeriodResponse>;
  excludeTxnFromCashFlow: handleUnaryCall<ExcludeTxnFromCashFlowRequest, ExcludeTxnFromCashFlowResponse>;
  fetchTransactionDocuments: handleUnaryCall<FetchTransactionDocumentsRequest, FetchTransactionDocumentsResponse>;
  deleteTransactionDocuments: handleUnaryCall<DeleteTransactionDocumentsRequest, DeleteTransactionDocumentsResponse>;
  searchTxns: handleUnaryCall<SearchTxnsRequest, SearchTxnsResponse>;
  findSimilarTxnsForTagging: handleUnaryCall<FindSimilarTxnsForTaggingRequest, FindSimilarTxnsForTaggingResponse>;
  searchSuggestions: handleUnaryCall<SearchSuggestionsRequest, SearchSuggestionsResponse>;
}

export interface DepositTransactionClient extends Client {
  fetchDepositTxns(
    request: FetchDepositTxnsRequest,
    callback: (error: ServiceError | null, response: FetchDepositTxnsResponse) => void,
  ): ClientUnaryCall;
  fetchDepositTxns(
    request: FetchDepositTxnsRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: FetchDepositTxnsResponse) => void,
  ): ClientUnaryCall;
  fetchDepositTxns(
    request: FetchDepositTxnsRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: FetchDepositTxnsResponse) => void,
  ): ClientUnaryCall;
  assignMerchantToDepositTxns(
    request: AssignMerchantToDepositTxnsRequest,
    callback: (error: ServiceError | null, response: AssignMerchantToDepositTxnsResponse) => void,
  ): ClientUnaryCall;
  assignMerchantToDepositTxns(
    request: AssignMerchantToDepositTxnsRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: AssignMerchantToDepositTxnsResponse) => void,
  ): ClientUnaryCall;
  assignMerchantToDepositTxns(
    request: AssignMerchantToDepositTxnsRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: AssignMerchantToDepositTxnsResponse) => void,
  ): ClientUnaryCall;
  assignCategoryToDepositTxns(
    request: AssignCategoryToDepositTxnsRequest,
    callback: (error: ServiceError | null, response: AssignCategoryToDepositTxnsResponse) => void,
  ): ClientUnaryCall;
  assignCategoryToDepositTxns(
    request: AssignCategoryToDepositTxnsRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: AssignCategoryToDepositTxnsResponse) => void,
  ): ClientUnaryCall;
  assignCategoryToDepositTxns(
    request: AssignCategoryToDepositTxnsRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: AssignCategoryToDepositTxnsResponse) => void,
  ): ClientUnaryCall;
  markDepositTxnFavorite(
    request: MarkDepositTxnFavoriteRequest,
    callback: (error: ServiceError | null, response: MarkDepositTxnFavoriteResponse) => void,
  ): ClientUnaryCall;
  markDepositTxnFavorite(
    request: MarkDepositTxnFavoriteRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: MarkDepositTxnFavoriteResponse) => void,
  ): ClientUnaryCall;
  markDepositTxnFavorite(
    request: MarkDepositTxnFavoriteRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: MarkDepositTxnFavoriteResponse) => void,
  ): ClientUnaryCall;
  updateTransactionNotes(
    request: UpdateTransactionNotesRequest,
    callback: (error: ServiceError | null, response: UpdateTransactionNotesResponse) => void,
  ): ClientUnaryCall;
  updateTransactionNotes(
    request: UpdateTransactionNotesRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: UpdateTransactionNotesResponse) => void,
  ): ClientUnaryCall;
  updateTransactionNotes(
    request: UpdateTransactionNotesRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: UpdateTransactionNotesResponse) => void,
  ): ClientUnaryCall;
  updateTxnCashFlowPeriod(
    request: UpdateTxnCashFlowPeriodRequest,
    callback: (error: ServiceError | null, response: UpdateTxnCashFlowPeriodResponse) => void,
  ): ClientUnaryCall;
  updateTxnCashFlowPeriod(
    request: UpdateTxnCashFlowPeriodRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: UpdateTxnCashFlowPeriodResponse) => void,
  ): ClientUnaryCall;
  updateTxnCashFlowPeriod(
    request: UpdateTxnCashFlowPeriodRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: UpdateTxnCashFlowPeriodResponse) => void,
  ): ClientUnaryCall;
  excludeTxnFromCashFlow(
    request: ExcludeTxnFromCashFlowRequest,
    callback: (error: ServiceError | null, response: ExcludeTxnFromCashFlowResponse) => void,
  ): ClientUnaryCall;
  excludeTxnFromCashFlow(
    request: ExcludeTxnFromCashFlowRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: ExcludeTxnFromCashFlowResponse) => void,
  ): ClientUnaryCall;
  excludeTxnFromCashFlow(
    request: ExcludeTxnFromCashFlowRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: ExcludeTxnFromCashFlowResponse) => void,
  ): ClientUnaryCall;
  fetchTransactionDocuments(
    request: FetchTransactionDocumentsRequest,
    callback: (error: ServiceError | null, response: FetchTransactionDocumentsResponse) => void,
  ): ClientUnaryCall;
  fetchTransactionDocuments(
    request: FetchTransactionDocumentsRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: FetchTransactionDocumentsResponse) => void,
  ): ClientUnaryCall;
  fetchTransactionDocuments(
    request: FetchTransactionDocumentsRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: FetchTransactionDocumentsResponse) => void,
  ): ClientUnaryCall;
  deleteTransactionDocuments(
    request: DeleteTransactionDocumentsRequest,
    callback: (error: ServiceError | null, response: DeleteTransactionDocumentsResponse) => void,
  ): ClientUnaryCall;
  deleteTransactionDocuments(
    request: DeleteTransactionDocumentsRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: DeleteTransactionDocumentsResponse) => void,
  ): ClientUnaryCall;
  deleteTransactionDocuments(
    request: DeleteTransactionDocumentsRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: DeleteTransactionDocumentsResponse) => void,
  ): ClientUnaryCall;
  searchTxns(
    request: SearchTxnsRequest,
    callback: (error: ServiceError | null, response: SearchTxnsResponse) => void,
  ): ClientUnaryCall;
  searchTxns(
    request: SearchTxnsRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: SearchTxnsResponse) => void,
  ): ClientUnaryCall;
  searchTxns(
    request: SearchTxnsRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: SearchTxnsResponse) => void,
  ): ClientUnaryCall;
  findSimilarTxnsForTagging(
    request: FindSimilarTxnsForTaggingRequest,
    callback: (error: ServiceError | null, response: FindSimilarTxnsForTaggingResponse) => void,
  ): ClientUnaryCall;
  findSimilarTxnsForTagging(
    request: FindSimilarTxnsForTaggingRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: FindSimilarTxnsForTaggingResponse) => void,
  ): ClientUnaryCall;
  findSimilarTxnsForTagging(
    request: FindSimilarTxnsForTaggingRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: FindSimilarTxnsForTaggingResponse) => void,
  ): ClientUnaryCall;
  searchSuggestions(
    request: SearchSuggestionsRequest,
    callback: (error: ServiceError | null, response: SearchSuggestionsResponse) => void,
  ): ClientUnaryCall;
  searchSuggestions(
    request: SearchSuggestionsRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: SearchSuggestionsResponse) => void,
  ): ClientUnaryCall;
  searchSuggestions(
    request: SearchSuggestionsRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: SearchSuggestionsResponse) => void,
  ): ClientUnaryCall;
}

export const DepositTransactionClient = makeGenericClientConstructor(
  DepositTransactionService,
  "backend_services.visualization.DepositTransaction",
) as unknown as {
  new (address: string, credentials: ChannelCredentials, options?: Partial<ClientOptions>): DepositTransactionClient;
  service: typeof DepositTransactionService;
  serviceName: string;
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
