import * as React from "react";
import type { SVGProps } from "react";
const SvgDebt = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="currentColor"
    viewBox="0 0 29 27"
    {...props}
  >
    <path
      fillRule="evenodd"
      d="M18.567.95H1.626A1.2 1.2 0 0 0 .415 2.141v.644a1.2 1.2 0 0 0 1.21 1.19h3.343l2.12-.026c1.792.669 3.156 2.065 3.156 3.24H1.626A1.2 1.2 0 0 0 .415 8.382v.538a1.2 1.2 0 0 0 1.21 1.191h7.95c-.97 1.399-3.509 2.281-6.567 2.281v-.003H1.626A1.2 1.2 0 0 0 .415 13.58v2.09c0 .61.234 1.2.656 1.648 2.957 3.136 5.865 6.522 8.057 9.157.23.276.573.436.937.436h3.746c1.021 0 1.585-1.165.94-1.942-2.722-3.276-5.484-6.435-7.895-8.875 3.374-.234 6.906-2.937 8.173-5.725a.6.6 0 0 0 .055-.258h3.483a1.2 1.2 0 0 0 1.21-1.19v-.54a1.2 1.2 0 0 0-1.21-1.19h-3.6a8 8 0 0 0-.317-1.298c-.184-.546-.804-1.603-1.412-2.022h5.33a1.2 1.2 0 0 0 1.211-1.19V2.14a1.2 1.2 0 0 0-1.21-1.19z"
      clipRule="evenodd"
    />
    <g
      stroke="#C4A4DC"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={3.273}
      clipPath="url(#debt_svg__a)"
    >
      <path d="M15.57 17.97h9.96M21.252 13.692l4.277 4.278-4.277 4.277" />
    </g>
    <defs>
      <clipPath id="debt_svg__a">
        <path d="M13.919 11.48h14.665v14.665H13.919z" />
      </clipPath>
    </defs>
  </svg>
);
export default SvgDebt;
