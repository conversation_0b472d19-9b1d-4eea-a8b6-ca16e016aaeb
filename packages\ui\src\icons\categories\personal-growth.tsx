import * as React from "react";
import type { SVGProps } from "react";
const SvgPersonalGrowth = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="currentColor"
    viewBox="0 0 31 31"
    {...props}
  >
    <path d="M30.132 15.464v12.291a.87.87 0 0 1-.86.878H1.73a.87.87 0 0 1-.86-.878v-5.268c0-.482.386-.877.86-.877h8.322v-2.634c0-.482.387-.877.86-.877h8.313v-2.634c0-.482.388-.877.86-.877h9.185c.473 0 .86.395.86.877zM24.446 2.37c.306-.944 1.641-.944 1.948 0l.603 1.855c.137.422.53.707.974.707h1.95c.992 0 1.405 1.27.602 1.853l-1.578 1.147c-.359.26-.509.723-.372 1.145l.603 1.855c.307.943-.773 1.728-1.576 1.145l-1.578-1.147a1.02 1.02 0 0 0-1.204 0l-1.578 1.147c-.803.583-1.883-.202-1.576-1.145l.603-1.855a1.02 1.02 0 0 0-.372-1.145l-1.578-1.147c-.803-.583-.39-1.853.602-1.853h1.95c.444 0 .837-.285.974-.707z" />
  </svg>
);
export default SvgPersonalGrowth;
