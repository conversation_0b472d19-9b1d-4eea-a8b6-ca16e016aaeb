syntax = "proto3";
package backend_services.data_access;

import "database/core.proto";

service Consent {
    rpc InsertConsentRecord(InsertConsentRecordRequest) returns (InsertConsentRecordResponse) {};
	rpc UpdateConsentRecord(UpdateConsentRecordRequest) returns (UpdateConsentRecordResponse) {};
	rpc DeleteConsent(DeleteConsentRequest) returns (DeleteConsentResponse) {};
	rpc GetConsentHandleCustomerId(GetConsentHandleCustomerIdRequest) returns (GetConsentHandleCustomerIdResponse) {};	
    rpc GetLatestConsentOfPhoneNumber(GetLatestConsentOfPhoneNumberRequest) returns (GetLatestConsentOfPhoneNumberResponse) {};
    rpc GetConsentById(GetConsentByIdRequest) returns (GetConsentByIdResponse) {};
	rpc GetConsentParentPhoneNumber(GetConsentParentPhoneNumberRequest) returns (GetConsentParentPhoneNumberResponse) {};
	rpc MarkConsentDataProcessedOnce(MarkConsentDataProcessedOnceRequest) returns (MarkConsentDataProcessedOnceResponse) {};
	rpc GetConsentBySessionId(GetConsentBySessionIdRequest) returns (GetConsentBySessionIdResponse) {}
	rpc UpdateConsentSessionId(UpdateConsentSessionIdRequest) returns (UpdateConsentSessionIdResponse) {}
	rpc UpdateDataProcessedCheckpointTimestamp(UpdateDataProcessedCheckpointTimestampArgs) returns (UpdateDataProcessedCheckpointTimestampResponse) {};
	rpc GetConsentsOfPhoneNumber(GetConsentsOfPhoneNumberRequest) returns (GetConsentsOfPhoneNumberResponse) {};
	rpc UpdateConsentAccounts(UpdateConsentAccountsRequest) returns (UpdateConsentAccountsResponse) {};
}

message InsertConsentRecordRequest {
	database.Consent consent = 1;
}
message InsertConsentRecordResponse {}


message GetConsentHandleCustomerIdRequest{
	string consent_handle = 1;
}
message GetConsentHandleCustomerIdResponse{
	string customer_id = 1;
}


message UpdateConsentRecordRequest{
	string update_doc_consent_request_id = 1;
	Updates updates = 2;
	
	message Updates{
		string consent_handle = 1;
		string consent_id = 2;
		database.FiType fi_type = 3;
		int64 consent_start = 4;
		int64 consent_expiry = 5;
		int64 data_range_from = 6;
		int64 data_range_to = 7;
		string session_id = 8;
		repeated database.ConsentedAccount accounts = 9;
	}
}
message UpdateConsentRecordResponse{}


message DeleteConsentRequest{
	string consent_request_id = 1;
}
message DeleteConsentResponse{
	int64 deletedDocsCount = 1;
}

message GetLatestConsentOfPhoneNumberRequest{
	string phone_number = 1;
}
message GetLatestConsentOfPhoneNumberResponse{
	database.Consent consent = 1;
}

message GetConsentByIdRequest{
	string consentId = 1;
}
message GetConsentByIdResponse{
	database.Consent consent = 1;
}

message GetConsentParentPhoneNumberRequest {
	string consent_id = 1;
}

message GetConsentParentPhoneNumberResponse {
	string parent_phone_number = 1;
}

message MarkConsentDataProcessedOnceRequest{
	string consent_id = 1;
}

message MarkConsentDataProcessedOnceResponse{}

message GetConsentBySessionIdRequest {
	string session_id = 1;
}

message GetConsentBySessionIdResponse {
	database.Consent consent = 1;
}

message UpdateConsentSessionIdRequest {
	string consent_id = 1;
	string session_id = 2;
}

message UpdateConsentSessionIdResponse {
}

message UpdateDataProcessedCheckpointTimestampArgs {
	string consent_id = 1;
	int64 data_processed_checkpoint_timestamp = 2;
}

message UpdateDataProcessedCheckpointTimestampResponse {

}

message GetConsentsOfPhoneNumberRequest{
	string phone_number = 1;
}
message GetConsentsOfPhoneNumberResponse{
	repeated database.Consent consents = 1;
}

message UpdateConsentAccountsRequest {
	string consent_id = 1;
	repeated database.ConsentedAccount accounts = 9;
}
message UpdateConsentAccountsResponse {}
