syntax = "proto3";
package database;

import "database/custom_type.proto";

// flag:is-collection=true;
// Transaction Categories
message TransactionCategory {
    ObjectId _id = 1;
    string name = 2; // Category name
    string auth_user_id = 3; // When the category is user-created
    string sf_symbol = 4; // SF Symbol name
    int32 sort_key = 5; // Controls display-order of category
    int64 created_at = 6;
    int64 updated_at = 7;
}

// flag:is-collection=true;
// Transaction Subcategories
message TransactionSubcategory {
    ObjectId _id = 1;
    string name = 2; // Subcategory name
    string auth_user_id = 3; // When the subcategory is user-created
    int32 sort_key = 4; // Controls display-order of subcategory
    message CategoryMapping {
        database.ObjectId category_id = 1;
        repeated string txn_types = 2; 
    }
    repeated CategoryMapping category_mappings = 5;
    int64 created_at = 6;
    int64 updated_at = 7;
}


// flag:is-collection=true;
message KeywordsToTxnTagMapping{
    ObjectId _id = 1;
    string debit_subcategory = 2;
    string debit_category = 3;
    string credit_subcategory = 4;
    string credit_category = 5;
    repeated string ngram_keywords = 6;
    repeated string standard_keywords = 7;
    bool is_generic = 8; // true if a generic record for category
}

// flag:is-collection=true;
message VpaToMerchantMap {
    ObjectId _id = 1;
    string vpa = 2;
    string name_at_bank = 3;
    int64 created_at = 4;
    int64 updated_at = 5;
}

// flag:is-collection=true;
message Merchant {
    ObjectId _id = 1;
    string name = 2;
    repeated string ngram_keywords = 3;
    repeated string standard_keywords = 4;
    repeated ObjectId tag_ids = 5;
    string auth_user_id = 6; // When the merchant is user-created
    int64 created_at = 7;
    int64 updated_at = 8;
}

message Highlight {
    repeated HighlightText texts = 1;
}
  
message HighlightText {
    string value = 1;
    string type = 2;
}