syntax = "proto3";
package backend_services.visualization;

// Service for Tags
service DepositTransactionTags{
    rpc CreateCategory(CreateCategoryRequest) returns (CreateCategoryResponse) {};
    rpc RenameCategory(RenameCategoryRequest) returns (RenameCategoryResponse) {};
    rpc DeleteCategory(DeleteCategoryRequest) returns (DeleteCategoryResponse) {};
    
    rpc CreateSubcategory(CreateSubcategoryRequest) returns (CreateSubcategoryResponse) {};
    rpc RenameSubcategory(RenameSubcategoryRequest) returns (RenameSubcategoryResponse) {};
    rpc DeleteSubcategory(DeleteSubcategoryRequest) returns (DeleteSubcategoryResponse) {};

    rpc RemoveSubcategoryFromCategory(RemoveSubcategoryFromCategoryRequest) returns (RemoveSubcategoryFromCategoryResponse) {};
    
    rpc CreateMerchant(CreateMerchantRequest) returns (CreateMerchantResponse) {};
    rpc GetMerchants(GetMerchantsRequest) returns (GetMerchantsResponse) {};
    rpc DeleteMerchant(DeleteMerchantRequest) returns (DeleteMerchantResponse) {};

    rpc SearchCategory(SearchCategoryRequest) returns (SearchCategoryResponse) {};
    rpc SearchMerchant(SearchMerchantRequest) returns (SearchMerchantResponse) {};
}


message CreateCategoryRequest { 
    string category_name = 2;
}
message CreateCategoryResponse {
    string category_id = 1;
}


message RenameCategoryRequest { 
    string category_id = 1;
    string category_name = 2;
}
message RenameCategoryResponse {}


message DeleteCategoryRequest {
    string category_id = 1;
}
message DeleteCategoryResponse{}


message CreateSubcategoryRequest {
    string category_id = 1;
    string subcategory_name = 2;
}
message CreateSubcategoryResponse {
    string subcategory_id = 1;
}


message RenameSubcategoryRequest { 
    string subcategory_id = 1;
    string subcategory_name = 2;
}
message RenameSubcategoryResponse {}

message DeleteSubcategoryRequest {
    string subcategory_id = 1;
}
message DeleteSubcategoryResponse {}


message RemoveSubcategoryFromCategoryRequest{
    string category_id = 1;
    string subcategory_id = 2;
}
message RemoveSubcategoryFromCategoryResponse{}


message CreateMerchantRequest {
    string merchant_name = 1;
}
message CreateMerchantResponse {
    string merchant_id = 1;
}


message GetMerchantsRequest {}
message GetMerchantsResponse {
    message Merchant {
        string id = 1;
        string name = 2;
    }
    repeated Merchant merchants = 1; 
}

message DeleteMerchantRequest {
    string merchant_id = 1;
}
message DeleteMerchantResponse{}


message SearchCategoryRequest {
    string keyword = 1;
    string txn_type = 2;
}
message SearchCategoryResponse {
    message Tag {
        string category_collection = 1;
        string category_id = 2;
        string subcategory_id = 3;
    }

    repeated Tag tags = 1;
}

message SearchMerchantRequest {
    string keyword = 1;
}
message SearchMerchantResponse {
    message Merchant {
        string id = 1;
        string name = 2;
    }
    repeated Merchant merchants = 1;
}
