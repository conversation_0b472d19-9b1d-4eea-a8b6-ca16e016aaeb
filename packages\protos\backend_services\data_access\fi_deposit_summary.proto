syntax = "proto3";
package backend_services.data_access;
import "database/fi_deposit.proto";

service FiDepositSummary {
    rpc UpdateFiDepositAccountsSummaryAndProfile(UpdateFiDepositAccountsSummaryAndProfileRequest) returns (UpdateFiDepositAccountsSummaryAndProfileResponse) {};
	rpc GetRecentlyFetchedDepositAccounts(GetRecentlyFetchedDepositAccountsRequest) returns (GetRecentlyFetchedDepositAccountsResponse) {};
	rpc GetDepositAccountsSummary(GetDepositAccountsSummaryRequest) returns (GetDepositAccountsSummaryResponse) {};
	rpc InsertFiDepositAccountsSummary(InsertFiDepositAccountsSummaryRequest) returns (InsertFiDepositAccountsSummaryResponse) {};
	rpc CopyFiDepositAccountsSummaryToDeleteCol(CopyFiDepositAccountsSummaryToDeleteColRequest) returns (CopyFiDepositAccountsSummaryToDeleteColResponse) {};
	rpc DeleteFiDepositAccountsSummary(DeleteFiDepositAccountsSummaryRequest) returns (DeleteFiDepositAccountsSummaryResponse) {};
	rpc UpsertFiDepositAccountSummary(UpsertFiDepositAccountSummaryRequest) returns (UpsertFiDepositAccountSummaryResponse) {};
	rpc GetDepositAccountSummariesByLinkedAccRef(GetDepositAccountSummariesByLinkedAccRefRequest) returns (GetDepositAccountSummariesByLinkedAccRefResponse) {};
	rpc GetDepositAccountSummaryByLinkedAccRef(GetDepositAccountSummaryByLinkedAccRefRequest) returns (GetDepositAccountSummaryByLinkedAccRefResponse) {};
}


message UpdateFiDepositAccountsSummaryAndProfileRequest{
	repeated database.FiDepositAccountSummary records = 1;
}
message UpdateFiDepositAccountsSummaryAndProfileResponse{}


message GetDepositAccountsSummaryRequest{
	repeated string ids = 1;
}
message GetDepositAccountsSummaryResponse{
	repeated database.FiDepositAccountSummary records = 1;
}

message GetRecentlyFetchedDepositAccountsRequest {
	int64 fetched_after = 1;
}
message GetRecentlyFetchedDepositAccountsResponse {
	repeated database.FiDepositAccountSummary records = 3;
}


message InsertFiDepositAccountsSummaryRequest{
	repeated database.FiDepositAccountSummary accounts_summary = 1;
}
message InsertFiDepositAccountsSummaryResponse{
	repeated string ids = 1;
}

message CopyFiDepositAccountsSummaryToDeleteColRequest{
	repeated string account_ids = 1;
}
message CopyFiDepositAccountsSummaryToDeleteColResponse {}

message DeleteFiDepositAccountsSummaryRequest{
	repeated string account_ids = 1;
}
message DeleteFiDepositAccountsSummaryResponse{}

message UpsertFiDepositAccountSummaryRequest {
	repeated database.FiDepositAccountSummary summaries = 1;
}

message UpsertFiDepositAccountSummaryResponse {
}

message GetDepositAccountSummariesByLinkedAccRefRequest {
	repeated string linked_acc_refs = 1;
}

message GetDepositAccountSummariesByLinkedAccRefResponse {
	repeated database.FiDepositAccountSummary summaries = 1;
}

message GetDepositAccountSummaryByLinkedAccRefRequest {
	string linked_acc_ref = 1;
}

message GetDepositAccountSummaryByLinkedAccRefResponse {
	database.FiDepositAccountSummary summary = 1;
}