syntax = "proto3";
package backend_services.data_access;
import "database/fi_equities.proto";

service FiEquitiesSummary {
    rpc UpdateFiEquitiesAccountsSummaryAndProfile(UpdateFiEquitiesAccountsSummaryAndProfileRequest) returns (UpdateFiEquitiesAccountsSummaryAndProfileResponse) {};
	rpc GetRecentlyFetchedEquitiesAccounts(GetRecentlyFetchedEquitiesAccountsRequest) returns (GetRecentlyFetchedEquitiesAccountsResponse) {};
	rpc GetEquitiesAccountsSummary(GetEquitiesAccountsSummaryRequest) returns (GetEquitiesAccountsSummaryResponse) {};
	rpc InsertFiEquitiesAccountsSummary(InsertFiEquitiesAccountsSummaryRequest) returns (InsertFiEquitiesAccountsSummaryResponse) {};
	rpc CopyFiEquitiesAccountsSummaryToDeleteCol(CopyFiEquitiesAccountsSummaryToDeleteColRequest) returns (CopyFiEquitiesAccountsSummaryToDeleteColResponse) {};
	rpc DeleteFiEquitiesAccountsSummary(DeleteFiEquitiesAccountsSummaryRequest) returns (DeleteFiEquitiesAccountsSummaryResponse) {};
	rpc UpsertFiEquitiesAccountSummary(UpsertFiEquitiesAccountSummaryRequest) returns (UpsertFiEquitiesAccountSummaryResponse) {};
	rpc GetEquitiesAccountSummariesByLinkedAccRef(GetEquitiesAccountSummariesByLinkedAccRefRequest) returns (GetEquitiesAccountSummariesByLinkedAccRefResponse) {};
}


message UpdateFiEquitiesAccountsSummaryAndProfileRequest{
	repeated database.FiEquitiesAccountSummary records = 1;
}
message UpdateFiEquitiesAccountsSummaryAndProfileResponse{}



message GetEquitiesAccountsSummaryRequest{
	repeated string ids = 1;
}
message GetEquitiesAccountsSummaryResponse{
	repeated database.FiEquitiesAccountSummary records = 1;
}


message GetRecentlyFetchedEquitiesAccountsRequest {
	int64 fetched_after = 1;
}
message GetRecentlyFetchedEquitiesAccountsResponse {
	repeated database.FiEquitiesAccountSummary records = 3;
}


message InsertFiEquitiesAccountsSummaryRequest{
	repeated database.FiEquitiesAccountSummary accounts_summary = 1;
}
message InsertFiEquitiesAccountsSummaryResponse{
	repeated string ids = 1;
}

message CopyFiEquitiesAccountsSummaryToDeleteColRequest{
	repeated string account_ids = 1;
}
message CopyFiEquitiesAccountsSummaryToDeleteColResponse {}

message DeleteFiEquitiesAccountsSummaryRequest{
	repeated string account_ids = 1;
}
message DeleteFiEquitiesAccountsSummaryResponse{}

message UpsertFiEquitiesAccountSummaryRequest {
	repeated database.FiEquitiesAccountSummary summaries = 1;
}

message UpsertFiEquitiesAccountSummaryResponse {
}

message GetEquitiesAccountSummariesByLinkedAccRefRequest {
	repeated string linked_acc_refs = 1;
}

message GetEquitiesAccountSummariesByLinkedAccRefResponse {
	repeated database.FiEquitiesAccountSummary summaries = 1;
}