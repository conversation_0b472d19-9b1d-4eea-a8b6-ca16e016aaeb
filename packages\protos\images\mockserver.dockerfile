FROM bavix/gripmock:2.8.0

RUN apk add rsync

# Generate TLS certs
RUN mkdir /certs
RUN curl -JLO "https://dl.filippo.io/mkcert/latest?for=linux/amd64" && \
     chmod +x mkcert-v*-linux-amd64 && \
     cp mkcert-v*-linux-amd64 /usr/local/bin/mkcert && \
     rm mkcert-v*-linux-amd64
RUN mkcert -key-file /certs/tls.key -cert-file /certs/tls.crt "localhost" "service-mockserver.mock.svc.cluster.local"

# For proto files
RUN mkdir /proto

# For mocking stubs
RUN mkdir /stub

COPY . ./files

# Copy protos. Using rsync instead of find to preserve the folder structure
RUN rsync -a --prune-empty-dirs --include='*/' --include='*.proto' --exclude='*' ./files/ /proto/

# Copy stubs
COPY ./mocks /stub

# Generate args
RUN echo $(find "/proto" -type f -name '*.proto' | tr '\n' ' ') > /proto_args.txt

ENTRYPOINT ["sh", "-c", "proto_args=$(cat /proto_args.txt); gripmock --stub=/stub --imports=/proto/ $proto_args"]