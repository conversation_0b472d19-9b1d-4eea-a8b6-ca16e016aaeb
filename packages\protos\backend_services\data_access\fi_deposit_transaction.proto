syntax = "proto3";
package backend_services.data_access;
import "database/fi_deposit.proto";

service FiDepositTransaction {
    rpc UpsertDepositAccountsTransactions(UpsertDepositAccountsTransactionsRequest) returns (UpsertDepositAccountsTransactionsResponse) {};	
	rpc GetTransactionsCreatedSince(GetTransactionsCreatedSinceRequest) returns (GetTransactionsCreatedSinceResponse) {}; // TODO: Add deposit in name
    rpc DeleteFiDepositAccountsTransactions(DeleteFiDepositAccountsTransactionsRequest) returns (DeleteFiDepositAccountsTransactionsResponse) {};
    rpc CopyFiDepositAccountsTxnsToDeleteCol(CopyFiDepositAccountsTxnsToDeleteColRequest) returns (CopyFiDepositAccountsTxnsToDeleteColResponse) {};
    rpc FindAnyDepositTxnMatchingIds(FindAnyDepositTxnMatchingIdsRequest) returns (FindAnyDepositTxnMatchingIdsResponse) {};
    rpc GetTransactions(GetTransactionsRequest) returns (GetTransactionsResponse) {}; // TODO: Add deposit in name
    rpc GetUnAnalysedDepositTxns(GetUnAnalysedDepositTxnsRequest) returns (GetUnAnalysedDepositTxnsResponse) {};
}


message UpsertDepositAccountsTransactionsRequest {
    repeated database.FiDepositAccountTransaction records = 1;
}
message UpsertDepositAccountsTransactionsResponse {
	map<int64, string> inserted_docs_index_to_id_map = 1;
}


message GetTransactionsCreatedSinceRequest {
    string account_id = 1;
    int64 created_since = 2;
}
message GetTransactionsCreatedSinceResponse {
    repeated database.FiDepositAccountTransaction records = 1;
}

message CopyFiDepositAccountsTxnsToDeleteColRequest{
	repeated string account_ids = 1;
}
message CopyFiDepositAccountsTxnsToDeleteColResponse {}

message DeleteFiDepositAccountsTransactionsRequest{
	repeated string account_ids = 1;
}
message DeleteFiDepositAccountsTransactionsResponse{}

message FindAnyDepositTxnMatchingIdsRequest{
    string account_id = 1;
    repeated string transaction_ids = 2;
}
message FindAnyDepositTxnMatchingIdsResponse{
    database.FiDepositAccountTransaction record = 1;
}

message GetTransactionsRequest {
    repeated string account_ids = 1;
    repeated string types = 2;
    int64 from_time = 3;
    int64 to_time = 4;
}
message GetTransactionsResponse {
    repeated database.FiDepositAccountTransaction records = 1;
}

message GetUnAnalysedDepositTxnsRequest {
    string account_id = 1;
}
message GetUnAnalysedDepositTxnsResponse {
    repeated database.FiDepositAccountTransaction records = 1;
}