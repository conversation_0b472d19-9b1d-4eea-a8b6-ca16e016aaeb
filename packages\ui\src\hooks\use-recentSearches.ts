import { useEffect, useState } from "react";

export const RECENT_SEARCH_KEY = "txn_recent_searches";
const MAX_RECENT_SEARCHES = 5;

function getRecentSearches(): string[] {
  const stored = localStorage.getItem(RECENT_SEARCH_KEY);
  return stored ? JSON.parse(stored) : [];
}

export function saveRecentSearch(search: string) {
  if (!search.trim()) return;
  let recent = getRecentSearches().filter((item) => item !== search);
  recent.unshift(search);
  if (recent.length > MAX_RECENT_SEARCHES)
    recent = recent.slice(0, MAX_RECENT_SEARCHES);
  localStorage.setItem(RECENT_SEARCH_KEY, JSON.stringify(recent));
}

export const useRecentSearches = () => {
  const [recentSearches, setRecentSearches] = useState<string[]>([]);

  useEffect(() => {
    setRecentSearches(getRecentSearches());
  }, []);

  const refreshRecentSearches = () => {
    setRecentSearches(getRecentSearches());
  };

  const removeFromRecentSearch = (index: number) => {
    const updated = recentSearches.filter((_, i) => i !== index);
    localStorage.setItem(RECENT_SEARCH_KEY, JSON.stringify(updated));
    refreshRecentSearches();
  };

  return { recentSearches, refreshRecentSearches, removeFromRecentSearch };
};
