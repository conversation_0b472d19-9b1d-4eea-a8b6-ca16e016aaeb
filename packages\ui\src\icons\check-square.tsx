import * as React from "react";
import type { SVGProps } from "react";
const SvgCheckSquare = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="none"
    viewBox="0 0 24 24"
    {...props}
  >
    <g
      stroke="#905BB5"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={1.491}
    >
      <path d="m9.348 11.388 2.237 2.237 7.456-7.457" />
      <path d="M18.297 12.133v5.22a1.49 1.49 0 0 1-1.491 1.49H6.366a1.49 1.49 0 0 1-1.491-1.49V6.912a1.49 1.49 0 0 1 1.491-1.491h8.203" />
    </g>
  </svg>
);
export default SvgCheckSquare;
