import { useState } from "react";
import { <PERSON>a, StoryObj } from "@storybook/react";

import { Badge, FilterContainer } from "@repo/ui";

const meta: Meta<typeof FilterContainer> = {
  title: "Filters/FilterContainer",
  component: FilterContainer,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof FilterContainer>;

export const Default: Story = {
  render: () => {
    const [isFilterOpen, setIsFilterOpen] = useState(false);

    return (
      <div>
        {" "}
        <Badge
          selected
          className="cursor-pointer fixed top-5 right-5 z-50"
          onClick={() => setIsFilterOpen(true)}
        >
          Open Filter
        </Badge>
        {isFilterOpen && (
          <div>
            <FilterContainer onApply={() => {}} onClear={() => {}} />
          </div>
        )}
      </div>
    );
  },
};
