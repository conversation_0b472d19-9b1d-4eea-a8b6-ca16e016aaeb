import { RecurrentTransactionMonthFilter, RecurrentTxnType } from "@repo/ui";
import { Meta, StoryObj } from "@storybook/react";

const meta: Meta<typeof RecurrentTransactionMonthFilter> = {
  title: "Recurrent Transaction/Recurrent Transaction Month Filter",
  component: RecurrentTransactionMonthFilter,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
  },
  argTypes: {
    selected: {
      options: Object.values(RecurrentTxnType),
      control: {
        type: "select",
      },
    },
  },
};

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    selected: RecurrentTxnType.UPCOMING,
    onSelect: () => {},
  },
};

export const PaidSelected: Story = {
  args: {
    selected: RecurrentTxnType.PAID,
    onSelect: () => {},
  },
};

export const MissedSelected: Story = {
  args: {
    selected: RecurrentTxnType.MISSED,
    onSelect: () => {},
  },
};
