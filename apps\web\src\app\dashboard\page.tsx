import { Metadata } from "next";
import { redirect } from "next/navigation";
import {
  AppSidebar,
  Account,
  dummyTransactions,
  extractCategoryFilters,
  extractStringParams,
  TransactionCategory,
  TransactionGroup,
} from "@repo/ui";
import { getTransactions } from "../lib/transaction";
import { getAccounts } from "../lib/account";
import { groupTransactionsByDate } from "../lib/helper";
import { cookies } from "next/headers";
import { CONSTANTS, ROUTE } from "../lib/constants";
import {
  deleteTransactionDocuments,
  fetchTransactionDocuments,
  searchTransaction,
  updateTransaction,
  updateTransactionCategory,
} from "../actions/transaction-actions";
import TransactionsClientContainer from "../components/TransactionsClientContainer";
import { items } from "@repo/ui/data/dummyNavigation";
import { getMembersAndGroups } from "../lib/member-mapper";
import { SearchBy } from "../lib/types/transaction.types";

export const metadata: Metadata = {
  title: "Transaction Dashboard",
};
interface TimeRange {
  fromTime: number;
  toTime: number;
}
interface AmountRange {
  minAmount: number;
  maxAmount: number;
}
interface BookmarkOptions {
  showFavorites?: boolean;
  excludedFromCashflow?: boolean;
  withNotes?: boolean;
}

interface UserId {
  id: string;
}

interface UserGroups {
  userIds: UserId[];
  userGroupIds: UserId[];
}

interface AccountFilters {
  accountIds: string[];
}

interface FilterOptions {
  userGroups?: UserGroups;
  accountFilters?: AccountFilters;
  timeRange?: TimeRange;
  amountRange?: AmountRange;
  transactionType?: string;
  bookmarkOptions?: BookmarkOptions;
  tagStatus?: string;
  categories?: TransactionCategory[];
}

function filterDummyTransactions(
  transactions: TransactionGroup[],
  fromDate?: string,
  toDate?: string,
  minAmount?: string,
  maxAmount?: string,
  transactionType?: string,
  showFavorites?: boolean,
  excludedFromCashflow?: boolean,
  withNotes?: boolean,
  tagStatus?: string,
  categoryFilters?: TransactionCategory[],
  memberIds?: string[],
  groupIds?: string[],
  accountIds?: string[],
): TransactionGroup[] {
  const fromDateObj = fromDate ? new Date(fromDate) : null;
  if (fromDateObj) fromDateObj.setHours(0, 0, 0, 0);
  const toDateObj = toDate ? new Date(toDate) : null;
  if (toDateObj) toDateObj.setHours(23, 59, 59, 999);
  const fromTimestamp = fromDateObj ? fromDateObj.getTime() : 0;
  const toTimestamp = toDateObj ? toDateObj.getTime() : Date.now();
  const minAmountValue = minAmount ? parseFloat(minAmount) : 0;
  const maxAmountValue = maxAmount
    ? parseFloat(maxAmount)
    : Number.MAX_SAFE_INTEGER;
  const hasMemberFilter = memberIds && memberIds.length > 0;
  const hasGroupFilter = groupIds && groupIds.length > 0;

  const filteredGroups = transactions.filter((group) => {
    const groupDate = new Date(group.date);
    groupDate.setHours(0, 0, 0, 0);
    const groupTimestamp = groupDate.getTime();
    const isInDateRange =
      groupTimestamp >= fromTimestamp &&
      (!toDateObj || groupTimestamp <= toTimestamp);

    const filteredTransactions = group.transactions.filter((txn) => {
      const amount = Math.abs(txn.amount);
      const isInAmountRange =
        amount >= minAmountValue && amount <= maxAmountValue;
      const transactionTypeMatch =
        !transactionType ||
        transactionType === "All" ||
        (transactionType === "Incoming" && txn.amount > 0) ||
        (transactionType === "Outgoing" && txn.amount < 0);

      const isExcluded = txn.isExcludedCashflow === true;
      const tagStatusMatch =
        !tagStatus ||
        tagStatus === "All" ||
        (tagStatus === "Tagged" && txn.tag) ||
        (tagStatus === "Untagged" && !txn.tag);

      const bookmarkMatch =
        (!showFavorites || txn.isSaved) &&
        (!excludedFromCashflow || isExcluded) &&
        (!withNotes || (txn.notes && txn.notes.trim() !== ""));

      const categoryMatch =
        !categoryFilters ||
        categoryFilters.length === 0 ||
        categoryFilters.some((filter) => {
          return (
            txn.tag?.categoryId === filter.categoryId &&
            (filter.subcategoryId === "" ||
              txn.tag?.subcategoryId === filter.subcategoryId)
          );
        });
      const memberMatch = !hasMemberFilter;
      const groupMatch = !hasGroupFilter;
      return (
        isInAmountRange &&
        transactionTypeMatch &&
        bookmarkMatch &&
        tagStatusMatch &&
        categoryMatch &&
        memberMatch &&
        groupMatch
      );
    });

    group.transactions = filteredTransactions;
    return isInDateRange && filteredTransactions.length > 0;
  });

  return filteredGroups;
}

function getSearchByEnum(value: string | undefined): SearchBy {
  switch ((value || "").toLowerCase()) {
    case "tag":
      return SearchBy.SEARCH_BY_TAG;
    case "merchant":
      return SearchBy.SEARCH_BY_MERCHANT;
    case "amount":
      return SearchBy.SEARCH_BY_AMOUNT;
    case "remark":
      return SearchBy.SEARCH_BY_REMARKS;
    case "notes":
      return SearchBy.SEARCH_BY_NOTES;
    case "user":
      return SearchBy.SEARCH_BY_USER;
    default:
      return SearchBy.SEARCH_BY_ALL;
  }
}

export default async function Page({
  searchParams,
}: {
  searchParams: Promise<Record<string, string | undefined>>;
}) {
  const resolvedParams = await searchParams;
  const isAnyFilterSet =
    resolvedParams.fromDate ||
    resolvedParams.toDate ||
    resolvedParams.minAmount ||
    resolvedParams.maxAmount ||
    resolvedParams.transactionType ||
    resolvedParams.tagStatus ||
    resolvedParams.showFavorites ||
    resolvedParams.excludedFromCashflow ||
    resolvedParams.withNotes ||
    resolvedParams.categoryFiltersCount ||
    resolvedParams.memberMatch ||
    resolvedParams.groupMatch ||
    resolvedParams.accountMatch;

  if (!isAnyFilterSet) {
    resolvedParams.memberCount = "0";
    resolvedParams.groupCount = "0";
  }
  const cookiesStore = await cookies();
  const authToken: string =
    cookiesStore.get(CONSTANTS.SESSION_COOKIE_NAME)?.value || "";
  const phoneNumber =
    cookiesStore.get(CONSTANTS.USER_PHONE_NUMBER)?.value || "";

  if (!authToken) redirect(ROUTE.LOGIN);
  let transactions: TransactionGroup[] = [];
  let searchResults: number = 0;
  let accounts: Account[] = [];
  const { members, groups } = await getMembersAndGroups(authToken);
  const showFavorites = resolvedParams.showFavorites === "true";
  const excludedFromCashflow = resolvedParams.excludedFromCashflow === "true";
  const withNotes = resolvedParams.withNotes === "true";
  const tagStatus = resolvedParams.tagStatus || "All";
  let initialPage = 0;
  const pageSize = 20;
  let filterOptions: Partial<FilterOptions> | undefined = undefined;

  const categoryCount = parseInt(
    resolvedParams.categoryFiltersCount || "0",
    10,
  );
  const categoryFilters = extractCategoryFilters(resolvedParams, categoryCount);

  const memberCount = parseInt(resolvedParams.memberCount || "0", 10);
  const memberIds = extractStringParams(
    resolvedParams,
    "memberId",
    memberCount,
  );

  const groupCount = parseInt(resolvedParams.groupCount || "0", 10);
  const groupIds = extractStringParams(resolvedParams, "groupId", groupCount);

  const accountCount = parseInt(resolvedParams.accountCount || "0", 10);
  const accountIds = extractStringParams(
    resolvedParams,
    "accountId",
    accountCount,
  );

  if (process.env.SHOW_DUMMY_DATA === "true") {
    transactions = filterDummyTransactions(
      dummyTransactions,
      resolvedParams.fromDate,
      resolvedParams.toDate,
      resolvedParams.minAmount,
      resolvedParams.maxAmount,
      resolvedParams.transactionType,
      showFavorites,
      excludedFromCashflow,
      withNotes,
      tagStatus,
      categoryFilters,
      memberIds,
      groupIds,
      accountIds,
    );
  } else {
    try {
      filterOptions = {};

      if (memberIds.length > 0 || groupIds.length > 0) {
        filterOptions.userGroups = {
          userIds: memberIds.map((id) => ({ id })),
          userGroupIds: groupIds.map((id) => ({ id })),
        };
      }
      if (accountIds.length > 0) {
        filterOptions.accountFilters = {
          accountIds: accountIds,
        };
      }
      if (categoryFilters.length > 0) {
        filterOptions.categories = categoryFilters;
      }
      if (resolvedParams.fromDate || resolvedParams.toDate) {
        const fromDate = resolvedParams.fromDate
          ? new Date(resolvedParams.fromDate)
          : null;
        const toDate = resolvedParams.toDate
          ? new Date(resolvedParams.toDate)
          : null;

        if (
          (fromDate && isNaN(fromDate.getTime())) ||
          (toDate && isNaN(toDate.getTime()))
        ) {
          throw new Error("Invalid date format provided");
        }

        filterOptions.timeRange = {
          fromTime: fromDate
            ? Math.floor(fromDate.setHours(0, 0, 0, 0) / 1000)
            : 0,
          toTime: toDate
            ? Math.floor(toDate.setHours(23, 59, 59, 999) / 1000)
            : Math.floor(Date.now() / 1000),
        };
      }

      if (resolvedParams.minAmount || resolvedParams.maxAmount) {
        filterOptions.amountRange = {
          minAmount: resolvedParams.minAmount
            ? parseFloat(resolvedParams.minAmount)
            : 0,
          maxAmount: resolvedParams.maxAmount
            ? parseFloat(resolvedParams.maxAmount)
            : 200000,
        };
      }

      if (
        resolvedParams.transactionType &&
        resolvedParams.transactionType !== "All"
      ) {
        filterOptions.transactionType = resolvedParams.transactionType;
      }
      if (tagStatus !== "All") {
        filterOptions.tagStatus = tagStatus;
      }
      if (showFavorites || excludedFromCashflow || withNotes) {
        filterOptions.bookmarkOptions = {
          showFavorites,
          excludedFromCashflow,
          withNotes,
        };
      }
      const [transactionResponse, accountsResponse] = await Promise.all([
        getTransactions(authToken, pageSize, initialPage, filterOptions),
        getAccounts(authToken),
      ]);
      const { cards: transactionCards } = transactionResponse;
      accounts = accountsResponse.accounts;
      transactions = groupTransactionsByDate(transactionCards || [], accounts);
    } catch (error: any) {
      console.error(error);
      if (error?.code === 16 || error.message.includes("unauthenticated")) {
        redirect(ROUTE.LOGOUT_API);
      }
      transactions = [];
    }
  }

  if (resolvedParams.search && resolvedParams.search.trim() !== "") {
    try {
      const searchBy = getSearchByEnum(resolvedParams.searchCategory);
      const [searchResultCard, accountsResponse] = await Promise.all([
        searchTransaction(
          resolvedParams.search,
          searchBy,
          initialPage,
          pageSize,
          filterOptions,
        ),
        getAccounts(authToken),
      ]);
      const { cards: transactionCards } = searchResultCard;
      const { accounts } = accountsResponse;
      searchResults = transactionCards.length;
      transactions = groupTransactionsByDate(transactionCards || [], accounts);
    } catch (error: any) {
      console.error("Error searching transactions:", error);
      if (error?.code === 16 || error.message.includes("unauthenticated")) {
        redirect(ROUTE.LOGOUT_API);
      }
      transactions = [];
    }
  }
  const getMoreTransactions = async (page: number) => {
    "use server";
    const res = await getTransactions(authToken, pageSize, page, filterOptions);

    return groupTransactionsByDate(res.cards || [], accounts);
  };

  return (
    <AppSidebar username={"John Doe"} email={"<EMAIL>"}>
      <TransactionsClientContainer
        initialTransactions={transactions}
        initialPage={initialPage}
        fetchMoreTransactions={getMoreTransactions}
        changeCategoryAction={updateTransactionCategory}
        updateTransaction={updateTransaction}
        fetchTransactionDocuments={fetchTransactionDocuments}
        deleteTransactionDocuments={deleteTransactionDocuments}
        phoneNumber={phoneNumber}
        authToken={authToken}
        members={members}
        groups={groups}
        filters={{
          fromDate: resolvedParams.fromDate,
          toDate: resolvedParams.toDate,
          minAmount: resolvedParams.minAmount,
          maxAmount: resolvedParams.maxAmount,
          transactionType: resolvedParams.transactionType,
          tagStatus: resolvedParams.tagStatus,
          bookmarkOptions: {
            showFavorites,
            excludedFromCashflow,
            withNotes,
          },
          categoryFilters: categoryFilters,
        }}
        filter={{
          userGroups: {
            ...(resolvedParams.groupMatch || resolvedParams.memberMatch
              ? {
                  userIds: memberIds.map((id) => ({ id })),
                  userGroupIds: groupIds.map((id) => ({ id })),
                }
              : {}),
          },
          accountFilters:
            accountIds.length > 0
              ? {
                  accountIds: accountIds.map((id) => ({ id })),
                }
              : undefined,
          txnFilters: {
            ...(resolvedParams.fromDate || resolvedParams.toDate
              ? {
                  timeRange: {
                    fromTime: resolvedParams.fromDate,
                    toTime: resolvedParams.toDate,
                  },
                }
              : {}),
            ...(resolvedParams.minAmount || resolvedParams.maxAmount
              ? {
                  amountRange: {
                    minAmount: resolvedParams.minAmount,
                    maxAmount: resolvedParams.maxAmount,
                  },
                }
              : {}),
            ...(showFavorites
              ? {
                  favorited: { favorited: true },
                }
              : {}),
            ...(excludedFromCashflow
              ? {
                  excludeCashFlow: { excludeCashFlow: true },
                }
              : {}),
            ...(withNotes
              ? {
                  hasUserNotes: { hasUserNotes: true },
                }
              : {}),
            ...(resolvedParams.transactionType &&
            resolvedParams.transactionType !== "All"
              ? {
                  txnType: {
                    txnType:
                      resolvedParams.transactionType === "Incoming"
                        ? "CREDIT"
                        : "DEBIT",
                  },
                }
              : {}),
          },
        }}
        searchResults={searchResults}
      />
    </AppSidebar>
  );
}
