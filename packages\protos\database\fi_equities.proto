syntax = "proto3";
package database;

import "rebit/fi_schemas/equities/equities.proto";
import "database/custom_type.proto";

// flag:is-collection=true;
message FiEquitiesAccountSummary {
    ObjectId _id = 1;

    // FIP Identifier
    string fip_id = 2;

    // Financial information type
    string fi_type = 3;

    // Masked demat id
    string masked_demat_id = 4;

    // Unique account number associated with aa account. 
    string linked_acc_ref = 5;
  
    // Basic Profile of the account which should include the Account Owner information, maskedAccNumber and linkedAccRef numbers, type of account specific to the FI type and any other generic details as might be pertinent for the specified FI type.
    rebit.fi_schemas.equities.Profile profile = 6;

    // The value of the account, term of the deposits if relevant and any other data that summarizes the funds in the account.
    rebit.fi_schemas.equities.Summary summary = 7;

    // The date till which the transactions are reflected in the summary.
    string holdings_date = 8;
    
    // Document creation timestamp
    int64 created_at = 9;

    // Document modification timestamp
    int64 updated_at = 10;

    // This value should never be updated, this value will be used as a reference point to recreate the gains
    FiEquitiesAccountInitialSummary initial_summary = 11;
}

message FiEquitiesAccountInitialSummary {
    rebit.fi_schemas.equities.Summary summary = 1;
    int64 last_transaction_timestamp = 2; // @gotags: bson:"last_transaction_timestamp"
}

// flag:is-collection=true;
message FiEquitiesAccountTransaction {
    ObjectId _id = 1;  

    // FIP Identifier
    string fip_id = 2;

    // Financial information type
    string fi_type = 3;

    // _id of account summary record in database
    ObjectId account_id = 5; 

    // Masked demat id
    string masked_demat_id = 6;
  
    // Details of all transactions that have been posted in an account.
    rebit.fi_schemas.equities.Transaction transaction = 7;
  
    // Document creation timestamp
    int64 created_at = 8;

    // Document modification timestamp
    int64 updated_at = 9;
}  

// flag:is-collection=true;
// Snapshot of holdings at summary_date; Monthly for now 
message EquitiesAccountHoldings {
    ObjectId _id = 1;
    ObjectId account_id = 2; 
    int64 summary_date = 3;
    double holdings_value = 4;
    int64 created_at = 5;
    int64 updated_at = 6;
}

// flag:is-collection=true;
// Realized, unrealized and unknown gains
message EquitiesGain {
    ObjectId _id = 1;  
    ObjectId account_id = 2; 
    string isin = 3;
    uint64 units = 4;

    string buy_rate = 5;
    int64 buy_timestamp = 6;

    string sell_rate = 7;
    int64 sell_timestamp = 8;

    int64 created_at = 9;
    int64 updated_at = 10;

    int64 last_processed_transaction_timestamp = 11; // @gotags: bson:"last_processed_transaction_timestamp"
}

// flag:is-collection=true;
message DeletedFiEquitiesAccountSummary {}

// flag:is-collection=true;
message DeletedFiEquitiesAccountTransaction {}