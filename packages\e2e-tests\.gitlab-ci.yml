image: mcr.microsoft.com/playwright:v1.52.0-noble

stages:
  - test

variables:
  CI: "true"
  PLAYWRIGHT_VERSION: "1.42.1"
  NODE_VERSION: "20"
  PNPM_VERSION: "9"
  MONGODB_DB_NAME: ${MONGODB_DB_NAME}
  GRPC_SERVER_URI: ${GRPC_SERVER_URI}
  PROTO_DIR_PATH: ${PROTO_DIR_PATH}
  NEXT_PUBLIC_FIREBASE_API_KEY: ${NEXT_PUBLIC_FIREBASE_API_KEY}
  NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: ${NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN}
  NEXT_PUBLIC_FIREBASE_PROJECT_ID: ${NEXT_PUBLIC_FIREBASE_PROJECT_ID}
  NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET: ${NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET}
  NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: ${NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID}
  NEXT_PUBLIC_FIREBASE_APP_ID: ${NEXT_PUBLIC_FIREBASE_APP_ID}
  NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID: ${NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID}

cache:
  key:
    files:
      - pnpm-lock.yaml
  paths:
    - .pnpm-store/
    - node_modules/
    - packages/*/node_modules/
  policy: pull-push

test:ci:
  stage: test
  script:
    # Install pnpm
    - npm install -g pnpm@$PNPM_VERSION
    # Install dependencies
    - pnpm install
    # Install Playwright browsers
    - pnpm --filter @repo/e2e-tests install:browsers
    # Run all tests
    - pnpm --filter @repo/e2e-tests test:ci 
  artifacts:
    when: always
    paths:
      - packages/e2e-tests/test-results/
      - packages/e2e-tests/playwright-report/
    reports:
      junit: packages/e2e-tests/test-results/junit.xml
    expire_in: 1 day
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
      when: manual