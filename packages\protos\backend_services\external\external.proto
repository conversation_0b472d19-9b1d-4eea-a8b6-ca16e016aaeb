syntax = "proto3";
package backend_services.external;


// Service for external interfaces
service ExternalService{
    // Consent Webhook Handler (Needs Transcoding)
    rpc HandleConsentNotification(ConsentNotificationRequest) returns (ConsentNotificationResponse) {};
    
    // Data Webhook Handler (Needs Transcoding)
    rpc HandleDataNotification(DataNotificationRequest) returns (DataNotificationResponse) {};
}
  
message ConsentNotificationRequest {
    string consent_handle = 1;
    string consent_id = 2;
    string status = 3;
}
message ConsentNotificationResponse {
    int64 status_code = 1; // Indicates status of processing for webhook retry
}

message DataNotificationRequest {
    string consent_handle = 1;
    string data_session_id = 2;
    string status = 3;
}
message DataNotificationResponse {
    int64 status_code = 1; // Indicates status of processing for webhook retry
}