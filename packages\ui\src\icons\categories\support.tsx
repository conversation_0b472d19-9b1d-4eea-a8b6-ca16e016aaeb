import * as React from "react";
import type { SVGProps } from "react";
const SvgSupport = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="currentColor"
    viewBox="0 0 30 23"
    {...props}
  >
    <path d="m17.552 8.918-.4-.028c-.698-.059-1.423-.115-2.118-.077a.4.4 0 0 0-.246.109c-.699.65-1.4 1.263-1.818 1.601a2.91 2.91 0 0 1-2.22.61 2.76 2.76 0 0 1-1.935-1.195.77.77 0 0 1 .12-1l5.131-4.825-2.234-2.146C10.544.727 8.828.047 7.005.047s-3.535.68-4.822 1.92a6.395 6.395 0 0 0 0 9.3l3.499 3.362 7.941 7.47c.213.204.482.244.672.244a1.53 1.53 0 0 0 1.021-.424c.518-.49.605-1.205.192-1.592l-4.376-4.12a.302.302 0 0 1 .003-.449.357.357 0 0 1 .48 0l4.377 4.116.285.27c.213.2.48.242.665.242a1.5 1.5 0 0 0 1.024-.425c.296-.282.452-.637.452-.963a.86.86 0 0 0-.26-.631l-4.376-4.12a.304.304 0 0 1 .004-.449.357.357 0 0 1 .479 0l4.663 4.386a.95.95 0 0 0 .665.242c.362 0 .731-.151 1.024-.425.296-.282.452-.637.452-.963a.84.84 0 0 0-.267-.631l-4.37-4.116a.303.303 0 0 1 0-.449.36.36 0 0 1 .482 0l4.656 4.382c.403.375 1.187.291 1.689-.183.292-.27.456-.623.456-.96a.83.83 0 0 0-.267-.632l-5.89-5.547z" />
    <path d="M21.717.098c-2.44.31-4.656 2.097-5.76 3.137l-6.543 6.15a.16.16 0 0 0-.025.211c.345.499.867.82 1.466.905a2.2 2.2 0 0 0 1.673-.46 42 42 0 0 0 1.783-1.574c.19-.173.432-.275.684-.291.749-.044 1.497.016 2.222.074.851.071 1.663.133 2.481.043 1.273-.136 2.305-.798 3.243-1.465a.35.35 0 0 1 .475.062.303.303 0 0 1-.066.446c-1.02.719-2.136 1.437-3.579 1.592a9 9 0 0 1-1.2.04l5.357 5.04q.166.164.273.365l3.172-3.048c1.335-1.279 2.069-2.958 2.069-4.713 0-1.757-.708-3.403-2.006-4.65C25.946.532 23.81-.166 21.718.094z" />
  </svg>
);
export default SvgSupport;
