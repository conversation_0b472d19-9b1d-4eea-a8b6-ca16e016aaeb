syntax = "proto3";
package backend_services.data_access;
import "database/fi_deposit.proto";

service DepositDailyClosingBalance {
    rpc UpsertDailyClosingBalanceDepositAccount(UpsertDailyClosingBalanceDepositAccountRequest) returns (UpsertDailyClosingBalanceDepositAccountResponse) {};
	rpc GetDailyClosingBalanceDepositAccount(GetDailyClosingBalanceDepositAccountRequest) returns (GetDailyClosingBalanceDepositAccountResponse) {};
	rpc DeleteDepositDailyClosingBalanceAccountsData(DeleteDepositDailyClosingBalanceAccountsDataRequest) returns (DeleteDepositDailyClosingBalanceAccountsDataResponse) {}
	rpc GetClosingBalances(GetClosingBalancesRequest) returns (GetClosingBalancesResponse) {};
	rpc GetRecentDepositDailyClosingBalances(GetRecentDepositDailyClosingBalancesRequest) returns (GetRecentDepositDailyClosingBalancesResponse) {};
}


message UpsertDailyClosingBalanceDepositAccountRequest {
	repeated database.DepositAccountDailyClosingBalance records = 1;
}
message UpsertDailyClosingBalanceDepositAccountResponse {}


message GetDailyClosingBalanceDepositAccountRequest{
	string account_id = 1;
	int64 before_summary_date = 2;
}
message GetDailyClosingBalanceDepositAccountResponse{
	database.DepositAccountDailyClosingBalance record = 1;
}

message GetClosingBalancesRequest{
	string account_id = 1;
	int64 summary_date_after = 2;
	int64 summary_date_before = 3;
}
message GetClosingBalancesResponse{
	repeated database.DepositAccountDailyClosingBalance records = 1;
}

message DeleteDepositDailyClosingBalanceAccountsDataRequest{
	repeated string account_ids = 1;
}
message DeleteDepositDailyClosingBalanceAccountsDataResponse{}

message GetRecentDepositDailyClosingBalancesRequest {
	repeated string account_ids = 1;
}

message GetRecentDepositDailyClosingBalancesResponse {
	repeated database.DepositAccountDailyClosingBalance records = 1;
}