interface FilterIconProps {
  className?: string;
}

export const FilterIcon: React.FC<FilterIconProps> = ({ className }) => {
  return (
    <svg
      width="13"
      height="12"
      viewBox="0 0 13 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="Group">
        <g id="Group_2">
          <g id="Group_3">
            <path
              id="Vector"
              d="M1.45766 2.62887H3.15488C3.38286 3.18616 3.9275 3.59147 4.57345 3.59147C5.21941 3.59147 5.76404 3.19883 5.99203 2.62887H12.3503C12.6669 2.62887 12.9202 2.37555 12.9202 2.0589C12.9202 1.74226 12.6669 1.48894 12.3503 1.48894H5.99203C5.76404 0.906313 5.21941 0.513672 4.57345 0.513672C3.9275 0.513672 3.38286 0.906313 3.15488 1.47628H1.45766C1.14101 1.47628 0.887695 1.72959 0.887695 2.04624C0.887695 2.36288 1.14101 2.62887 1.45766 2.62887Z"
              fill="#607BF4"
            />
            <path
              id="Vector_2"
              d="M12.3503 5.45284H10.6531C10.4251 4.89554 9.88044 4.49023 9.23448 4.49023C8.58852 4.49023 8.04389 4.88288 7.81591 5.45284H1.45766C1.14101 5.45284 0.887695 5.70615 0.887695 6.0228C0.887695 6.33945 1.14101 6.59276 1.45766 6.59276H7.81591C8.04389 7.15006 8.58852 7.55537 9.23448 7.55537C9.88044 7.55537 10.4251 7.16273 10.6531 6.59276H12.3503C12.6669 6.59276 12.9202 6.33945 12.9202 6.0228C12.9202 5.70615 12.6669 5.45284 12.3503 5.45284Z"
              fill="#607BF4"
            />
            <path
              id="Vector_3"
              d="M12.3503 9.41768H5.99203C5.76404 8.86039 5.21941 8.45508 4.57345 8.45508C3.9275 8.45508 3.38286 8.84772 3.15488 9.41768H1.45766C1.14101 9.41768 0.887695 9.671 0.887695 9.98764C0.887695 10.3043 1.14101 10.5576 1.45766 10.5576H3.15488C3.38286 11.1149 3.9275 11.5202 4.57345 11.5202C5.21941 11.5202 5.76404 11.1276 5.99203 10.5576H12.3503C12.6669 10.5576 12.9202 10.3043 12.9202 9.98764C12.9202 9.68366 12.6669 9.41768 12.3503 9.41768Z"
              fill="#607BF4"
            />
          </g>
        </g>
      </g>
    </svg>
  );
};
