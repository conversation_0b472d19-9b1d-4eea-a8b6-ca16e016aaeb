syntax = "proto3";
package backend_services.data_access;
import "database/custom_type.proto";
import "database/transaction_category.proto";

service TransactionCategory {
    rpc InsertUserTxnCategory(InsertUserTxnCategoryRequest) returns (InsertUserTxnCategoryResponse) {};
	rpc UpdateUserTxnCategory(UpdateUserTxnCategoryRequest) returns (UpdateUserTxnCategoryResponse) {};
	rpc DeleteUserTxnCategory(DeleteUserTxnCategoryRequest) returns (DeleteUserTxnCategoryResponse) {};
	rpc GetTxnCategory(GetTxnCategoryRequest) returns (GetTxnCategoryResponse) {};
	rpc GetTxnCategoriesById(GetTxnCategoriesByIdRequest) returns (GetTxnCategoriesByIdResponse) {};
}


message InsertUserTxnCategoryRequest{
	database.TransactionCategory record = 1;
}	
message InsertUserTxnCategoryResponse{
	database.ObjectId category_id = 1;
}


message UpdateUserTxnCategoryRequest{
	database.ObjectId category_id = 1;
	string category_name = 2;
}
message UpdateUserTxnCategoryResponse{}


message GetTxnCategoryRequest {
	database.ObjectId category_id = 1;
}
message GetTxnCategoryResponse {
	database.TransactionCategory record = 1;
}

message DeleteUserTxnCategoryRequest {
	database.ObjectId category_id = 1;
}
message DeleteUserTxnCategoryResponse{}

message GetTxnCategoriesByIdRequest {
	repeated database.ObjectId category_ids = 1;
}

message GetTxnCategoriesByIdResponse {
	repeated database.TransactionCategory records = 1;
}
