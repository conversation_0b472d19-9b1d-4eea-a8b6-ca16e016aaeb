[{"service": "RecurrentGroups", "method": "GetRecurrentTxns", "input": {"equals": {}}, "headers": {"contains": {"type": "success"}}, "output": {"data": {"summaries": [{"recurrent_group_id": "group_1_id", "merchant": {"user_id": "", "merchant_id": "62ea81593c0d8b2f8a68785d", "merchant_name": "Zomato India Pvt. Ltd."}, "tag": {"category_collection": "global", "category_id": "62ea81593c0d8b2f8a62785d", "subcategory_id": "62ea81593c0d8b2f8b68785d"}, "account_id": "67ea81593c0d8b1f8a68785d", "user_id": "63ea81593c0d8b1f8a68785d", "amount": 12123.122, "frequency": "Monthly", "dates": ["1st", "5th"], "favorite": false, "exclude_cash_flow": true, "txn_mode": "UPI"}, {"recurrent_group_id": "group_2_id", "merchant": {"user_id": "", "merchant_id": "61ea81593c0d8b2f8a68785d", "merchant_name": "HDFC Mutual Fund AMC"}, "tag": {"category_collection": "global", "category_id": "60ea81593c0d8b2f8a62785d", "subcategory_id": "60ea81593c0d8b2f8b68785d"}, "account_id": "67ea81593c0d8b1f8a68785d", "user_id": "63ea81593c0d8b1f8a68785d", "amount": 235345.122, "frequency": "Monthly", "dates": ["10th"], "favorite": true, "exclude_cash_flow": false, "txn_mode": "AUTO_DEBIT"}], "txns": [{"recurrent_txn_type": "RECURRENT_TXN_TYPE_PAID", "txn_card": {"txn_id": "67dd216b4918a136984725a5", "account_id": "67ea81593c0d8b1f8a68785d", "amount": 6883, "type": "DEBIT", "txn_timestamp": "**********", "mode": "UPI", "narration": "Food Order", "raw_txn_id": "a4c1afbb-a860-4757-b41d-b2e8da86633b", "favorite": true, "tag": {"category_collection": "global", "category_id": "62ea81593c0d8b2f8a62785d", "subcategory_id": "62ea81593c0d8b2f8b68785d"}, "merchant": {"user_id": "", "merchant_id": "62ea81593c0d8b2f8a68785d", "merchant_name": "Zomato India Pvt. Ltd."}, "exclude_cash_flow": false, "fip_id": "", "user_notes": "testtt Notes ", "cash_flow_period": {"month": 3, "year": 2025}, "documents_count": 2, "recurrent_group_id": "group_1_id"}}, {"recurrent_txn_type": "RECURRENT_TXN_TYPE_UPCOMING", "txn_card": {"txn_id": "", "account_id": "67ea81593c0d8b1f8a68785d", "amount": 6883, "type": "DEBIT", "txn_timestamp": "**********", "recurrent_group_id": "group_1_id"}}, {"recurrent_txn_type": "RECURRENT_TXN_TYPE_MISSED", "txn_card": {"txn_id": "", "account_id": "67ea81593c0d8b1f8a68785d", "amount": 6883, "type": "DEBIT", "txn_timestamp": "**********", "recurrent_group_id": "group_1_id"}}, {"recurrent_txn_type": "RECURRENT_TXN_TYPE_PAID", "txn_card": {"txn_id": "67dd216b4918a136984725b5", "account_id": "67ea81593c0d8b1f8a68785d", "amount": 3386, "type": "DEBIT", "txn_timestamp": "**********", "mode": "UPI", "narration": "Food Order", "raw_txn_id": "a5c1afbb-a860-4757-b41d-b2e8da86633b", "favorite": true, "tag": {"category_collection": "global", "category_id": "60ea81593c0d8b2f8a62785d", "subcategory_id": "60ea81593c0d8b2f8b68785d"}, "merchant": {"user_id": "", "merchant_id": "61ea81593c0d8b2f8a68785d", "merchant_name": "HDFC Mutual Fund AMC"}, "exclude_cash_flow": false, "fip_id": "", "user_notes": "testtt Notes ", "cash_flow_period": {"month": 3, "year": 2025}, "documents_count": 2, "recurrent_group_id": "group_2_id"}}, {"recurrent_txn_type": "RECURRENT_TXN_TYPE_UPCOMING", "txn_card": {"txn_id": "", "account_id": "67ea81593c0d8b1f8a68785d", "amount": 3386, "type": "DEBIT", "txn_timestamp": "**********", "recurrent_group_id": "group_2_id"}}]}, "code": 0}}, {"service": "RecurrentGroups", "method": "GetCommonRecurrentGroups", "input": {"equals": {}}, "headers": {"contains": {"type": "success"}}, "output": {"data": {"cards": [{"merchant": {"user_id": "", "merchant_id": "62ea81593c0d8b2f8a68785d", "merchant_name": "Zomato India Pvt. Ltd."}, "amount": 150.0, "user_ids": ["63ea81593c0d8b1f8a68785d", "63ea81593c0d8b1f8a68785e"], "frequency": "Monthly", "dates": ["1st", "5th", "10th"], "summaries": [{"recurrent_group_id": "group_1_id", "merchant": {"user_id": "", "merchant_id": "62ea81593c0d8b2f8a68785d", "merchant_name": "Zomato India Pvt. Ltd."}, "tag": {"category_collection": "global", "category_id": "62ea81593c0d8b2f8a62785d", "subcategory_id": "62ea81593c0d8b2f8b68785d"}, "account_id": "67ea81593c0d8b1f8a68785d", "user_id": "63ea81593c0d8b1f8a68785d", "amount": 100.0, "frequency": "Monthly", "dates": ["1st", "5th"], "favorite": false, "exclude_cash_flow": true, "txn_mode": "UPI"}, {"recurrent_group_id": "group_3_id", "merchant": {"user_id": "", "merchant_id": "62ea81593c0d8b2f8a68785d", "merchant_name": "Zomato India Pvt. Ltd."}, "tag": {"category_collection": "global", "category_id": "62ea81593c0d8b2f8a62785d", "subcategory_id": "62ea81593c0d8b2f8b68785d"}, "account_id": "67ea81593c0d8b1f8a68785d", "user_id": "63ea81593c0d8b1f8a68785d", "amount": 200.0, "frequency": "Monthly", "dates": ["10th"], "favorite": false, "exclude_cash_flow": true, "txn_mode": "UPI"}]}]}, "code": 0}}, {"service": "RecurrentGroups", "method": "GetRecurrentGroupDetails", "input": {"equals": {}}, "headers": {"contains": {"type": "success"}}, "output": {"data": {"summary": {"recurrent_group_id": "group_1_id", "merchant": {"user_id": "", "merchant_id": "62ea81593c0d8b2f8a68785d", "merchant_name": "Zomato India Pvt. Ltd."}, "tag": {"category_collection": "global", "category_id": "62ea81593c0d8b2f8a62785d", "subcategory_id": "62ea81593c0d8b2f8b68785d"}, "account_id": "67ea81593c0d8b1f8a68785d", "user_id": "63ea81593c0d8b1f8a68785d", "amount": 100.0, "frequency": "Monthly", "dates": ["1st", "5th"], "favorite": false, "exclude_cash_flow": true, "txn_mode": "UPI"}}, "txns": [{"recurrent_txn_type": "RECURRENT_TXN_TYPE_PAID", "txn_card": {"txn_id": "67dd216b4918a136984725a5", "account_id": "67ea81593c0d8b1f8a68785d", "amount": 6883, "type": "DEBIT", "txn_timestamp": "**********", "mode": "UPI", "narration": "Food Order", "raw_txn_id": "a4c1afbb-a860-4757-b41d-b2e8da86633b", "favorite": true, "tag": {"category_collection": "global", "category_id": "62ea81593c0d8b2f8a62785d", "subcategory_id": "62ea81593c0d8b2f8b68785d"}, "merchant": {"user_id": "", "merchant_id": "62ea81593c0d8b2f8a68785d", "merchant_name": "Zomato India Pvt. Ltd."}, "exclude_cash_flow": false, "fip_id": "", "user_notes": "testtt Notes ", "cash_flow_period": {"month": 3, "year": 2025}, "documents_count": 2, "recurrent_group_id": "group_1_id"}}, {"recurrent_txn_type": "RECURRENT_TXN_TYPE_UPCOMING", "txn_card": {"txn_id": "", "account_id": "67ea81593c0d8b1f8a68785d", "amount": 6883, "type": "DEBIT", "txn_timestamp": "**********", "recurrent_group_id": "group_1_id"}}, {"recurrent_txn_type": "RECURRENT_TXN_TYPE_MISSED", "txn_card": {"txn_id": "", "account_id": "67ea81593c0d8b1f8a68785d", "amount": 6883, "type": "DEBIT", "txn_timestamp": "**********", "recurrent_group_id": "group_1_id"}}]}, "code": 0}]