import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { CategoryFilter, debitCategories } from "@repo/ui";

const meta: Meta<typeof CategoryFilter> = {
  title: "Filters/CategoryFilter",
  component: CategoryFilter,
  tags: ["autodocs"],
};
export default meta;

type Story = StoryObj<typeof CategoryFilter>;

export const Interactive: Story = {
  render: () => {
    return (
      <div className="bg-white border rounded-md p-4 shadow-lg w-full sm:w-96 md:w-[28rem]">
        <CategoryFilter
          categories={debitCategories}
          selectedCategories={{}}
          setSelectedCategories={() => {}}
        />
      </div>
    );
  },
};
