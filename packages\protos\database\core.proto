syntax = "proto3";
package database;

import "database/custom_type.proto";

// flag:is-collection=true;
message User {
  ObjectId _id = 1;
  string phone_number = 2;
  string pan = 3;
  string name = 4;
  string address = 5;
  string date_of_birth = 6;
  repeated string alternate_phone_numbers = 7;
  string family_id = 8;
  ObjectId group_id = 9;
  bool is_primary = 10;
  int64 last_login_time = 11;
  int64 created_at = 12;
  int64 updated_at = 13;
  string nickname = 14; // Nickname provided by primary user. This is different from holder name which is received during FI fetch
  string email = 15;
  UserRelation relation = 16; // Relation to the primary user. 
}

// flag:is-collection=true;
message UserGroup {
  ObjectId _id = 1;
  string name = 2;
  string family_id = 3;
  int64 created_at = 101;
  int64 updated_at = 102;
}


enum ConsentStatus{
  CONSENT_STATUS_UNSPECIFIED = 0;
  CONSENT_STATUS_PENDING = 1;
  CONSENT_STATUS_ACTIVE = 2;
  CONSENT_STATUS_REVOKED = 3;
  CONSENT_STATUS_EXPIRED = 4;
  CONSENT_STATUS_REJECTED = 5;
}

// flag:is-collection=true;
message Consent {
  ObjectId _id = 1;
  string parent_phone_number = 2;
  string consent_phone_number = 3;
  string consent_handle = 4;
  string consent_id = 5;
  repeated ConsentedAccount accounts = 6;
  string customer_id = 7;
  FiType fi_type = 8;
  int64 consent_start = 9;
  int64 consent_expiry = 10;
  int64 data_range_from = 11;
  int64 data_range_to =  12;
  bool is_data_processed_once = 13;
  int64 created_at =  14;
  int64 updated_at = 15;
  string session_id = 16;
  int64 data_processed_checkpoint_timestamp = 17;
  string consent_request_id = 18;
}

// flag:is-collection=true;
message ConsentedAccount {
  string fip_id = 1;
  string linked_acc_ref = 2;
  string masked_acc_number = 3;
  bool is_deleted = 4; // Account deleted by user before FI fetch
}

// flag:is-collection=true;
message FipDetails {
  ObjectId _id = 1;
  string fip_id = 2;
  string fip_name = 3;
  repeated FiType fi_types = 4;
  string entity_icon_uri = 5;
  string entity_logo_uri = 6;
  string entity_logo_with_name_uri = 7;
  string code = 8; //TODO: Confirm type as value null in Sandbox
  string enable = 9;
  int32 otp_length = 10;
  int64 created_at = 11;
  int64 updated_at = 12;
}

enum FiType {
  UNSPECIFIED = 0;
  DEPOSIT = 1;
  EQUITY = 2;
  MUTUAL_FUND = 3;
}

// flag:is-collection=true;
message UserAccount {
  ObjectId _id = 1;
  ObjectId user_id = 2;
  FiType fi_type = 3;
  string family_id = 4;
  string consent_id = 5;
  string linked_acc_ref = 6;
  ObjectId account_id = 7;
  int64 created_at = 8;
  int64 updated_at = 9;
}

// flag:is-collection=true;
message DeletedAccountLog {
  ObjectId _id = 1;
  FiType fi_type = 2;
  ObjectId account_id = 3;
  int64 created_at = 4;
}

enum UserRelation {
  USER_RELATION_UNSPECIFIED = 0;
  USER_RELATION_SELF = 1;
  USER_RELATION_WIFE = 2;
  USER_RELATION_SPOUSE = 3;
  USER_RELATION_SON = 4;
  USER_RELATION_DAUGHTER = 5;
  USER_RELATION_SIBLING = 6;
  USER_RELATION_PARENT = 7;
  USER_RELATION_GRAND_PARENTS = 8;
}