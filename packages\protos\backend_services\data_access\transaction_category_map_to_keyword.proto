syntax = "proto3";
package backend_services.data_access;

service TransactionCategoryMapToKeyword {
    rpc GetAllTxnsKeywordsAndTheirCategories(GetAllTxnsKeywordsAndTheirCategoriesRequest) returns (GetAllTxnsKeywordsAndTheirCategoriesResponse) {};
    rpc UpsertTxnsKeywordCategory(UpsertTxnsKeywordCategoryRequest) returns (UpsertTxnsKeywordCategoryResponse) {};
    rpc UpdateTxnsKeywordMatchProcess(UpdateTxnsKeywordMatchProcessRequest) returns (UpdateTxnsKeywordMatchProcessResponse) {};
    rpc GetTxnCategoryIdsFromKeywords(GetTxnCategoryIdsFromKeywordsRequest) returns (GetTxnCategoryIdsFromKeywordsResponse) {};
}


message GetAllTxnsKeywordsAndTheirCategoriesRequest {
	int32 page_number = 1;
	int32 page_size = 2;
	string last_id = 3;
}
message GetAllTxnsKeywordsAndTheirCategoriesResponse {
	message KeywordCategories {
		string keyword = 1; // Transaction Keyword
		string category = 2; // Transaction Category
		string subcategory = 3; // Transaction Subcategory
	}

	repeated KeywordCategories keywords_catgories = 1;
	string last_id = 903;
}


message UpsertTxnsKeywordCategoryRequest{
	message Keyword {
		string transaction_category_id = 1;
		string match_process = 2;
	}
	map<string, Keyword> keyword_txn_category_id_map = 1; // Map of keyword --> transaction category id
}
message UpsertTxnsKeywordCategoryResponse{}



message UpdateTxnsKeywordMatchProcessRequest{
	map<string, string> keyword_match_process_map = 1; // Map of keyword --> match process
}
message UpdateTxnsKeywordMatchProcessResponse{}



message GetTxnCategoryIdsFromKeywordsRequest{
	repeated string keywords = 1;
	string match_process = 2;
}
message GetTxnCategoryIdsFromKeywordsResponse{
	map<string, string> keyword_txn_category_id_map = 1;
}