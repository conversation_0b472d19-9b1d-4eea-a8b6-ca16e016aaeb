import { TransactionFilters, TransactionRequest } from "../types/transaction.types";

export function applyUserGroupFilters(filters: TransactionFilters, request: TransactionRequest) {
  if ('userGroups' in filters && filters.userGroups) {
    request.filter.userGroups = {};

    if (filters.userGroups.userIds?.length) {
      request.filter.userGroups.userIds = filters.userGroups.userIds;
    }

    if (filters.userGroups.userGroupIds?.length) {
      request.filter.userGroups.userGroupIds = filters.userGroups.userGroupIds;
    }
  }
}

export function applyAccountFilters(filters: TransactionFilters, request: TransactionRequest) {
  if ('accountFilters' in filters && filters.accountFilters?.accountIds?.length) {
    request.filter.accountFilters = {
      accountIds: filters.accountFilters.accountIds.map((id: string) => id),
    };
  }
}

export function applyTimeRangeFilter(filters: TransactionFilters, request: TransactionRequest) {
  if (
    filters.timeRange &&
    (filters.timeRange.fromTime > 0 || filters.timeRange.toTime > 0)
  ) {
    request.filter.txnFilters = request.filter.txnFilters || {};
    request.filter.txnFilters.timeRange = {
      fromTime:
        filters.timeRange.fromTime > 0
          ? String(filters.timeRange.fromTime)
          : undefined,
      toTime:
        filters.timeRange.toTime > 0
          ? String(filters.timeRange.toTime)
          : undefined,
    };

    if (!request.filter.txnFilters.timeRange.fromTime) {
      delete request.filter.txnFilters.timeRange.fromTime;
    }
    if (!request.filter.txnFilters.timeRange.toTime) {
      delete request.filter.txnFilters.timeRange.toTime;
    }
    if (
      !request.filter.txnFilters.timeRange.fromTime &&
      !request.filter.txnFilters.timeRange.toTime
    ) {
      delete request.filter.txnFilters.timeRange;
    }
  }
}

export function applyAmountRangeFilter(filters: TransactionFilters, request: TransactionRequest) {
  if (
    filters.amountRange &&
    ((typeof filters.amountRange.minAmount === "number" &&
      filters.amountRange.minAmount > 0) ||
      (typeof filters.amountRange.maxAmount === "number" &&
        filters.amountRange.maxAmount > 0))
  ) {
    request.filter.txnFilters = request.filter.txnFilters || {};
    request.filter.txnFilters.amountRange = {};

    if (
      typeof filters.amountRange.minAmount === "number" &&
      filters.amountRange.minAmount > 0
    ) {
      request.filter.txnFilters.amountRange.minAmount = String(
        filters.amountRange.minAmount,
      );
    }

    if (
      typeof filters.amountRange.maxAmount === "number" &&
      filters.amountRange.maxAmount > 0
    ) {
      request.filter.txnFilters.amountRange.maxAmount = String(
        filters.amountRange.maxAmount,
      );
    }
  }
}

export function applyBookmarkFilters(filters: TransactionFilters, request: TransactionRequest) {
  if (filters.bookmarkOptions) {
    request.filter.txnFilters = request.filter.txnFilters || {};
    const { showFavorites, excludedFromCashflow, withNotes } = filters.bookmarkOptions;

    if (showFavorites) {
      request.filter.txnFilters.favorited = { favorited: true };
    }

    if (excludedFromCashflow) {
      request.filter.txnFilters.excludeCashFlow = {
        excludeCashFlow: true,
      };
    }

    if (withNotes) {
      request.filter.txnFilters.hasUserNotes = { hasUserNotes: true };
    }
  }
}

export function applyTransactionTypeFilter(filters: TransactionFilters, request: TransactionRequest) {
  if (filters.transactionType && filters.transactionType !== "all") {
    request.filter.txnFilters = request.filter.txnFilters || {};
    const txnType = filters.transactionType.toLowerCase() === "incoming" ? "CREDIT" : "DEBIT";
    request.filter.txnFilters.txnType = { txnType: txnType };
  }
}

export function applyTagStatusFilter(filters: TransactionFilters, request: TransactionRequest) {
  if (filters.tagStatus && filters.tagStatus.toLowerCase() !== "all") {
    request.filter.txnFilters = request.filter.txnFilters || {};
    const isUntagged = filters.tagStatus.toLowerCase() === "untagged";
    request.filter.txnFilters.untagged = { untagged: isUntagged };
  }
}

export function applyCategoriesFilter(filters: TransactionFilters, request: TransactionRequest) {
  if (filters.categories?.length) {
    request.filter.txnFilters = request.filter.txnFilters || {};
    request.filter.txnFilters.category = filters.categories.map((category) => {
      if ('categoryId' in category) {
        return {
          categoryCollection: category.categoryCollection || "global",
          categoryId: category.categoryId,
          subcategoryId: category.subcategoryId || "",
        };
      }
      return {
        categoryCollection: "global",
        categoryId: "",
        subcategoryId: "",
      };
    });
  }
}

export function applyTransactionFilters(filters: TransactionFilters): TransactionRequest {
  const request: TransactionRequest = {
    filter: {},
    paginationParams: {
      pageSize: 0,  // These will be set by the caller
      pageNumber: 0,
    },
  };

  if (filters) {
    applyUserGroupFilters(filters, request);
    applyAccountFilters(filters, request);
    request.filter.txnFilters = {};
    applyTimeRangeFilter(filters, request);
    applyAmountRangeFilter(filters, request);
    applyBookmarkFilters(filters, request);
    applyTransactionTypeFilter(filters, request);
    applyTagStatusFilter(filters, request);
    applyCategoriesFilter(filters, request);
  }

  return request;
}