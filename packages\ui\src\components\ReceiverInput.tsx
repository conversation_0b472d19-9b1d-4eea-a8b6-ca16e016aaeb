import { useState, useRef, useEffect } from "react";
import { Input } from "./ui/input";

export interface ReceiverInputProps {
  defaultValue?: string;
  onChangeReceiver: (receiver: string) => void;
}

export function ReceiverInput({
  defaultValue,
  onChangeReceiver,
}: ReceiverInputProps) {
  const [value, setValue] = useState(defaultValue);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    // console.log('HG changing value', value);
    setValue(defaultValue);
  }, [defaultValue]);

  return (
    <Input
      value={value}
      placeholder="Add Receiver"
      type="text"
      ref={inputRef}
      outerClassName="p-0 border-none shadow-none focus-within:ring-0"
      className="min-w-20"
      onClick={(event) => event.stopPropagation()}
      onChange={(event) => {
        setValue(event.target.value);
      }}
      onBlur={() => {
        onChangeReceiver(value ?? "");
      }}
      onKeyDown={(event) => {
        if (event.key === "Enter" || event.key === "Escape") {
          event.preventDefault();
          inputRef.current?.blur();
        }
      }}
    />
  );
}
