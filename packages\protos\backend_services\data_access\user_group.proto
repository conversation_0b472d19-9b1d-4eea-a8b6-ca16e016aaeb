syntax = "proto3";
package backend_services.data_access;

import "database/core.proto";

service UserGroups {
    rpc CreateGroup(CreateGroupRequest) returns (CreateGroupResponse) {};
    rpc GetFamilyGroupsByIds(GetFamilyGroupsByIdsRequest) returns (GetFamilyGroupsByIdsResponse) {};
    rpc CountGroupInFamily(CountGroupInFamilyRequest) returns (CountGroupInFamilyResponse) {};
    rpc DeleteGroup(DeleteGroupRequest) returns (DeleteGroupResponse) {};
    rpc UpdateGroupName(UpdateGroupNameRequest) returns (UpdateGroupNameResponse) {};
}

message CreateGroupRequest {
  database.UserGroup record = 1;
}
message CreateGroupResponse {
  string group_id = 1;
}

message GetFamilyGroupsByIdsRequest {
  repeated string group_ids = 1;
  string family_id = 2;
}
message GetFamilyGroupsByIdsResponse {
  repeated database.UserGroup groups = 1;
}

message CountGroupInFamilyRequest {
  string group_id = 1;
  string family_id = 2;
}

message CountGroupInFamilyResponse {
  int64 count = 1;
}

message DeleteGroupRequest {
  string group_id = 1;
}
message DeleteGroupResponse {}

message UpdateGroupNameRequest {
  string group_id = 1;
  string name = 2;
}
message UpdateGroupNameResponse {}