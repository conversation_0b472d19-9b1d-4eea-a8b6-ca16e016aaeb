import * as React from "react";
import type { SVGProps } from "react";
const SvgPersonalAsset = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="currentColor"
    viewBox="0 0 36 46"
    {...props}
  >
    <g clipPath="url(#personal_asset_svg__a)">
      <path d="m16.875 22.84-6.75 2.157V36.44l6.75-2.157zM9 22.945l5.227-1.67-7.115-2.272-5.226 1.67zM19.125 10.047v9.664L27 17.195V7.53zM16.875 19.711v-9.664L9 7.53v9.665zM18 7.995l7.115-2.273L18 3.45l-7.114 2.272zM28.125 24.996v11.445L36 33.925V22.48zM7.875 24.996 0 22.48v11.445l7.875 2.516zM28.888 19.003l-7.115 2.272L27 22.945l7.115-2.273zM25.875 24.997l-6.75-2.158v11.445l6.75 2.157z" />
    </g>
    <defs>
      <clipPath id="personal_asset_svg__a">
        <path fill="#fff" d="M0 0h36v46H0z" />
      </clipPath>
    </defs>
  </svg>
);
export default SvgPersonalAsset;
