syntax = "proto3";
package backend_services.data_access;
import "database/fi_mutual_funds.proto";

service FiMutualFundsSummary {
    rpc UpdateFiMutualFundsAccountsSummaryAndProfile(UpdateFiMutualFundsAccountsSummaryAndProfileRequest) returns (UpdateFiMutualFundsAccountsSummaryAndProfileResponse) {};
	rpc GetMutualFundsAccountsSummary(GetMutualFundsAccountsSummaryRequest) returns (GetMutualFundsAccountsSummaryResponse) {};
	rpc InsertFiMutualFundsAccountsSummary(InsertFiMutualFundsAccountsSummaryRequest) returns (InsertFiMutualFundsAccountsSummaryResponse) {}; 
	rpc CopyFiMutualFundsAccountsSummaryToDeleteCol(CopyFiMutualFundsAccountsSummaryToDeleteColRequest) returns (CopyFiMutualFundsAccountsSummaryToDeleteColResponse) {};
	rpc DeleteFiMutualFundsAccountsSummary(DeleteFiMutualFundsAccountsSummaryRequest) returns (DeleteFiMutualFundsAccountsSummaryResponse) {};
	rpc UpsertFiMutualFundsAccountSummary(UpsertFiMutualFundsAccountSummaryRequest) returns (UpsertFiMutualFundsAccountSummaryResponse) {};
	rpc GetMutualFundsAccountSummariesByLinkedAccRef(GetMutualFundsAccountSummariesByLinkedAccRefRequest) returns (GetMutualFundsAccountSummariesByLinkedAccRefResponse) {};
}


message UpdateFiMutualFundsAccountsSummaryAndProfileRequest{
	repeated database.FiMutualFundsAccountSummary records = 1;
}
message UpdateFiMutualFundsAccountsSummaryAndProfileResponse{}


message GetMutualFundsAccountsSummaryRequest{
	repeated string ids = 1;
}
message GetMutualFundsAccountsSummaryResponse{
	repeated database.FiMutualFundsAccountSummary records = 1;
}


message InsertFiMutualFundsAccountsSummaryRequest{
	repeated database.FiMutualFundsAccountSummary accounts_summary = 1;
}
message InsertFiMutualFundsAccountsSummaryResponse{
	repeated string ids = 1;
}

message CopyFiMutualFundsAccountsSummaryToDeleteColRequest{
	repeated string account_ids = 1;
}
message CopyFiMutualFundsAccountsSummaryToDeleteColResponse {}

message DeleteFiMutualFundsAccountsSummaryRequest{
	repeated string account_ids = 1;
}
message DeleteFiMutualFundsAccountsSummaryResponse{}

message UpsertFiMutualFundsAccountSummaryRequest {
	repeated database.FiMutualFundsAccountSummary summaries = 1;
}

message UpsertFiMutualFundsAccountSummaryResponse {
}

message GetMutualFundsAccountSummariesByLinkedAccRefRequest {
	repeated string linked_acc_refs = 1;
}

message GetMutualFundsAccountSummariesByLinkedAccRefResponse {
	repeated database.FiMutualFundsAccountSummary summaries = 1;
}