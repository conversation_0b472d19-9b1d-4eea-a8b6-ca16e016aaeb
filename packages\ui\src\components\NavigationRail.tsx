import { ComponentType, SVGProps } from "react";
import { cn } from "../lib/utils";
import { UserCircle } from "lucide-react";

export interface NavigationRailItem {
  title: string;
  url: string;
  icon: ComponentType<SVGProps<SVGSVGElement>>;
}

export interface NavigationRailProps {
  items: NavigationRailItem[];
  activeLink?: string;
}

export function NavigationRail({ items, activeLink }: NavigationRailProps) {
  return (
    <nav className="w-fit h-full px-4 py-3 bg-purple-50 flex flex-col justify-between items-center">
      <ul className="flex flex-col gap-4">
        {items.map((item) => {
          const isActive = item.title === activeLink;

          return (
            <li key={item.title}>
              <a
                href={item.url}
                className={cn(
                  "group flex flex-col items-center text-xs leading-none gap-1",
                  isActive && "font-semibold",
                )}
              >
                <span
                  className={cn(
                    "py-1.5 px-2 rounded-md",
                    !isActive && "group-hover:bg-primary/10",
                    isActive && "bg-primary/30",
                  )}
                >
                  <item.icon className="size-6" />
                </span>
                {item.title}
              </a>
            </li>
          );
        })}
      </ul>

      <a href="#" className="px-2 py-1.5">
        <UserCircle className="size-8" aria-label="Profile" />
      </a>
    </nav>
  );
}
