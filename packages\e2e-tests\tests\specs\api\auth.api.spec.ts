import { test, expect } from "@playwright/test";

// Skip all API tests for now until the API endpoints are implemented
test.describe.skip("Authentication API Tests", () => {
  test("should return 401 for unauthenticated requests", async ({
    request,
  }) => {
    // Make a request to a protected endpoint without authentication
    const response = await request.get("/api/protected");

    // Check that the response status is 401 Unauthorized
    expect(response.status()).toBe(401);

    // Check the response body
    const body = await response.json();
    expect(body.error).toBeTruthy();
  });

  test("should validate phone number format", async ({ request }) => {
    // Make a request with an invalid phone number
    const response = await request.post("/api/auth/send-otp", {
      data: {
        phoneNumber: "invalid-phone",
      },
    });

    // Check that the response status is 400 Bad Request
    expect(response.status()).toBe(400);

    // Check the response body
    const body = await response.json();
    expect(body.error).toBeTruthy();
  });

  test("should handle OTP verification", async ({ request }) => {
    // First, send OTP to a test phone number
    const sendOtpResponse = await request.post("/api/auth/send-otp", {
      data: {
        phoneNumber: "8469658694",
      },
    });

    // Check that the send OTP request was successful
    expect(sendOtpResponse.status()).toBe(200);

    // Now verify the OTP
    const verifyOtpResponse = await request.post("/api/auth/verify-otp", {
      data: {
        phoneNumber: "8469658694",
        otp: "123456", // Test OTP
      },
    });

    // Check that the verify OTP request was successful
    expect(verifyOtpResponse.status()).toBe(200);

    // Check that the response contains a session token
    const body = await verifyOtpResponse.json();
    expect(body.token).toBeTruthy();
  });
});
