import { Badge } from "@repo/ui";
import { FoodDrink } from "@repo/ui/icons/categories";
import type { Meta, StoryObj } from "@storybook/react";

const meta: Meta<typeof Badge> = {
  title: "Components/Badge",
  component: Badge,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
  },
  args: {
    variant: "default",
    children: "Default Badge",
  },
};

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {};

export const Selected: Story = {
  args: {
    children: "Selected Badge",
    selected: true,
  },
};

export const Icon: Story = {
  args: {
    children: "Food & Drinks",
    selected: true,
    startIcon: <FoodDrink width="24px" height="25px" />,
  },
};
