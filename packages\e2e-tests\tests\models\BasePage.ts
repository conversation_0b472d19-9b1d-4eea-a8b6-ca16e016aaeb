import { <PERSON>, Locator, expect, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@playwright/test";
import { Logger } from "../utils/logger";

/**
 * Base Page Object Model class that all page objects should extend
 */
export class BasePage {
  readonly page: Page;
  readonly url: string;
  readonly logger: Logger;

  /**
   * @param page - Playwright page instance
   * @param url - URL path for this page (without base URL)
   */
  constructor(page: Page, url: string = "/") {
    this.page = page;
    this.url = url;
    this.logger = new Logger(`[${this.constructor.name}]`);
  }

  /**
   * Navigate to this page
   * @param options - Navigation options
   */
  async goto(options?: {
    timeout?: number;
    waitUntil?: "load" | "domcontentloaded" | "networkidle";
  }) {
    this.logger.info(`Navigating to ${this.url}`);
    await this.page.goto(this.url, options);
  }

  /**
   * Wait for page to be loaded
   * Override in subclasses to add specific loading conditions
   */
  async waitForPageLoad() {
    this.logger.info("Waiting for page to load");
    await this.page.waitForLoadState("domcontentloaded");
    await this.page.waitForLoadState("networkidle");
  }

  /**
   * Get page title
   */
  async getTitle(): Promise<string> {
    const title = await this.page.title();
    this.logger.info(`Page title: ${title}`);
    return title;
  }

  /**
   * Take a screenshot of the current page
   * @param name - Name of the screenshot file
   * @param options - Screenshot options
   */
  async takeScreenshot(
    name: string,
    options?: { fullPage?: boolean; path?: string },
  ): Promise<Buffer> {
    const path = options?.path || `./test-results/screenshots/${name}.png`;
    this.logger.info(`Taking screenshot: ${path}`);
    return await this.page.screenshot({
      path,
      fullPage: options?.fullPage ?? true,
    });
  }

  /**
   * Take a screenshot of a specific element
   * @param locator - Element locator
   * @param name - Name of the screenshot file
   */
  async takeElementScreenshot(locator: Locator, name: string): Promise<Buffer> {
    this.logger.info(`Taking element screenshot: ${name}`);
    return await locator.screenshot({
      path: `./test-results/screenshots/${name}.png`,
    });
  }

  /**
   * Check if element is visible
   * @param locator - Element locator
   */
  async isVisible(locator: Locator): Promise<boolean> {
    const isVisible = await locator.isVisible();
    this.logger.debug(`Element visibility: ${isVisible}`);
    return isVisible;
  }

  /**
   * Wait for element to be visible
   * @param locator - Element locator
   * @param timeout - Timeout in milliseconds
   */
  async waitForVisible(locator: Locator, timeout?: number): Promise<void> {
    this.logger.debug(
      `Waiting for element to be visible${timeout ? ` (timeout: ${timeout}ms)` : ""}`,
    );
    await locator.waitFor({ state: "visible", timeout });
  }

  /**
   * Wait for element to be hidden
   * @param locator - Element locator
   * @param timeout - Timeout in milliseconds
   */
  async waitForHidden(locator: Locator, timeout?: number): Promise<void> {
    this.logger.debug(
      `Waiting for element to be hidden${timeout ? ` (timeout: ${timeout}ms)` : ""}`,
    );
    await locator.waitFor({ state: "hidden", timeout });
  }

  /**
   * Click on element
   * @param locator - Element locator
   * @param options - Click options
   */
  async click(
    locator: Locator,
    options?: { force?: boolean; timeout?: number },
  ): Promise<void> {
    this.logger.debug("Clicking element");
    await locator.click(options);
  }

  /**
   * Double click on element
   * @param locator - Element locator
   */
  async dblClick(locator: Locator): Promise<void> {
    this.logger.debug("Double-clicking element");
    await locator.dblclick();
  }

  /**
   * Right click on element
   * @param locator - Element locator
   */
  async rightClick(locator: Locator): Promise<void> {
    this.logger.debug("Right-clicking element");
    await locator.click({ button: "right" });
  }

  /**
   * Hover over element
   * @param locator - Element locator
   */
  async hover(locator: Locator): Promise<void> {
    this.logger.debug("Hovering over element");
    await locator.hover();
  }

  /**
   * Fill input field
   * @param locator - Input field locator
   * @param value - Value to fill
   */
  async fill(locator: Locator, value: string): Promise<void> {
    this.logger.debug(`Filling input with value: ${value}`);
    await locator.fill(value);
  }

  /**
   * Type text into input field
   * @param locator - Input field locator
   * @param text - Text to type
   * @param options - Type options
   */
  async type(
    locator: Locator,
    text: string,
    options?: { delay?: number },
  ): Promise<void> {
    this.logger.debug(`Typing text: ${text}`);
    await locator.type(text, options);
  }

  /**
   * Clear input field
   * @param locator - Input field locator
   */
  async clearInput(locator: Locator): Promise<void> {
    this.logger.debug("Clearing input field");
    await locator.clear();
  }

  /**
   * Get text from element
   * @param locator - Element locator
   */
  async getText(locator: Locator): Promise<string> {
    const text = (await locator.textContent()) || "";
    this.logger.debug(`Element text: ${text}`);
    return text;
  }

  /**
   * Get input value
   * @param locator - Input field locator
   */
  async getValue(locator: Locator): Promise<string> {
    const value = await locator.inputValue();
    this.logger.debug(`Input value: ${value}`);
    return value;
  }

  /**
   * Check if element has class
   * @param locator - Element locator
   * @param className - Class name to check
   */
  async hasClass(locator: Locator, className: string): Promise<boolean> {
    const classAttribute = await locator.getAttribute("class");
    const hasClass =
      classAttribute !== null && classAttribute.split(" ").includes(className);
    this.logger.debug(`Element has class '${className}': ${hasClass}`);
    return hasClass;
  }

  /**
   * Get element attribute
   * @param locator - Element locator
   * @param attributeName - Attribute name
   */
  async getAttribute(
    locator: Locator,
    attributeName: string,
  ): Promise<string | null> {
    const attribute = await locator.getAttribute(attributeName);
    this.logger.debug(`Element attribute '${attributeName}': ${attribute}`);
    return attribute;
  }

  /**
   * Check if element is enabled
   * @param locator - Element locator
   */
  async isEnabled(locator: Locator): Promise<boolean> {
    const isEnabled = await locator.isEnabled();
    this.logger.debug(`Element is enabled: ${isEnabled}`);
    return isEnabled;
  }

  /**
   * Check if element is disabled
   * @param locator - Element locator
   */
  async isDisabled(locator: Locator): Promise<boolean> {
    const isDisabled = await locator.isDisabled();
    this.logger.debug(`Element is disabled: ${isDisabled}`);
    return isDisabled;
  }

  /**
   * Check if element is checked (for checkboxes and radio buttons)
   * @param locator - Element locator
   */
  async isChecked(locator: Locator): Promise<boolean> {
    const isChecked = await locator.isChecked();
    this.logger.debug(`Element is checked: ${isChecked}`);
    return isChecked;
  }

  /**
   * Select option from dropdown
   * @param locator - Select element locator
   * @param option - Option to select (value, label, or index)
   */
  async selectOption(
    locator: Locator,
    option: string | { value?: string; label?: string; index?: number },
  ): Promise<void> {
    this.logger.debug(`Selecting option: ${JSON.stringify(option)}`);
    await locator.selectOption(option);
  }

  /**
   * Press keyboard key
   * @param key - Key to press
   */
  async pressKey(key: string): Promise<void> {
    this.logger.debug(`Pressing key: ${key}`);
    await this.page.keyboard.press(key);
  }

  /**
   * Wait for a specific condition
   * @param condition - Condition function that returns a promise resolving to boolean
   * @param options - Wait options
   */
  async waitForCondition(
    condition: () => Promise<boolean>,
    options?: { timeout?: number; message?: string },
  ): Promise<void> {
    const message = options?.message || "Waiting for condition";
    const timeout = options?.timeout || 30000;
    this.logger.debug(`${message} (timeout: ${timeout}ms)`);

    const startTime = Date.now();
    while (Date.now() - startTime < timeout) {
      if (await condition()) {
        return;
      }
      await this.page.waitForTimeout(100);
    }
    throw new Error(
      `Timeout of ${timeout}ms exceeded while waiting for condition: ${message}`,
    );
  }

  /**
   * Execute JavaScript in the browser context
   * @param script - JavaScript to execute
   * @param args - Arguments to pass to the script
   */
  async evaluate<T>(
    script: string | ((arg1: any) => T),
    arg?: any,
  ): Promise<T> {
    this.logger.debug("Executing JavaScript in browser context");
    return await this.page.evaluate(script, arg);
  }

  /**
   * Wait for navigation to complete
   * @param options - Navigation options
   */
  async waitForNavigation(options?: {
    timeout?: number;
    waitUntil?: "load" | "domcontentloaded" | "networkidle";
  }): Promise<void> {
    this.logger.info("Waiting for navigation to complete");
    await this.page.waitForNavigation(options);
  }

  /**
   * Wait for a specific URL
   * @param urlOrPredicate - URL string or predicate function
   * @param options - Wait options
   */
  async waitForURL(
    urlOrPredicate: string | RegExp | ((url: string) => boolean),
    options?: { timeout?: number },
  ): Promise<void> {
    this.logger.info(`Waiting for URL: ${urlOrPredicate.toString()}`);
    await this.page.waitForURL(urlOrPredicate, options);
  }

  /**
   * Reload the page
   */
  async reload(): Promise<void> {
    this.logger.info("Reloading page");
    await this.page.reload();
  }

  /**
   * Go back in browser history
   */
  async goBack(): Promise<void> {
    this.logger.info("Going back in browser history");
    await this.page.goBack();
  }

  /**
   * Go forward in browser history
   */
  async goForward(): Promise<void> {
    this.logger.info("Going forward in browser history");
    await this.page.goForward();
  }

  /**
   * Check if element exists
   * @param locator - Element locator
   */
  async exists(locator: Locator): Promise<boolean> {
    const count = await locator.count();
    const exists = count > 0;
    this.logger.debug(`Element exists: ${exists}`);
    return exists;
  }

  /**
   * Wait for element to exist
   * @param locator - Element locator
   * @param timeout - Timeout in milliseconds
   */
  async waitForExists(locator: Locator, timeout?: number): Promise<void> {
    this.logger.debug(
      `Waiting for element to exist${timeout ? ` (timeout: ${timeout}ms)` : ""}`,
    );
    await expect(locator).toHaveCount(1, { timeout });
  }

  /**
   * Wait for element to not exist
   * @param locator - Element locator
   * @param timeout - Timeout in milliseconds
   */
  async waitForNotExists(locator: Locator, timeout?: number): Promise<void> {
    this.logger.debug(
      `Waiting for element to not exist${timeout ? ` (timeout: ${timeout}ms)` : ""}`,
    );
    await expect(locator).toHaveCount(0, { timeout });
  }
}
