import { test as base, expect } from "@playwright/test";
import { LoginPage } from "../models/LoginPage";

/**
 * Default test credentials
 */
export const DEFAULT_CREDENTIALS = {
  phoneNumber: "8469658694",
  otp: "123456",
};

/**
 * Custom fixtures for our tests
 */
type CustomFixtures = {
  loginPage: LoginPage;
};

/**
 * Extended test with custom fixtures
 */
export const test = base.extend<CustomFixtures>({
  // Login page fixture
  loginPage: async ({ page }, use) => {
    // Create login page object
    const loginPage = new LoginPage(page);

    // Navigate to the login page
    await loginPage.goto();

    // Wait for page to load
    await loginPage.waitForPageLoad();

    // Use the fixture
    await use(loginPage);
  },
});

// Export expect for convenience
export { expect };
