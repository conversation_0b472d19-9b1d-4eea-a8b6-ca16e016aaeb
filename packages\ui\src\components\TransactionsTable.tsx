"use client";

import { format } from "date-fns";
import {
  Table,
  TableRow,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
} from "./ui/table";
import { categoryIconMap } from "./AddEditTag";
import { ReceiverInput } from "./ReceiverInput";
import { cn } from "../lib/utils";
import { displayBank, currencyFormatter } from "../lib/helper";
import SvgExcludedCashFlow from "../icons/excluded-cash-flow";
import SvgSavedTransaction from "../icons/saved-transaction";
import { Badge } from "./ui/badge";
import { Button } from "./ui/button";
import { Misc } from "../icons/categories";

export const TransactionActionTypes = {
  EXCLUDE_CASH_FLOW: "EXCLUDE_CASH_FLOW",
  SAVE_TRANSACTION: "SAVE_TRANSACTION",
  NOTES: "NOTES",
} as const;

export type TransactionActionType =
  (typeof TransactionActionTypes)[keyof typeof TransactionActionTypes];

export interface BasePayload {
  transactionId: string;
}
export interface SaveTxnPayload extends BasePayload {
  isSaved: boolean;
}
export interface ExcludecashFlowPayload extends BasePayload {
  isExcludedFromCashFlow: boolean;
}
export interface NotesPayload extends BasePayload {
  notes: string;
}

export type TransactionActionPayloadMap = {
  [TransactionActionTypes.SAVE_TRANSACTION]: SaveTxnPayload;
  [TransactionActionTypes.EXCLUDE_CASH_FLOW]: ExcludecashFlowPayload;
  [TransactionActionTypes.NOTES]: NotesPayload;
};

export type TransactionActionFunction = <T extends TransactionActionType>(
  action: T,
  payload: TransactionActionPayloadMap[T],
) => void;

// Interface (camelCase for use throughout the application)
export interface TransactionCategory {
  categoryCollection: string;
  categoryId: string;
  subcategoryId: string;
}
export interface TransactionCard {
  txnId: string;
  txnMode: string;
  txnType: "CREDIT" | "DEBIT";
  tag: TransactionCategory | null ;
  merchantName: string;
  account?: Account;
  amount: number;
  bankName: string;
  isSaved?: boolean;
  isExcludedCashflow?: boolean;
  narration: string;
  documentsCount: number;
  notes: string;
  cashFlowPeriod: {
    month: number;
    year: number;
  };
}

export enum TransactionTypeEnum {
  DEBIT = "DEBIT",
  CREDIT = "CREDIT",
}
export interface Account {
  accountId: string;
  linkedAccRef: string;
  maskedAccNumber: string;
  fipId: string;
  holderName: string;
  branch: string;
  fiType: string;
  userId: string;
  dataSyncedAt: string;
}

export interface Subcategory {
  id: string;
  name: string;
  sortKey: number;
  createdAt: number;
}
export interface Category {
  id: string;
  name: string;
  subCategories: Subcategory[];
  sortKey: number;
  createdAt: number;
  sfSymbol?: string;
  iconUrl?: string;
  cashflowGroup?: number;
}

export interface TransactionDocument {
  name: string;
  objectName: string;
  thumbnail: string;
  signedUrl: string;
  contentType: string;
}

// Frontend interface (camelCase for use in the UI)
export interface FetchDocuments {
  documents: TransactionDocument[];
}

// For backward compatibility
export type TransactionDocumentCamel = TransactionDocument;
export type FetchDocumentsCamel = FetchDocuments;
export interface TransactionGroup {
  date: Date;
  transactions: TransactionCard[];
}

export interface TransactionCardWithDate extends TransactionCard {
  date: Date;
}

interface TransactionsTableProps {
  transactionGroups: TransactionGroup[];
  categories: Category[];
  openTransactionId: string | undefined;
  onTransactionClick: (transaction: TransactionCardWithDate) => void;
  onChangeReceiver: (transactionId: string, receiver: string) => void;
  onAddEditTagClick: (transaction: TransactionCardWithDate) => void;
  lastItemRef: (node: HTMLTableRowElement) => void;
  hasMore: boolean;
}

export const BankIcon = ({
  bankName,
  className = "",
}: {
  bankName: string;
  className?: string;
}) => {
  const Comp = displayBank(bankName);
  if (Comp) return <Comp className={className} />;
};

const TransactionsTable = ({
  transactionGroups,
  categories,
  openTransactionId,
  onTransactionClick,
  onChangeReceiver,
  onAddEditTagClick,
  lastItemRef,
  hasMore,
}: TransactionsTableProps) => {
  function handleTransactionClick(transaction: TransactionCard, date: Date) {
    onTransactionClick({ ...transaction, date: date });
  }

  function handleAddEditTagClick(transaction: TransactionCard, date: Date) {
    onAddEditTagClick({ ...transaction, date: date });
  }

  return (
    <>
      <Table className="bg-background mb-10">
        <TableHeader>
          <TableRow className="sr-only">
            <TableHead>Bank</TableHead>
            <TableHead>User</TableHead>
            <TableHead>Entity</TableHead>
            <TableHead>Amount</TableHead>
            <TableHead>Category</TableHead>
            <TableHead>Extra Info</TableHead>
          </TableRow>
        </TableHeader>

        {transactionGroups.map((group, groupIndex) => (
          <TableBody key={groupIndex}>
            <TableRow className="hover:bg-transparent">
              <TableHead className="pt-6 pl-4" scope="rowgroup" colSpan={6}>
                <time
                  dateTime={group.date.toISOString()}
                  className="flex gap-1 items-baseline"
                >
                  <span className="text-xl font-semibold text-[#1B1B1B]">
                    {format(group.date, "dd")}
                  </span>
                  <span className="text-xs font-medium text-[#797878]">
                    {format(group.date, "MMM'' yy")}
                  </span>
                </time>
              </TableHead>
            </TableRow>
            {group.transactions.map((transaction, idx) => {
              const formattedAmount = currencyFormatter
                .format(transaction.amount)
                .split(".");
              const amountUnit = formattedAmount[0] + ".";
              const amountSubUnit = formattedAmount[1];
              const selectedCategory = categories.find(
                (item) => item.id === transaction?.tag?.categoryId
              );

              return (
                <TableRow
                  key={transaction.txnId}
                  ref={
                    groupIndex === transactionGroups.length - 1 &&
                    idx === group.transactions.length - 1
                      ? lastItemRef
                      : null
                  }
                  className={cn(
                    "bg-white hover:bg-[#FAF8FC]",
                    openTransactionId === transaction.txnId && "bg-[#FAF8FC]",
                  )}
                  onClick={() =>
                    handleTransactionClick(transaction, group.date)
                  }
                >
                  <TableCell className="pl-4 w-6">
                    <div className="flex items-center justify-start">
                      <BankIcon
                        bankName={transaction.bankName}
                        className="w-4"
                      />
                    </div>
                  </TableCell>
                  <TableCell className="px-3 text-base font-medium">
                    {transaction.account && transaction.account?.holderName ? (
                      transaction.account?.holderName
                    ) : (
                      <span className="text-sm text-disabled text-nowrap">
                        John Doe
                      </span>
                    )}
                  </TableCell>
                  <TableCell className="px-3 w-[1%]">
                    <div
                      className={cn(
                        "flex font-bold items-baseline justify-end",
                        transaction.txnType === TransactionTypeEnum.CREDIT &&
                          "text-[#4FA33F]",
                      )}
                    >
                      {transaction.txnType === TransactionTypeEnum.CREDIT && (
                        <span className="mr-1" aria-hidden={true}>
                          +
                        </span>
                      )}
                      <span className="text-sm">{amountUnit}</span>
                      <span
                        className={cn(
                          "text-xs font-normal",
                          transaction.txnType === TransactionTypeEnum.CREDIT
                            ? "text-[#6FBE5D]"
                            : "text-[#888888]",
                        )}
                      >
                        {amountSubUnit}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="px-3 pl-6">
                    <ReceiverInput
                      defaultValue={transaction.merchantName}
                      onChangeReceiver={(receiver) =>
                        onChangeReceiver(transaction.txnId, receiver)
                      }
                    />
                  </TableCell>
                  <TableCell
                    className="px-3 flex"
                    onClick={(event) => {
                      event.stopPropagation();
                      handleAddEditTagClick(transaction, group.date);
                    }}
                  >
                    {selectedCategory ? (
                      <Badge
                        variant="default"
                        className="cursor-pointer"
                        selected={false}
                      >
                        <div className="flex gap-2 items-center justify-center">
                          <span className="text-sm">
                            {categoryIconMap[selectedCategory.name] || <Misc />}
                          </span>
                          <span className="text-sm font-semibold text-[#AC80CC]">
                            {selectedCategory.name}
                          </span>
                        </div>
                      </Badge>
                    ) : (
                      <Button
                        size="sm"
                        className="px-4 font-semibold rounded-[10px]"
                      >
                        Add Tag
                      </Button>
                    )}
                  </TableCell>
                  <TableCell className="min-w-20 max-w-20 relative">
                    <div className="absolute right-0 -top-1.5 flex gap-2 items-center justify-end pr-3">
                      {transaction.isExcludedCashflow && (
                        <div>
                          <SvgExcludedCashFlow className="w-4" />
                          <span className="sr-only">
                            Excluded from cash flow
                          </span>
                        </div>
                      )}
                      {transaction.isSaved && (
                        <div>
                          <SvgSavedTransaction className="w-4 text-[#C4A4DC]" />
                          <span className="sr-only">Saved</span>
                        </div>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        ))}
      </Table>
      {!hasMore && (
        <div className="text-center text-muted-foreground text-md py-4">
          You've reached the end.
        </div>
      )}
    </>
  );
};

export { TransactionsTable };
