import SvgHdfc from "../icons/bank/hdfc";
import SvgAxis from "../icons/bank/axis";
import SvgSbi from "../icons/bank/sbi";
import SvgIcici from "../icons/bank/icici";
import { Landmark } from "lucide-react";
import {
  TransactionCard,
  TransactionGroup,
} from "../components/TransactionsTable";

// Format all currency values to INR by default.
export const currencyFormatter = new Intl.NumberFormat("en-IN", {
  style: "currency",
  currency: "INR",
});

export const displayBank = (bankName: string) => {
  switch (bankName.toLowerCase()) {
    case "hdfc":
      return SvgHdfc;
    case "axis":
      return SvgAxis;
    case "sbi":
      return SvgSbi;
    case "icici":
      return SvgIcici;
    default:
      return Landmark;
  }
};

// Merge newly fetched transactions into existing ones grouped by date
// Ensures seamless infinite scrolling while maintaining date-wise grouping
export function mergeTransactionGroups(
  oldGroups: TransactionGroup[],
  newGroups: TransactionGroup[],
): TransactionGroup[] {
  const mergedMap = new Map<number, TransactionCard[]>();

  for (const group of oldGroups) {
    const dateKey = new Date(group.date).setHours(0, 0, 0, 0);
    mergedMap.set(dateKey, group.transactions);
  }

  for (const group of newGroups) {
    const dateKey = new Date(group.date).setHours(0, 0, 0, 0);

    if (mergedMap.has(dateKey)) {
      const existing = mergedMap.get(dateKey)!;
      mergedMap.set(dateKey, [...existing, ...group.transactions]);
    } else {
      mergedMap.set(dateKey, group.transactions);
    }
  }

  return Array.from(mergedMap.entries())
    .sort((a, b) => b[0] - a[0])
    .map(([dateKey, transactions]) => ({
      date: new Date(dateKey),
      transactions,
    }));
}
