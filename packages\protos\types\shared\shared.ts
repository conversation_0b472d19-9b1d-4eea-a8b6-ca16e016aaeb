// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.2
//   protoc               v6.31.1
// source: shared/shared.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "shared";

export enum CalendarPeriod {
  /** CALENDAR_PERIOD_UNSPECIFIED - Undefined period, raises an error. */
  CALENDAR_PERIOD_UNSPECIFIED = 0,
  /** DAY - A day. */
  DAY = 1,
  /**
   * WEEK - A week. Weeks begin on Monday, following
   * [ISO 8601](https://en.wikipedia.org/wiki/ISO_week_date).
   */
  WEEK = 2,
  /**
   * FORTNIGHT - A fortnight. The first calendar fortnight of the year begins at the start
   * of week 1 according to
   * [ISO 8601](https://en.wikipedia.org/wiki/ISO_week_date).
   */
  FORTNIGHT = 3,
  /** MONTH - A month. */
  MONTH = 4,
  /**
   * QUARTER - A quarter. Quarters start on dates 1-Jan, 1-Apr, 1-Jul, and 1-Oct of each
   * year.
   */
  QUARTER = 5,
  /** HALF - A half-year. Half-years start on dates 1-Jan and 1-Jul. */
  HALF = 6,
  /** YEAR - A year. */
  YEAR = 7,
  UNRECOGNIZED = -1,
}

export function calendarPeriodFromJSON(object: any): CalendarPeriod {
  switch (object) {
    case 0:
    case "CALENDAR_PERIOD_UNSPECIFIED":
      return CalendarPeriod.CALENDAR_PERIOD_UNSPECIFIED;
    case 1:
    case "DAY":
      return CalendarPeriod.DAY;
    case 2:
    case "WEEK":
      return CalendarPeriod.WEEK;
    case 3:
    case "FORTNIGHT":
      return CalendarPeriod.FORTNIGHT;
    case 4:
    case "MONTH":
      return CalendarPeriod.MONTH;
    case 5:
    case "QUARTER":
      return CalendarPeriod.QUARTER;
    case 6:
    case "HALF":
      return CalendarPeriod.HALF;
    case 7:
    case "YEAR":
      return CalendarPeriod.YEAR;
    case -1:
    case "UNRECOGNIZED":
    default:
      return CalendarPeriod.UNRECOGNIZED;
  }
}

export function calendarPeriodToJSON(object: CalendarPeriod): string {
  switch (object) {
    case CalendarPeriod.CALENDAR_PERIOD_UNSPECIFIED:
      return "CALENDAR_PERIOD_UNSPECIFIED";
    case CalendarPeriod.DAY:
      return "DAY";
    case CalendarPeriod.WEEK:
      return "WEEK";
    case CalendarPeriod.FORTNIGHT:
      return "FORTNIGHT";
    case CalendarPeriod.MONTH:
      return "MONTH";
    case CalendarPeriod.QUARTER:
      return "QUARTER";
    case CalendarPeriod.HALF:
      return "HALF";
    case CalendarPeriod.YEAR:
      return "YEAR";
    case CalendarPeriod.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum DayOfWeek {
  /** DAY_OF_WEEK_UNSPECIFIED - The day of the week is unspecified. */
  DAY_OF_WEEK_UNSPECIFIED = 0,
  /** MONDAY - Monday */
  MONDAY = 1,
  /** TUESDAY - Tuesday */
  TUESDAY = 2,
  /** WEDNESDAY - Wednesday */
  WEDNESDAY = 3,
  /** THURSDAY - Thursday */
  THURSDAY = 4,
  /** FRIDAY - Friday */
  FRIDAY = 5,
  /** SATURDAY - Saturday */
  SATURDAY = 6,
  /** SUNDAY - Sunday */
  SUNDAY = 7,
  UNRECOGNIZED = -1,
}

export function dayOfWeekFromJSON(object: any): DayOfWeek {
  switch (object) {
    case 0:
    case "DAY_OF_WEEK_UNSPECIFIED":
      return DayOfWeek.DAY_OF_WEEK_UNSPECIFIED;
    case 1:
    case "MONDAY":
      return DayOfWeek.MONDAY;
    case 2:
    case "TUESDAY":
      return DayOfWeek.TUESDAY;
    case 3:
    case "WEDNESDAY":
      return DayOfWeek.WEDNESDAY;
    case 4:
    case "THURSDAY":
      return DayOfWeek.THURSDAY;
    case 5:
    case "FRIDAY":
      return DayOfWeek.FRIDAY;
    case 6:
    case "SATURDAY":
      return DayOfWeek.SATURDAY;
    case 7:
    case "SUNDAY":
      return DayOfWeek.SUNDAY;
    case -1:
    case "UNRECOGNIZED":
    default:
      return DayOfWeek.UNRECOGNIZED;
  }
}

export function dayOfWeekToJSON(object: DayOfWeek): string {
  switch (object) {
    case DayOfWeek.DAY_OF_WEEK_UNSPECIFIED:
      return "DAY_OF_WEEK_UNSPECIFIED";
    case DayOfWeek.MONDAY:
      return "MONDAY";
    case DayOfWeek.TUESDAY:
      return "TUESDAY";
    case DayOfWeek.WEDNESDAY:
      return "WEDNESDAY";
    case DayOfWeek.THURSDAY:
      return "THURSDAY";
    case DayOfWeek.FRIDAY:
      return "FRIDAY";
    case DayOfWeek.SATURDAY:
      return "SATURDAY";
    case DayOfWeek.SUNDAY:
      return "SUNDAY";
    case DayOfWeek.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface DateMessage {
  /**
   * Year of the date. Must be from 1 to 9999, or 0 to specify a date without
   * a year.
   */
  year: number;
  /**
   * Month of a year. Must be from 1 to 12, or 0 to specify a year without a
   * month and day.
   */
  month: number;
  /**
   * Day of a month. Must be from 1 to 31 and valid for the year and month, or 0
   * to specify a year by itself or a year and month where the day isn't
   * significant.
   */
  day: number;
}

function createBaseDateMessage(): DateMessage {
  return { year: 0, month: 0, day: 0 };
}

export const DateMessage: MessageFns<DateMessage> = {
  encode(message: DateMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.year !== 0) {
      writer.uint32(8).int32(message.year);
    }
    if (message.month !== 0) {
      writer.uint32(16).int32(message.month);
    }
    if (message.day !== 0) {
      writer.uint32(24).int32(message.day);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DateMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDateMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.year = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.month = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.day = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DateMessage {
    return {
      year: isSet(object.year) ? globalThis.Number(object.year) : 0,
      month: isSet(object.month) ? globalThis.Number(object.month) : 0,
      day: isSet(object.day) ? globalThis.Number(object.day) : 0,
    };
  },

  toJSON(message: DateMessage): unknown {
    const obj: any = {};
    if (message.year !== 0) {
      obj.year = Math.round(message.year);
    }
    if (message.month !== 0) {
      obj.month = Math.round(message.month);
    }
    if (message.day !== 0) {
      obj.day = Math.round(message.day);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DateMessage>, I>>(base?: I): DateMessage {
    return DateMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DateMessage>, I>>(object: I): DateMessage {
    const message = createBaseDateMessage();
    message.year = object.year ?? 0;
    message.month = object.month ?? 0;
    message.day = object.day ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
