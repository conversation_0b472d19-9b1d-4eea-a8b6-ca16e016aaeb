import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { Checkbox } from "@repo/ui";

const meta: Meta<typeof Checkbox> = {
  title: "Components/Checkbox",
  component: Checkbox,
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof Checkbox>;

// Checked Checkbox Story (Static)
export const Checked: Story = {
  args: {
    checked: true,
  },
};

// Unchecked Checkbox Story (Static)
export const Unchecked: Story = {
  args: {
    checked: false,
  },
};

// Interactive Checkbox Story
export const Interactive: Story = {};
