import * as React from "react";
import type { SVGProps } from "react";
const SvgInvestment = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="currentColor"
    viewBox="0 0 33 37"
    {...props}
  >
    <path d="M23.362.803c-2.19.025-3.813.63-4.855 1.799-.953 1.067-1.243 2.405-1.325 3.32 1.13-.896 3.106-2.051 5.92-2.26.348-.025.651.24.676.587a.63.63 0 0 1-.58.676c-3.17.233-5.152 1.843-5.878 2.55.89 1.225 1.964 1.944 3.207 2.16 2.633.454 5.29-1.484 5.575-1.699 2.31-2.273 4.949-2.588 6.445-2.55C29.757.398 23.634.784 23.362.803M12.473 9.634c1.243-.214 2.323-.94 3.207-2.159-.726-.707-2.709-2.316-5.878-2.55a.63.63 0 0 1-.58-.675.635.635 0 0 1 .675-.588c2.822.215 4.798 1.364 5.928 2.26-.089-.909-.379-2.253-1.332-3.32C13.45 1.434 11.829.828 9.676.802 9.366.779 3.243.4.453 5.387c1.496-.038 4.135.278 6.445 2.55.285.215 2.942 2.153 5.575 1.698M15.888 13.384c.202-.006.41-.013.612-.013.221 0 .436.007.65.013V9.211a7 7 0 0 1-.65-.732 7 7 0 0 1-.612.694z" />
    <path
      fillRule="evenodd"
      d="M16.5 14.634h.018c5.921.013 10.738 4.817 10.738 10.75 0 5.934-4.83 10.758-10.757 10.758-5.928 0-10.757-4.83-10.757-10.757 0-5.928 4.83-10.751 10.757-10.751m-4.255 12.1 5.265 4.072h2.531l-5.178-4.073h1.324c1.978 0 3.418-1.061 3.65-2.821h.917v-1.135h-.917a3.16 3.16 0 0 0-.756-1.687h1.673v-1.12h-8.509v1.527h3.985c.96 0 1.658.48 1.877 1.28h-5.862v1.135h5.862c-.219.8-.917 1.294-1.877 1.294h-3.985z"
      clipRule="evenodd"
    />
  </svg>
);
export default SvgInvestment;
